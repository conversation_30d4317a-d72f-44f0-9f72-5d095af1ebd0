{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/miniconda3/envs/pyarpesenv/lib/python3.8/site-packages/arpes/config.py:54: UserWarning: Could not find local configuration file. If you don't have one, you can safely ignore this message.\n", "  warnings.warn(msg)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Activating auto-logging. Current session state plus future input saved.\n", "Filename       : logs/unnamed_2024-07-31_14-22-29.log\n", "Mode           : backup\n", "Output logging : False\n", "Raw input log  : False\n", "Timestamping   : <PERSON><PERSON><PERSON>\n", "State          : active\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "cd34db7cc1c34bb28745ea08936c676e", "version_major": 2, "version_minor": 0}, "text/plain": ["interactive(children=(IntSlider(value=1, continuous_update=False, description='Scan Index', max=1, min=1), Flo…"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1855ab6cb9b54e9da04ae72b81770d4a", "version_major": 2, "version_minor": 0}, "image/png": "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", "text/html": ["\n", "            <div style=\"display: inline-block;\">\n", "                <div class=\"jupyter-widgets widget-label\" style=\"text-align: center;\">\n", "                    Figure\n", "                </div>\n", "                <img src='data:image/png;base64,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' width=1000.0/>\n", "            </div>\n", "        "], "text/plain": ["Canvas(toolbar=Toolbar(toolitems=[('Home', 'Reset original view', 'home', 'home'), ('Back', 'Back to previous …"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import numpy as np\n", "import xarray as xr\n", "import matplotlib.pyplot as plt\n", "from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons\n", "from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm\n", "from IPython.display import display, clear_output\n", "import tkinter as tk\n", "from tkinter import filedialog\n", "import warnings\n", "from arpes.load_pxt import *\n", "from arpes.io import *\n", "from arpes import *\n", "from arpes.utilities import *\n", "from matplotlib import MatplotlibDeprecationWarning\n", "from lmfit.models import GaussianModel, LinearModel\n", "from scipy.signal import find_peaks\n", "from skimage.feature import canny\n", "from scipy.ndimage import convolve\n", "\n", "# Set up matplotlib and suppress warnings\n", "%matplotlib widget\n", "if not hasattr(np, 'complex'):\n", "    np.complex = np.complex128\n", "plt.rcParams['font.family'] = 'sans-serif'\n", "plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana'] \n", "plt.rcParams.update({\n", "    \"text.usetex\": True,\n", "    \"font.family\": \"sans-serif\",\n", "    \"font.sans-serif\": \"Helvetica\",\n", "    \"text.color\": \"black\", \n", "    \"axes.labelcolor\": \"black\",\n", "})\n", "warnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n", "warnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n", "\n", "# Modules\n", "class ARPESModule:\n", "    def __init__(self):\n", "        self.enabled = True\n", "        \n", "    def enable(self):\n", "        self.enabled = True\n", "        \n", "    def disable(self):\n", "        self.enabled = False\n", "        \n", "    def plot(self, ax, data):\n", "        if self.enabled:\n", "            self._plot(ax, data)\n", "            \n", "    def _plot(self, ax, data):\n", "        raise NotImplementedError(\"Subclasses must implement _plot method\")\n", "\n", "        \n", "class ColorMapModule(ARPESModule):\n", "    def __init__(self):\n", "        super().__init__()\n", "        self.cmap = 'rainbow_light'\n", "        self.vmin = 0\n", "        self.vmax = None\n", "        \n", "    def set_cmap(self, cmap):\n", "        self.cmap = cmap\n", "        \n", "    def set_clim(self, vmin, vmax):\n", "        self.vmin = vmin\n", "        self.vmax = vmax\n", "        \n", "    def _plot(self, ax, data):\n", "        im = ax.pcolormesh(data['k_parallel'], data['energy_values'], data['data_values'], \n", "                           shading='auto', cmap=self.cmap, \n", "                           norm=Normalize(vmin=self.vmin, vmax=self.vmax))\n", "        return im\n", "        \n", "\n", "class EDCModule(ARPESModule):\n", "    def __init__(self):\n", "        super().__init__()\n", "        self.n = 5\n", "        self.vertical_offset = 0.5\n", "        self.show_edc = True\n", "        self.show_fit = False\n", "        self.num_peaks = 1\n", "        self.fit_type = 'Maxima'\n", "        self.fit_e_min = None\n", "        self.fit_e_max = None\n", "        \n", "    def set_params(self, n=5, vertical_offset=0.5, show_edc=True, show_fit=False, \n", "                   num_peaks=1, fit_type='Maxima', fit_e_min=None, fit_e_max=None):\n", "        self.n = n\n", "        self.vertical_offset = vertical_offset\n", "        self.show_edc = show_edc\n", "        self.show_fit = show_fit\n", "        self.num_peaks = num_peaks\n", "        self.fit_type = fit_type\n", "        self.fit_e_min = fit_e_min\n", "        self.fit_e_max = fit_e_max\n", "        \n", "    def _plot(self, ax, data):\n", "        k_indices = np.linspace(0, len(data['k_parallel']) - 1, self.n, dtype=int)\n", "        for i, k_index in enumerate(k_indices):\n", "            actual_k = data['k_parallel'][k_index]\n", "            kdc = data['data_values'][:, k_index]\n", "            if self.show_edc:\n", "                offset_kdc = kdc + i * self.vertical_offset\n", "                ax.plot(data['energy_values'], offset_kdc, label=fr'$k_\\parallel$ = {actual_k:.2f}')\n", "                ax.axhline(y=i * self.vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)\n", "            if self.show_fit:\n", "                self._plot_fit(ax, data['energy_values'], kdc, actual_k, i)\n", "                \n", "    def _plot_fit(self, ax, energy_values, kdc, actual_k, i):\n", "        # Fit data and plot the fit line\n", "        # (implementation similar to the original code)\n", "        pass\n", "\n", "        \n", "class FilterModule(ARPESModule):\n", "    def __init__(self):\n", "        super().__init__()\n", "        self.use_canny = False\n", "        self.sigma = 1.0\n", "        self.low_threshold = 0.1\n", "        self.high_threshold = 0.2\n", "        self.enable_averaging = False\n", "        self.averaging_kernel_size = 2\n", "        \n", "    def set_params(self, use_canny=False, sigma=1.0, low_threshold=0.1, high_threshold=0.2,\n", "                   enable_averaging=False, averaging_kernel_size=2):\n", "        self.use_canny = use_canny\n", "        self.sigma = sigma\n", "        self.low_threshold = low_threshold\n", "        self.high_threshold = high_threshold\n", "        self.enable_averaging = enable_averaging\n", "        self.averaging_kernel_size = averaging_kernel_size\n", "        \n", "    def _plot(self, ax, data):\n", "        data_to_plot = data['data_values'].copy()\n", "        if self.use_canny:\n", "            data_to_plot = canny(data_to_plot, sigma=self.sigma, \n", "                                 low_threshold=self.low_threshold, \n", "                                 high_threshold=self.high_threshold)\n", "        if self.enable_averaging:\n", "            kernel_size = max(3, self.averaging_kernel_size // 2 * 2 + 1)\n", "            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)\n", "            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')\n", "        data['data_values'] = data_to_plot\n", "        \n", "        \n", "# Main plotting function\n", "def plot_arpes(data_files, work_function, modules):\n", "    all_plots = []\n", "    global_ranges = {'k_min': float('inf'), 'k_max': float('-inf'), \n", "                     'e_min': float('inf'), 'e_max': float('-inf'),\n", "                     'intensity_max': float('-inf')}\n", "    \n", "    for file_path in data_files:\n", "        data = read_single_pxt(file_path)\n", "        E_photon = data.attrs['hv']\n", "        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19\n", "        k_parallel = (np.sqrt(-2 * 9.1093837015e-31 * E_b) * np.sin(np.radians(data.phi)) / 1.054571817e-34) / 10**10\n", "        energy_values = -data.eV.values\n", "        \n", "        all_plots.append({\n", "            'k_parallel': k_parallel,\n", "            'energy_values': energy_values,\n", "            'data_values': data.values,\n", "            'file_name': os.path.basename(file_path)\n", "        })\n", "        \n", "        global_ranges['k_min'] = min(global_ranges['k_min'], np.min(k_parallel))\n", "        global_ranges['k_max'] = max(global_ranges['k_max'], np.max(k_parallel))\n", "        global_ranges['e_min'] = min(global_ranges['e_min'], np.min(energy_values))\n", "        global_ranges['e_max'] = max(global_ranges['e_max'], np.max(energy_values))\n", "        global_ranges['intensity_max'] = max(global_ranges['intensity_max'], np.max(data.values))\n", "        \n", "    fig, ax = plt.subplots(figsize=(10, 8))\n", "    \n", "    def update_plot(scan_index, k_min, k_max, e_min, e_max):\n", "        plot_data = all_plots[scan_index - 1]\n", "        ax.clear()\n", "        \n", "        valid_k_indices = np.where((plot_data['k_parallel'] >= k_min) & (plot_data['k_parallel'] <= k_max))[0]\n", "        valid_e_indices = np.where((plot_data['energy_values'] >= e_min) & (plot_data['energy_values'] <= e_max))[0]\n", "        data_to_plot = {\n", "            'k_parallel': plot_data['k_parallel'][valid_k_indices],\n", "            'energy_values': plot_data['energy_values'][valid_e_indices],\n", "            'data_values': plot_data['data_values'][np.ix_(valid_e_indices, valid_k_indices)]\n", "        }\n", "        \n", "        for module in modules:\n", "            module.plot(ax, data_to_plot)\n", "            \n", "        ax.set_xlabel(r'$k_\\parallel $($\\AA^{-1}$)', fontsize=12, fontweight='bold')\n", "        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n", "        ax.set_title(f'ARPES Data - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n", "        ax.set_xlim(data_to_plot['k_parallel'].min(), data_to_plot['k_parallel'].max())\n", "        ax.set_ylim(data_to_plot['energy_values'].min(), data_to_plot['energy_values'].max())\n", "        ax.tick_params(axis='both', which='major', labelsize=10)\n", "        plt.tight_layout()\n", "        fig.canvas.draw_idle()\n", "        \n", "    interactive_plot = interactive(\n", "        update_plot,\n", "        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n", "        k_min=FloatSlider(value=global_ranges['k_min'], min=global_ranges['k_min'], max=global_ranges['k_max'], \n", "                          step=(global_ranges['k_max']-global_ranges['k_min'])/1000, description='k_min (Å⁻¹)', continuous_update=True),\n", "        k_max=FloatSlider(value=global_ranges['k_max'], min=global_ranges['k_min'], max=global_ranges['k_max'], \n", "                          step=(global_ranges['k_max']-global_ranges['k_min'])/1000, description='k_max (Å⁻¹)', continuous_update=True),\n", "        e_min=FloatSlider(value=global_ranges['e_min'], min=global_ranges['e_min'], max=global_ranges['e_max'], \n", "                          step=(global_ranges['e_max']-global_ranges['e_min'])/1000, description='E_min (eV)', continuous_update=True),\n", "        e_max=FloatSlider(value=global_ranges['e_max'], min=global_ranges['e_min'], max=global_ranges['e_max'], \n", "                          step=(global_ranges['e_max']-global_ranges['e_min'])/1000, description='E_max (eV)', continuous_update=True)\n", "    )\n", "    \n", "    return interactive_plot\n", "\n", "# Usage\n", "if __name__ == '__main__':\n", "    root = tk.Tk()\n", "    root.withdraw()\n", "    folder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\n", "    root.destroy()\n", "\n", "    if folder_path:\n", "        data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]\n", "        data_files.sort()\n", "        work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n", "        \n", "        # Create module instances\n", "        color_map_module = ColorMapModule()\n", "        edc_module = EDCModule()\n", "        filter_module = FilterModule()\n", "        \n", "        # Configure modules (optional)\n", "        color_map_module.set_cmap('rainbow_light')\n", "        color_map_module.set_clim(0, None)\n", "        edc_module.set_params(n=5, vertical_offset=0.5, show_edc=True, show_fit=False, num_peaks=1, \n", "                              fit_type='Maxima', fit_e_min=None, fit_e_max=None)\n", "        filter_module.set_params(use_canny=False, sigma=1.0, low_threshold=0.1, high_threshold=0.2,\n", "                                 enable_averaging=False, averaging_kernel_size=2)\n", "        \n", "        # Create a list of enabled modules\n", "        modules = [color_map_module, edc_module, filter_module]\n", "        \n", "        interactive_plot = plot_arpes(data_files, work_function, modules)\n", "        display(interactive_plot)\n", "    else:\n", "        print(\"No folder selected.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 2}