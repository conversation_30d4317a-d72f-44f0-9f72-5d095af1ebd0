#!/usr/bin/env python3
"""
Simple launcher for the ARPES Analysis GUI
This script checks dependencies and launches the main application.
"""

import sys
import subprocess
import importlib

def check_package(package_name, import_name=None):
    """Check if a package is installed"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Main launcher function"""
    print("🔬 ARPES Analysis GUI Launcher")
    print("=" * 40)
    
    # Required packages
    required_packages = [
        ("plotly", "plotly"),
        ("pandas", "pandas"),
        ("numpy", "numpy"),
        ("scipy", "scipy"),
        ("scikit-image", "skimage"),
        ("xarray", "xarray")
    ]
    
    missing_packages = []
    
    # Check for missing packages
    print("📋 Checking dependencies...")
    for package_name, import_name in required_packages:
        if check_package(package_name, import_name):
            print(f"✅ {package_name}")
        else:
            print(f"❌ {package_name} - MISSING")
            missing_packages.append(package_name)
    
    # Install missing packages
    if missing_packages:
        print(f"\n📦 Installing {len(missing_packages)} missing packages...")
        for package in missing_packages:
            print(f"Installing {package}...")
            if install_package(package):
                print(f"✅ {package} installed successfully")
            else:
                print(f"❌ Failed to install {package}")
                print("Please install manually with:")
                print(f"   pip install {package}")
                return False
    
    print("\n🚀 All dependencies satisfied!")
    print("🔬 Launching ARPES Analysis GUI...")
    
    # Import and run the main application
    try:
        from arpes_analysis_gui import main as run_gui
        run_gui()
    except ImportError as e:
        print(f"❌ Error importing GUI: {e}")
        print("Make sure arpes_analysis_gui.py is in the same directory")
        return False
    except Exception as e:
        print(f"❌ Error running GUI: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        input("\nPress Enter to exit...")
        sys.exit(1)
