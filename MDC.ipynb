{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "import xarray as xr\n", "import matplotlib.pyplot as plt\n", "from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox\n", "from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm\n", "from IPython.display import display, clear_output\n", "import tkinter as tk\n", "from tkinter import filedialog\n", "import warnings\n", "from arpes.load_pxt import *\n", "from arpes.io import *\n", "from arpes import *\n", "from arpes.utilities import *\n", "from matplotlib import MatplotlibDeprecationWarning\n", "from lmfit.models import LorentzianModel, LinearModel\n", "from scipy.signal import find_peaks\n", "from skimage.feature import canny\n", "from scipy.ndimage import convolve\n", "\n", "%matplotlib widget\n", "\n", "if not hasattr(np, 'complex'):\n", "    np.complex = np.complex128\n", "\n", "# Set the font family for all text elements\n", "plt.rcParams['font.family'] = 'sans-serif'\n", "plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\n", "plt.rcParams.update({\n", "    \"text.usetex\": True,\n", "    \"font.family\": \"sans-serif\",\n", "    \"font.sans-serif\": \"Helvetica\",\n", "    \"text.color\": \"black\",\n", "    \"axes.labelcolor\": \"black\",\n", "})\n", "\n", "def load_data_files(folder_path):\n", "    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n", "    data_files.sort()\n", "    return [os.path.join(folder_path, f) for f in data_files]\n", "\n", "warnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n", "warnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n", "\n", "def edc_plot(data_files, work_function):\n", "    # Constants\n", "    hbar = 1.054571817e-34  # J*s\n", "    m_e = 9.1093837015e-31  # kg\n", "\n", "    # Pre-calculate all plots\n", "    all_plots = []\n", "    global_k_min = float('inf')\n", "    global_k_max = float('-inf')\n", "    global_e_min = float('inf')\n", "    global_e_max = float('-inf')\n", "    global_intensity_max = float('-inf')\n", "\n", "    for file_path in data_files:\n", "        data = read_single_pxt(file_path)\n", "        E_photon = data.attrs['hv']\n", "\n", "        # Calculate kinetic energy and momentum\n", "        E_b = (work_function + np.abs(data.eV) - E_photon)*1.602176634e-19\n", "        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar)/10**10\n", "        # Get the energy values\n", "        energy_values = -data.eV.values\n", "\n", "        all_plots.append({\n", "            'k_parallel': k_parallel,\n", "            'energy_values': energy_values,\n", "            'data_values': data.values,\n", "            'file_name': os.path.basename(file_path)\n", "        })\n", "\n", "        # Update global ranges\n", "        global_k_min = min(global_k_min, np.min(k_parallel))\n", "        global_k_max = max(global_k_max, np.max(k_parallel))\n", "        global_e_min = min(global_e_min, np.min(energy_values))\n", "        global_e_max = max(global_e_max, np.max(energy_values))\n", "        global_intensity_max = max(global_intensity_max, np.max(data.values))\n", "\n", "    global_k_range = global_k_max - global_k_min\n", "    global_e_range = global_e_max - global_e_min\n", "\n", "    fig, ax = plt.subplots(figsize=(10, 8))\n", "\n", "    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size):\n", "        plot_data = all_plots[scan_index - 1]\n", "        ax.clear()\n", "        \n", "        data_to_plot = plot_data['data_values'].copy()\n", "        \n", "        if use_canny:\n", "            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)\n", "            data_to_plot = edges.astype(float)\n", "        \n", "        if enable_averaging:\n", "            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)\n", "            data_to_plot = convolve(data_to_plot, averaging_kernel)\n", "\n", "        # Calculate equally spaced energy indices within the specified range\n", "        energy_values = plot_data['energy_values']\n", "        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]\n", "        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)\n", "        e_indices = valid_indices[e_indices]\n", "\n", "        k_parallel = plot_data['k_parallel'][0]\n", "        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]\n", "        k_parallel = k_parallel[valid_k_indices]\n", "\n", "        max_intensity = float('-inf')\n", "        min_intensity = float('inf')\n", "\n", "        for i, e_index in enumerate(e_indices):\n", "            actual_e = energy_values[e_index]\n", "            kdc = data_to_plot[e_index, valid_k_indices]\n", "\n", "            # Convert to numpy arrays if they are xarray.DataArray\n", "            if isinstance(kdc, xr.<PERSON>y):\n", "                kdc = kdc.values\n", "            if isinstance(k_parallel, xr.<PERSON>y):\n", "                k_parallel = k_parallel.values\n", "\n", "            # Normalize the curve if edge detection is off\n", "            if not use_canny:\n", "                kdc = kdc / np.max(kdc)\n", "\n", "            # Apply vertical offset\n", "            offset_kdc = kdc + i * vertical_offset\n", "            max_intensity = max(max_intensity, np.max(offset_kdc))\n", "            min_intensity = min(min_intensity, np.min(offset_kdc))\n", "\n", "            if show_edc:\n", "                ax.plot(k_parallel, offset_kdc, label=f'E = {actual_e:.2f} eV')\n", "\n", "            # Draw x'd line at the \"zero level\" of each curve\n", "            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)\n", "\n", "            if show_fit:\n", "                # Fit KDC with multiple Lorentzian peaks + Linear background\n", "                peaks, _ = find_peaks(kdc)\n", "                peak_heights = kdc[peaks]\n", "                sorted_indices = np.argsort(peak_heights)[-num_peaks:]\n", "                largest_peaks = peaks[sorted_indices]\n", "\n", "                model = LinearModel(prefix='bkg_')\n", "                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))\n", "\n", "                for j, peak in enumerate(largest_peaks):\n", "                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')\n", "                    model += lorentzian\n", "                    params.update(lorentzian.make_params())\n", "                    params[f'l{j+1}_center'].set(value=k_parallel[peak], min=k_parallel.min(), max=k_parallel.max())\n", "                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n", "                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)\n", "\n", "                result = model.fit(kdc, params, x=k_parallel)\n", "                fit = result.best_fit\n", "                offset_fit = fit + i * vertical_offset\n", "\n", "                ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')\n", "\n", "                # Find local maxima and minima on the fitted curve\n", "                fit_peaks, _ = find_peaks(fit)\n", "                fit_valleys, _ = find_peaks(-fit)\n", "\n", "                # Plot local maxima and minima\n", "                ax.plot(k_parallel[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)\n", "                ax.plot(k_parallel[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)\n", "\n", "        ax.set_xlabel(r'$k_\\parallel $($\\AA^{-1}$)', fontsize=12, fontweight='bold')\n", "        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')\n", "        ax.set_title(f'MDCs - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n", "        ax.legend()\n", "        ax.tick_params(axis='both', which='major', labelsize=10)\n", "\n", "        # Set axis limits based on sliders\n", "        ax.set_xlim(k_min, k_max)\n", "\n", "        # Ensure the entire curve is visible, including negative values\n", "        y_range = max_intensity - min_intensity\n", "        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)\n", "\n", "        plt.tight_layout()\n", "        fig.canvas.draw_idle()\n", "\n", "        return max_intensity - min_intensity\n", "\n", "    def save_plot(b):\n", "        current_values = {\n", "            'scan_index': interactive_plot.children[0].value,\n", "            'n': interactive_plot.children[1].value,\n", "            'vertical_offset': interactive_plot.children[2].value,\n", "            'show_edc': interactive_plot.children[3].value,\n", "            'show_fit': interactive_plot.children[4].value,\n", "            'num_peaks': interactive_plot.children[5].value,\n", "            'k_min': interactive_plot.children[6].value,\n", "            'k_max': interactive_plot.children[7].value,\n", "            'e_min': interactive_plot.children[8].value,\n", "            'e_max': interactive_plot.children[9].value,\n", "            'use_canny': interactive_plot.children[10].value,\n", "            'sigma': interactive_plot.children[11].value,\n", "            'low_threshold': interactive_plot.children[12].value,\n", "            'high_threshold': interactive_plot.children[13].value,\n", "            'enable_averaging': interactive_plot.children[14].value,\n", "            'averaging_kernel_size': interactive_plot.children[15].value\n", "        }\n", "        plot_edc(**current_values)\n", "        filename = f\"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png\"\n", "        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n", "        print(f\"Plot saved as {filename}\")\n", "\n", "    interactive_plot = interactive(plot_edc,\n", "        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n", "        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),\n", "        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),\n", "        show_edc=Checkbox(value=True, description='Show MDCs'),\n", "        show_fit=Checkbox(value=False, description='Show Fits'),\n", "        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),\n", "        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min ()', continuous_update=True),\n", "        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max ()', continuous_update=True),\n", "        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),\n", "        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),\n", "        use_canny=Checkbox(value=False, description='Use Canny Filter'),\n", "        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),\n", "        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),\n", "        high_threshold=FloatSlider(value=0.2, min=0.0, max=10.0, step=0.05, description='High Threshold', continuous_update=False),\n", "        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),\n", "        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)\n", "    )\n", "\n", "    save_button = Button(description=\"Save Plot\")\n", "    save_button.on_click(save_plot)\n", "\n", "    output = VBox([interactive_plot, save_button])\n", "    return output\n", "\n", "# Usage\n", "root = tk.Tk()\n", "root.withdraw()\n", "folder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\n", "root.destroy()\n", "\n", "if folder_path:\n", "    data_files = load_data_files(folder_path)\n", "    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n", "    interactive_plot_with_save = edc_plot(data_files, work_function)\n", "    display(interactive_plot_with_save)\n", "else:\n", "    print(\"No folder selected.\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}