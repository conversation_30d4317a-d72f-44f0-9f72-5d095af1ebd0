{"cells": [{"cell_type": "markdown", "id": "intro", "metadata": {}, "source": ["# Enhanced ARPES Thesis Analysis Notebook\n", "\n", "This notebook provides an enhanced interactive interface for ARPES data analysis using Plotly for modern, interactive visualizations.\n", "\n", "## Features:\n", "- Interactive Plotly-based plotting\n", "- Enhanced GUI with organized controls\n", "- Multiple visualization modes (2D, 3D, contour)\n", "- Peak detection and analysis\n", "- Export functionality\n", "- Better error handling and user feedback"]}, {"cell_type": "code", "execution_count": null, "id": "imports", "metadata": {}, "outputs": [], "source": ["# Enhanced imports for modern ARPES analysis\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "import xarray as xr\n", "import plotly.graph_objects as go\n", "import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "import plotly.colors as pcolors\n", "from ipywidgets import (\n", "    interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, \n", "    HBox, Tab, Accordion, HTML, Dropdown, Text, Output\n", ")\n", "from IPython.display import display, clear_output\n", "import tkinter as tk\n", "from tkinter import filedialog, messagebox\n", "from arpes.load_pxt import read_single_pxt\n", "import warnings\n", "from skimage.feature import canny\n", "from scipy.interpolate import griddata\n", "from scipy.ndimage import gaussian_filter, maximum_filter\n", "from skimage.measure import label, euler_number\n", "from scipy.spatial import cKDTree\n", "\n", "# Suppress warnings for cleaner output\n", "warnings.filterwarnings(\"ignore\", category=UserWarning)\n", "if not hasattr(np, 'complex'):\n", "    np.complex = np.complex128\n", "\n", "print(\"✓ All libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": null, "id": "colormap_setup", "metadata": {}, "outputs": [], "source": ["# Custom colormap setup for Plotly\n", "igor_data = np.array([\n", "    [57600, 54784, 58112],\n", "    [56561.95, 53415.66, 57121.13],\n", "    [55523.89, 52047.31, 56130.26],\n", "    [54485.84, 50678.96, 55139.39],\n", "    [53447.78, 49310.62, 54148.52],\n", "    [52409.73, 47942.27, 53157.65],\n", "    [51371.67, 46655.25, 52193.88],\n", "    [50333.62, 45428.45, 51250.2],\n", "    [49295.56, 44201.66, 50306.51],\n", "    [48257.51, 42974.87, 49362.82],\n", "    [47219.45, 41748.08, 48419.14],\n", "    [46223.56, 40563.45, 47510.59],\n", "    [45468.61, 39619.77, 46802.82],\n", "    [44713.66, 38676.08, 46095.06],\n", "    [43958.71, 37732.39, 45387.29],\n", "    [43203.77, 36788.71, 44679.53],\n", "    [42448.82, 35845.02, 43971.77],\n", "    [41693.87, 34833.07, 43195.73],\n", "    [40938.92, 33795.01, 42393.6],\n", "    [40183.97, 32756.96, 41591.46],\n", "    [39429.02, 31718.9, 40789.33],\n", "    [38674.07, 30680.85, 39987.2],\n", "    [37919.12, 29670.9, 39171.01],\n", "    [37164.17, 28727.21, 38321.7],\n", "    [36409.22, 27783.53, 37472.38],\n", "    [35654.27, 26839.84, 36623.06],\n", "    [34899.32, 25896.16, 35773.74],\n", "    [34144.38, 24952.47, 34924.42],\n", "    [33512.91, 24173.43, 34239.75],\n", "    [32899.52, 23418.48, 33579.17],\n", "    [32286.12, 22663.53, 32918.59],\n", "    [31672.72, 21908.58, 32258.01],\n", "    [31059.33, 21153.63, 31597.43],\n", "    [30467.01, 20419.77, 30957.93],\n", "    [29900.8, 19712, 30344.53],\n", "    [29334.59, 19004.23, 29731.14],\n", "    [28768.38, 18296.47, 29117.74],\n", "    [28202.16, 17588.71, 28504.35],\n", "    [27641.98, 16886.96, 27901.99],\n", "    [27358.87, 16462.31, 27807.62],\n", "    [27075.77, 16037.65, 27713.26],\n", "    [26792.66, 15612.99, 27618.89],\n", "    [26509.55, 15188.33, 27524.52],\n", "    [26226.45, 14763.67, 27430.15],\n", "    [26027.67, 14479.56, 27448.22],\n", "    [25886.12, 14290.82, 27542.59],\n", "    [25744.56, 14102.09, 27636.96],\n", "    [25603.01, 13913.35, 27731.33],\n", "    [25461.46, 13724.61, 27825.69],\n", "    [25279.75, 13503.75, 27944.16],\n", "    [24902.28, 13126.27, 28180.08],\n", "    [24524.8, 12748.8, 28416],\n", "    [24147.33, 12371.33, 28651.92],\n", "    [23769.85, 11993.85, 28887.84],\n", "    [23392.38, 11616.38, 29123.77],\n", "    [22874.35, 11168.63, 29359.69],\n", "    [22308.14, 10696.78, 29595.61],\n", "    [21741.93, 10224.94, 29831.53],\n", "    [21175.72, 9753.098, 30067.45],\n", "    [20609.51, 9281.255, 30303.37],\n", "    [19952.94, 8899.765, 30539.29],\n", "    [19103.62, 8711.027, 30775.21],\n", "    [18254.31, 8522.29, 31011.14],\n", "    [17404.99, 8333.553, 31247.06],\n", "    [16555.67, 8144.816, 31482.98],\n", "    [15706.35, 7956.079, 31718.9],\n", "    [14688.38, 7893.835, 31828.33],\n", "    [13650.32, 7846.651, 31922.7],\n", "    [12612.27, 7799.467, 32017.07],\n", "    [11574.21, 7752.282, 32111.44],\n", "    [10536.16, 7705.098, 32205.8],\n", "    [9807.31, 7922.949, 32388.52],\n", "    [9429.835, 8441.977, 32671.62],\n", "    [9052.36, 8961.004, 32954.73],\n", "    [8674.887, 9480.031, 33237.84],\n", "    [8297.412, 9999.059, 33520.94],\n", "    [7911.906, 10526.12, 33812.08],\n", "    [7345.694, 11233.88, 34283.92],\n", "    [6779.482, 11941.65, 34755.77],\n", "    [6213.271, 12649.41, 35227.61],\n", "    [5647.059, 13357.18, 35699.45],\n", "    [5080.847, 14064.94, 36171.29],\n", "    [4543.749, 14714.48, 36614.02],\n", "    [4024.722, 15327.87, 37038.68],\n", "    [3505.694, 15941.27, 37463.34],\n", "    [2986.667, 16554.67, 37888],\n", "    [2467.639, 17168.06, 38312.66],\n", "    [1984.753, 17790.49, 38764.42],\n", "    [1654.463, 18451.07, 39330.64],\n", "    [1324.173, 19111.65, 39896.85],\n", "    [993.8823, 19772.23, 40463.06],\n", "    [663.5922, 20432.82, 41029.27],\n", "    [333.302, 21093.4, 41595.48],\n", "    [256, 21464.85, 41944.85],\n", "    [256, 21747.95, 42227.95],\n", "    [256, 22031.06, 42511.06],\n", "    [256, 22314.16, 42794.16],\n", "    [256, 22597.27, 43077.27],\n", "    [239.9373, 23008.88, 43456.75],\n", "    [192.7529, 23669.46, 44022.96],\n", "    [145.5686, 24330.04, 44589.18],\n", "    [98.38432, 24990.62, 45155.39],\n", "    [51.2, 25651.2, 45721.6],\n", "    [4.015687, 26311.78, 46287.81],\n", "    [0, 26972.36, 46897.19],\n", "    [0, 27632.94, 47510.59],\n", "    [0, 28293.52, 48123.98],\n", "    [0, 28954.1, 48737.38],\n", "    [0, 29614.68, 49350.78],\n", "    [0, 30344.53, 50033.44],\n", "    [0, 31146.67, 50788.39],\n", "    [0, 31948.8, 51543.34],\n", "    [0, 32750.93, 52298.29],\n", "    [0, 33553.07, 53053.24],\n", "    [0, 34358.21, 53805.18],\n", "    [0, 35207.53, 54512.94],\n", "    [0, 36056.85, 55220.71],\n", "    [0, 36906.16, 55928.47],\n", "    [0, 37755.48, 56636.23],\n", "    [0, 38604.8, 57344],\n", "    [0, 39062.59, 57208.47],\n", "    [0, 39298.51, 56595.07],\n", "    [0, 39534.43, 55981.68],\n", "    [0, 39770.35, 55368.28],\n", "    [0, 40006.27, 54754.89],\n", "    [0, 40181.96, 54041.1],\n", "    [0, 40134.78, 52955.86],\n", "    [0, 40087.59, 51870.62],\n", "    [0, 40040.41, 50785.38],\n", "    [0, 39993.22, 49700.14],\n", "    [0, 39946.04, 48614.9],\n", "    [0, 39936, 47641.1],\n", "    [0, 39936, 46697.41],\n", "    [0, 39936, 45753.73],\n", "    [0, 39936, 44810.04],\n", "    [0, 39936, 43866.35],\n", "    [0, 39918.93, 42854.4],\n", "    [0, 39871.75, 41721.98],\n", "    [0, 39824.57, 40589.55],\n", "    [0, 39777.38, 39457.13],\n", "    [0, 39730.2, 38324.71],\n", "    [0, 39683.01, 37192.28],\n", "    [0, 39680, 36369.07],\n", "    [0, 39680, 35566.93],\n", "    [0, 39680, 34764.8],\n", "    [0, 39680, 33962.67],\n", "    [0, 39680, 33160.54],\n", "    [0, 39680, 32527.06],\n", "    [0, 39680, 32055.21],\n", "    [0, 39680, 31583.37],\n", "    [0, 39680, 31111.53],\n", "    [0, 39680, 30639.69],\n", "    [0, 39675.98, 30123.67],\n", "    [0, 39628.8, 29132.8],\n", "    [0, 39581.62, 28141.93],\n", "    [0, 39534.43, 27151.06],\n", "    [0, 39487.25, 26160.19],\n", "    [0, 39440.06, 25169.32],\n", "    [0, 39361.76, 24240.69],\n", "    [0, 39267.39, 23344.19],\n", "    [0, 39173.02, 22447.69],\n", "    [0, 39078.65, 21551.18],\n", "    [0, 38984.28, 20654.68],\n", "    [0, 38923.04, 19835.48],\n", "    [0, 38970.23, 19269.27],\n", "    [0, 39017.41, 18703.06],\n", "    [0, 39064.6, 18136.85],\n", "    [0, 39111.78, 17570.63],\n", "    [0, 39158.96, 17004.42],\n", "    [0, 39435.04, 16781.55],\n", "    [0, 39765.33, 16640],\n", "    [0, 40095.62, 16498.45],\n", "    [0, 40425.91, 16356.89],\n", "    [0, 40756.2, 16215.34],\n", "    [993.8823, 41122.64, 16073.79],\n", "    [3589.02, 41547.29, 15932.24],\n", "    [6184.157, 41971.95, 15790.68],\n", "    [8779.294, 42396.61, 15649.13],\n", "    [11374.43, 42821.27, 15507.58],\n", "    [13969.57, 43245.93, 15366.02],\n", "    [15796.71, 43715.77, 15224.47],\n", "    [17589.71, 44187.61, 15082.92],\n", "    [19382.71, 44659.45, 14941.36],\n", "    [21175.72, 45131.29, 14799.81],\n", "    [22968.72, 45603.14, 14658.26],\n", "    [24686.43, 46100.08, 14516.71],\n", "    [26337.88, 46619.11, 14375.15],\n", "    [27989.33, 47138.13, 14233.6],\n", "    [29640.79, 47657.16, 14092.05],\n", "    [31292.23, 48176.19, 13950.49],\n", "    [32933.65, 48705.25, 13798.9],\n", "    [34490.73, 49318.65, 13562.98],\n", "    [36047.81, 49932.05, 13327.06],\n", "    [37604.89, 50545.44, 13091.14],\n", "    [39161.98, 51158.84, 12855.22],\n", "    [40719.06, 51772.23, 12619.29],\n", "    [41922.76, 52225, 12415.5],\n", "    [42960.82, 52602.48, 12226.76],\n", "    [43998.87, 52979.95, 12038.02],\n", "    [45036.93, 53357.43, 11849.29],\n", "    [46074.98, 53734.9, 11660.55],\n", "    [47293.74, 54196.71, 11411.58],\n", "    [49039.56, 54904.47, 10986.92],\n", "    [50785.38, 55612.23, 10562.26],\n", "    [52531.2, 56320, 10137.6],\n", "    [54277.02, 57027.77, 9712.941],\n", "    [56022.84, 57735.53, 9288.282],\n", "    [57494.59, 58325.84, 8785.317],\n", "    [58910.12, 58892.05, 8266.29],\n", "    [60325.65, 59458.26, 7747.263],\n", "    [61741.18, 60024.47, 7228.235],\n", "    [63156.71, 60590.68, 6709.208],\n", "    [64076.3, 60470.21, 6457.224],\n", "    [64265.04, 59337.79, 6598.776],\n", "    [64453.77, 58205.36, 6740.33],\n", "    [64642.51, 57072.94, 6881.882],\n", "    [64831.25, 55940.52, 7023.435],\n", "    [65019.98, 54808.09, 7164.988],\n", "    [64746.92, 53260.05, 7398.902],\n", "    [64463.81, 51702.96, 7634.824],\n", "    [64180.71, 50145.88, 7870.745],\n", "    [63897.6, 48588.8, 8106.667],\n", "    [63614.49, 47031.72, 8342.588],\n", "    [63592.41, 45605.14, 8474.102],\n", "    [63781.14, 44283.98, 8521.286],\n", "    [63969.88, 42962.82, 8568.471],\n", "    [64158.62, 41641.66, 8615.655],\n", "    [64347.36, 40320.5, 8662.839],\n", "    [64415.62, 38993.32, 8704],\n", "    [63660.68, 37624.97, 8704],\n", "    [62905.73, 36256.63, 8704],\n", "    [62150.78, 34888.28, 8704],\n", "    [61395.83, 33519.94, 8704],\n", "    [60640.88, 32151.59, 8704],\n", "    [60283.48, 30882.63, 8704],\n", "    [60094.75, 29655.84, 8704],\n", "    [59906.01, 28429.05, 8704],\n", "    [59717.27, 27202.26, 8704],\n", "    [59528.54, 25975.47, 8704],\n", "    [59339.8, 24722.57, 8704],\n", "    [59151.06, 23401.41, 8704],\n", "    [58962.32, 22080.25, 8704],\n", "    [58773.59, 20759.09, 8704],\n", "    [58584.85, 19437.93, 8704],\n", "    [58396.11, 18116.77, 8704],\n", "    [58287.69, 17197.18, 8704],\n", "    [58193.32, 16347.86, 8704],\n", "    [58098.95, 15498.54, 8704],\n", "    [58004.58, 14649.22, 8704],\n", "    [57910.21, 13799.91, 8704],\n", "    [57795.77, 12267.92, 8704],\n", "    [57654.21, 9814.337, 8704],\n", "    [57512.66, 7360.753, 8704],\n", "    [57371.11, 4907.168, 8704],\n", "    [57229.55, 2453.584, 8704],\n", "    [57088, 0, 8704]\n", "])\n", "\n", "# Create custom colorscale for Plotly\n", "def create_custom_colorscale(rgb_array):\n", "    \"\"\"Convert RGB array to Plotly colorscale format\"\"\"\n", "    n_colors = len(rgb_array)\n", "    colorscale = []\n", "    for i, rgb in enumerate(rgb_array):\n", "        position = i / (n_colors - 1)\n", "        color = f'rgb({int(rgb[0]/255)}, {int(rgb[1]/255)}, {int(rgb[2]/255)})'\n", "        colorscale.append([position, color])\n", "    return colorscale\n", "\n", "# Normalize and create colorscale\n", "normalized_data = igor_data / 65535.0\n", "rainbowlightct = create_custom_colorscale(igor_data)\n", "\n", "print(\"✓ Custom colormap created successfully!\")\n", "\n", "# Additional built-in colorscales for variety\n", "COLORSCALES = {\n", "    'Custom Rainbow': rainbowlightct,\n", "    'Viridis': 'Viridis',\n", "    'Plasma': 'Plasma',\n", "    'Inferno': 'Inferno',\n", "    'Magma': 'Magma',\n", "    'Cividis': 'Cividis',\n", "    'Turbo': 'Turbo'\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "data_loading", "metadata": {}, "outputs": [], "source": ["# Enhanced Data Loading Class\n", "class ARPESDataLoader:\n", "    def __init__(self):\n", "        self.data = None\n", "        self.data_attributes = None\n", "        self.data_proc = None\n", "        self.work_function = 4.5  # eV\n", "        self.status_output = Output()\n", "        \n", "    def load_pxt_files(self):\n", "        \"\"\"Enhanced PXT file loader with progress tracking\"\"\"\n", "        with self.status_output:\n", "            clear_output(wait=True)\n", "            print(\"🔍 Opening file dialog...\")\n", "        \n", "        # Use tkinter for file dialog\n", "        root = tk.Tk()\n", "        root.withdraw()  # Hide the main window\n", "        \n", "        folder_path = filedialog.askdirectory(\n", "            title=\"Select folder containing PXT files\",\n", "            initialdir=os.getcwd()\n", "        )\n", "        root.destroy()\n", "        \n", "        if not folder_path:\n", "            with self.status_output:\n", "                clear_output(wait=True)\n", "                print(\"❌ No folder selected.\")\n", "            return False\n", "\n", "        pxt_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n", "        if not pxt_files:\n", "            with self.status_output:\n", "                clear_output(wait=True)\n", "                print(\"❌ No PXT files found in the selected folder.\")\n", "            return False\n", "\n", "        # Sort files naturally\n", "        pxt_files.sort()\n", "        \n", "        with self.status_output:\n", "            clear_output(wait=True)\n", "            print(f\"📁 Found {len(pxt_files)} PXT files\")\n", "            print(\"⏳ Loading files...\")\n", "\n", "        data_arrays = []\n", "        attributes = []\n", "        failed_files = []\n", "        \n", "        for i, file in enumerate(pxt_files):\n", "            file_path = os.path.join(folder_path, file)\n", "            try:\n", "                data = read_single_pxt(file_path)\n", "                df = pd.DataFrame(\n", "                    data.values, \n", "                    columns=data.coords['phi'].values, \n", "                    index=data.coords['eV'].values\n", "                )\n", "                data_arrays.append(df)\n", "                attributes.append(data.attrs)\n", "                \n", "                # Update progress\n", "                if (i + 1) % 10 == 0 or i == len(pxt_files) - 1:\n", "                    with self.status_output:\n", "                        clear_output(wait=True)\n", "                        progress = (i + 1) / len(pxt_files) * 100\n", "                        print(f\"📁 Found {len(pxt_files)} PXT files\")\n", "                        print(f\"⏳ Loading files... {progress:.1f}% ({i+1}/{len(pxt_files)})\")\n", "                        \n", "            except Exception as e:\n", "                failed_files.append((file, str(e)))\n", "\n", "        # Store data\n", "        self.data = data_arrays\n", "        self.data_attributes = attributes\n", "        \n", "        # Process data for binding energy\n", "        self._process_data()\n", "        \n", "        # Show final status\n", "        with self.status_output:\n", "            clear_output(wait=True)\n", "            success_msg = f\"✅ Successfully loaded {len(data_arrays)} PXT files\"\n", "            print(success_msg)\n", "            \n", "            if failed_files:\n", "                print(f\"⚠️  Failed to load {len(failed_files)} files:\")\n", "                for file, error in failed_files[:3]:  # Show first 3 failures\n", "                    print(f\"   - {file}: {error[:50]}...\")\n", "                if len(failed_files) > 3:\n", "                    print(f\"   ... and {len(failed_files) - 3} more.\")\n", "            \n", "            print(f\"📊 Data shape: {len(self.data)} scans\")\n", "            if self.data:\n", "                print(f\"📊 Each scan: {self.data[0].shape[0]} energies × {self.data[0].shape[1]} angles\")\n", "        \n", "        return True\n", "    \n", "    def _process_data(self):\n", "        \"\"\"Process data to convert to binding energy\"\"\"\n", "        if not self.data or not self.data_attributes:\n", "            return\n", "            \n", "        self.data_proc = [df.copy() for df in self.data]\n", "        \n", "        for i in range(len(self.data_proc)):\n", "            hv = self.data_attributes[i]['hv']\n", "            # Convert kinetic energy to binding energy\n", "            new_index = [hv - self.work_function - abs(idx) for idx in self.data[i].index]\n", "            self.data_proc[i] = self.data_proc[i].set_index(pd.Index(new_index))\n", "            \n", "            # Add polar angle to dataframe attributes\n", "            self.data_proc[i].attrs = {'polar': self.data_attributes[i]['polar']}\n", "    \n", "    def get_binding_energy_range(self):\n", "        \"\"\"Get the full binding energy range across all data\"\"\"\n", "        if not self.data_proc:\n", "            return 0, 1\n", "            \n", "        E_binding_values = []\n", "        for i, df in enumerate(self.data_proc):\n", "            hv = self.data_attributes[i]['hv']\n", "            E_kinetic_values = df.index.values.astype(float)\n", "            E_binding_scan = hv - self.work_function - E_kinetic_values\n", "            E_binding_values.extend(E_binding_scan)\n", "        \n", "        E_binding_array = np.array(E_binding_values)\n", "        return E_binding_array.min(), E_binding_array.max()\n", "\n", "# Create global data loader instance\n", "data_loader = ARPESDataLoader()\n", "\n", "print(\"✓ Data loader initialized!\")"]}, {"cell_type": "code", "execution_count": null, "id": "utility_functions", "metadata": {}, "outputs": [], "source": ["# Utility Functions for Analysis\n", "def moving_average(data, kernel_size):\n", "    \"\"\"Apply moving average filter\"\"\"\n", "    if kernel_size <= 1:\n", "        return data\n", "    kernel = np.ones(kernel_size) / kernel_size\n", "    return np.convolve(data, kernel, mode='same')\n", "\n", "def find_peaks_in_intensity(intensities, neighborhood_size, threshold, smoothing_sigma):\n", "    \"\"\"Find peaks in intensity data\"\"\"\n", "    # Smooth the intensities to reduce noise\n", "    intensities_smooth = gaussian_filter(intensities, sigma=smoothing_sigma)\n", "\n", "    # Apply maximum filter to find local maxima\n", "    local_max = maximum_filter(intensities_smooth, size=neighborhood_size) == intensities_smooth\n", "\n", "    # Apply threshold to identify significant peaks\n", "    detected_peaks = (intensities_smooth > threshold) & local_max\n", "\n", "    # Get peak indices\n", "    peak_indices = np.argwhere(detected_peaks)\n", "\n", "    return peak_indices\n", "\n", "def create_hover_text(x, y, z, mode):\n", "    \"\"\"Create hover text for Plotly plots\"\"\"\n", "    if mode == 'E vs kx':\n", "        return f'kx: {x:.3f} Å⁻¹<br>Binding Energy: {y:.3f} eV<br>Intensity: {z:.3f}'\n", "    elif mode == 'kx vs ky':\n", "        return f'kx: {x:.3f} Å⁻¹<br>ky: {y:.3f} Å⁻¹<br>Intensity: {z:.3f}'\n", "    elif mode == 'kx vs kz':\n", "        return f'kx: {x:.3f} Å⁻¹<br>kz: {y:.3f} Å⁻¹<br>Intensity: {z:.3f}'\n", "    else:\n", "        return f'X: {x:.3f}<br>Y: {y:.3f}<br>Z: {z:.3f}'\n", "\n", "# Constants\n", "WORK_FUNCTION = 4.5  # eV\n", "V0 = 10  # Inner potential in eV\n", "\n", "print(\"✓ Utility functions loaded!\")"]}, {"cell_type": "code", "execution_count": null, "id": "plotting_functions", "metadata": {}, "outputs": [], "source": ["# Enhanced Plotly-based Plotting Functions\n", "class ARPESPlotter:\n", "    def __init__(self, data_loader):\n", "        self.data_loader = data_loader\n", "        self.current_fig = None\n", "        \n", "    def plot_E_vs_kx(self, scan_number, vmin, vmax, kernel_size, x_offset, y_offset, \n", "                     colorscale='Custom Rainbow', show_peaks=False, peak_threshold=0.5,\n", "                     neighborhood_size=5, smoothing_sigma=1.0):\n", "        \"\"\"Plot binding energy vs kx for a single scan\"\"\"\n", "        \n", "        if not self.data_loader.data_proc:\n", "            print(\"❌ No data loaded. Please load data first.\")\n", "            return None\n", "            \n", "        if scan_number < 0 or scan_number >= len(self.data_loader.data_proc):\n", "            print(f\"❌ Invalid scan number. Must be between 0 and {len(self.data_loader.data_proc)-1}\")\n", "            return None\n", "\n", "        # Get data for the selected scan\n", "        df = self.data_loader.data_proc[scan_number]\n", "        hv = self.data_loader.data_attributes[scan_number]['hv']\n", "\n", "        # Emission angles and kinetic energies\n", "        emission_angles = df.columns.values.astype(float)\n", "        theta_rad = np.deg2rad(emission_angles)\n", "        E_kinetic_values = df.index.values.astype(float)\n", "\n", "        # Ensure increasing order\n", "        if np.any(np.diff(E_kinetic_values) < 0):\n", "            E_kinetic_values = E_kinetic_values[::-1]\n", "            df = df.iloc[::-1]\n", "\n", "        # Create grids\n", "        theta_grid, E_kinetic_grid = np.meshgrid(theta_rad, E_kinetic_values, indexing='xy')\n", "        E_binding_grid = hv - WORK_FUNCTION - E_kinetic_grid + y_offset\n", "        kx_grid = 0.5123 * np.sqrt(E_kinetic_grid) * np.sin(theta_grid) + x_offset\n", "\n", "        # Process intensities\n", "        intensities = df.values\n", "        if kernel_size > 1:\n", "            intensities = np.apply_along_axis(\n", "                lambda m: moving_average(m, kernel_size), axis=0, arr=intensities\n", "            )\n", "\n", "        # Normalize intensities\n", "        max_intensity = np.nanmax(intensities)\n", "        if max_intensity > 0:\n", "            intensities = intensities / max_intensity\n", "\n", "        # Mask invalid data\n", "        valid_mask = np.isfinite(kx_grid) & np.isfinite(E_binding_grid) & np.isfinite(intensities)\n", "        intensities[~valid_mask] = np.nan\n", "\n", "        # Create Plotly figure\n", "        fig = go.Figure()\n", "\n", "        # Add heatmap\n", "        colorscale_to_use = COLORSCALES.get(colorscale, 'Viridis')\n", "        \n", "        fig.add_trace(go.Heatmap(\n", "            x=kx_grid[0, :],  # kx values\n", "            y=E_binding_grid[:, 0],  # binding energy values\n", "            z=intensities,\n", "            colorscale=colorscale_to_use,\n", "            zmin=vmin,\n", "            zmax=vmax,\n", "            colorbar=dict(\n", "                title=\"Normalized Intensity\",\n", "                titleside=\"right\"\n", "            ),\n", "            hovertemplate='kx: %{x:.3f} Å⁻¹<br>Binding Energy: %{y:.3f} eV<br>Intensity: %{z:.3f}<extra></extra>'\n", "        ))\n", "\n", "        # Add peaks if requested\n", "        if show_peaks:\n", "            intensities_filled = np.nan_to_num(intensities, nan=0.0)\n", "            peak_indices = find_peaks_in_intensity(\n", "                intensities_filled, neighborhood_size, peak_threshold, smoothing_sigma\n", "            )\n", "            \n", "            if peak_indices.size > 0:\n", "                peak_kx = [kx_grid[i, j] for i, j in peak_indices]\n", "                peak_E = [E_binding_grid[i, j] for i, j in peak_indices]\n", "                \n", "                fig.add_trace(go.<PERSON>(\n", "                    x=peak_kx,\n", "                    y=peak_E,\n", "                    mode='markers',\n", "                    marker=dict(color='red', size=8, symbol='circle'),\n", "                    name='Peaks',\n", "                    hovertemplate='Peak<br>kx: %{x:.3f} Å⁻¹<br>Binding Energy: %{y:.3f} eV<extra></extra>'\n", "                ))\n", "\n", "        # Update layout\n", "        fig.update_layout(\n", "            title=f'Binding Energy vs k<sub>x</sub> Map - Scan {scan_number}',\n", "            xaxis_title='k<sub>x</sub> (Å⁻¹)',\n", "            yaxis_title='Binding Energy (eV)',\n", "            width=800,\n", "            height=600,\n", "            yaxis=dict(autorange='reversed'),  # Invert y-axis\n", "            template='plotly_white'\n", "        )\n", "\n", "        self.current_fig = fig\n", "        return fig\n", "    \n", "    def plot_kx_vs_ky(self, E_binding, vmin, vmax, kernel_size, x_offset, y_offset,\n", "                      colorscale='Custom Rainbow', use_contours=False, contour_levels=20,\n", "                      show_peaks=False, peak_threshold=0.5, neighborhood_size=5, smoothing_sigma=1.0):\n", "        \"\"\"Plot kx vs ky constant energy map\"\"\"\n", "        \n", "        if not self.data_loader.data_proc:\n", "            print(\"❌ No data loaded. Please load data first.\")\n", "            return None\n", "\n", "        # Collect data from all scans\n", "        kx_list, ky_list, intensity_list = [], [], []\n", "\n", "        for i in range(len(self.data_loader.data_proc)):\n", "            df = self.data_loader.data_proc[i]\n", "            hv = self.data_loader.data_attributes[i]['hv']\n", "            polar_angle = self.data_loader.data_attributes[i]['polar']\n", "            polar_angle_rad = np.deg2rad(polar_angle)\n", "\n", "            emission_angles = df.columns.values.astype(float)\n", "            theta_rad = np.deg2rad(emission_angles)\n", "\n", "            # Calculate kinetic energy for desired binding energy\n", "            E_kinetic = hv - WORK_FUNCTION - E_binding\n", "            E_kinetic_values = df.index.values.astype(float)\n", "            \n", "            if E_kinetic < E_kinetic_values.min() or E_kinetic > E_kinetic_values.max():\n", "                continue\n", "\n", "            k_magnitude = 0.5123 * np.sqrt(E_kinetic)\n", "            kx = k_magnitude * np.sin(theta_rad) + x_offset\n", "            ky = k_magnitude * np.sin(polar_angle_rad) + y_offset\n", "\n", "            # Extract intensities\n", "            if E_kinetic in E_kinetic_values:\n", "                intensities = df.loc[E_kinetic].values\n", "            else:\n", "                intensities = df.apply(\n", "                    lambda col: np.interp(E_kinetic, E_kinetic_values, col.values)\n", "                ).values\n", "\n", "            if kernel_size > 1:\n", "                intensities = moving_average(intensities, kernel_size)\n", "\n", "            kx_list.extend(kx)\n", "            ky_list.extend(np.full_like(kx, ky))\n", "            intensity_list.extend(intensities)\n", "\n", "        if not kx_list:\n", "            print(f\"❌ No data available for binding energy {E_binding:.2f} eV\")\n", "            return None\n", "\n", "        # Convert to arrays and interpolate\n", "        kx_array = np.array(kx_list)\n", "        ky_array = np.array(ky_list)\n", "        intensity_array = np.array(intensity_list)\n", "\n", "        # Create grid for interpolation\n", "        grid_resolution = 200\n", "        kx_grid = np.linspace(kx_array.min(), kx_array.max(), grid_resolution)\n", "        ky_grid = np.linspace(ky_array.min(), ky_array.max(), grid_resolution)\n", "        kx_mesh, ky_mesh = np.meshgrid(kx_grid, ky_grid)\n", "\n", "        intensity_grid = griddata(\n", "            points=(kx_array, ky_array),\n", "            values=intensity_array,\n", "            xi=(kx_mesh, ky_mesh),\n", "            method='cubic'\n", "        )\n", "\n", "        intensity_grid = np.nan_to_num(intensity_grid)\n", "        max_intensity = intensity_grid.max()\n", "        if max_intensity > 0:\n", "            intensity_grid /= max_intensity\n", "\n", "        # Create figure\n", "        fig = go.Figure()\n", "        colorscale_to_use = COLORSCALES.get(colorscale, 'Viridis')\n", "\n", "        if use_contours:\n", "            fig.add_trace(go.Contour(\n", "                x=kx_grid,\n", "                y=ky_grid,\n", "                z=intensity_grid,\n", "                colorscale=colorscale_to_use,\n", "                ncontours=contour_levels,\n", "                colorbar=dict(title=\"Intensity\"),\n", "                hovertemplate='kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<br>Intensity: %{z:.3f}<extra></extra>'\n", "            ))\n", "        else:\n", "            fig.add_trace(go.Heatmap(\n", "                x=kx_grid,\n", "                y=ky_grid,\n", "                z=intensity_grid,\n", "                colorscale=colorscale_to_use,\n", "                zmin=vmin,\n", "                zmax=vmax,\n", "                colorbar=dict(title=\"Intensity\"),\n", "                hovertemplate='kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<br>Intensity: %{z:.3f}<extra></extra>'\n", "            ))\n", "\n", "        # Add peaks if requested\n", "        if show_peaks:\n", "            peak_indices = find_peaks_in_intensity(\n", "                intensity_grid, neighborhood_size, peak_threshold, smoothing_sigma\n", "            )\n", "            \n", "            if peak_indices.size > 0:\n", "                peak_kx = [kx_mesh[i, j] for i, j in peak_indices]\n", "                peak_ky = [ky_mesh[i, j] for i, j in peak_indices]\n", "                \n", "                fig.add_trace(go.<PERSON>(\n", "                    x=peak_kx,\n", "                    y=peak_ky,\n", "                    mode='markers',\n", "                    marker=dict(color='red', size=8, symbol='circle'),\n", "                    name='Peaks',\n", "                    hovertemplate='Peak<br>kx: %{x:.3f} Å⁻¹<br>ky: %{y:.3f} Å⁻¹<extra></extra>'\n", "                ))\n", "\n", "        fig.update_layout(\n", "            title=f'k<sub>x</sub> vs k<sub>y</sub> Map at E<sub>B</sub> = {E_binding:.2f} eV',\n", "            xaxis_title='k<sub>x</sub> (Å⁻¹)',\n", "            yaxis_title='k<sub>y</sub> (Å⁻¹)',\n", "            width=800,\n", "            height=600,\n", "            template='plotly_white'\n", "        )\n", "\n", "        self.current_fig = fig\n", "        return fig\n", "\n", "# Create global plotter instance\n", "plotter = ARPESPlotter(data_loader)\n", "\n", "print(\"✓ Plotting functions loaded!\")"]}, {"cell_type": "code", "execution_count": null, "id": "gui_interface", "metadata": {}, "outputs": [], "source": ["# Enhanced GUI Interface\n", "class ARPESAnalysisGUI:\n", "    def __init__(self, data_loader, plotter):\n", "        self.data_loader = data_loader\n", "        self.plotter = plotter\n", "        self.plot_output = Output()\n", "        self.setup_widgets()\n", "        \n", "    def setup_widgets(self):\n", "        \"\"\"Setup all GUI widgets with enhanced organization\"\"\"\n", "        \n", "        # Data loading section\n", "        self.load_button = Button(\n", "            description='📁 Load PXT Files',\n", "            button_style='primary',\n", "            layout=Layout(width='200px', height='40px')\n", "        )\n", "        self.load_button.on_click(self.load_data)\n", "        \n", "        # Plot mode selection\n", "        self.mode_selector = Dropdown(\n", "            options=['E vs kx', 'kx vs ky', 'kx vs kz', '3D Visualization'],\n", "            value='E vs kx',\n", "            description='Plot Mode:',\n", "            style={'description_width': '100px'},\n", "            layout=Layout(width='250px')\n", "        )\n", "        \n", "        # Colorscale selection\n", "        self.colorscale_selector = Dropdown(\n", "            options=list(COLORSCALES.keys()),\n", "            value='Custom Rainbow',\n", "            description='Colorscale:',\n", "            style={'description_width': '100px'},\n", "            layout=Layout(width='250px')\n", "        )\n", "        \n", "        # Energy and scan controls\n", "        self.E_binding_slider = FloatSlider(\n", "            value=0.0,\n", "            min=-2.0,\n", "            max=2.0,\n", "            step=0.01,\n", "            description='E_B (eV):',\n", "            continuous_update=False,\n", "            style={'description_width': '80px'},\n", "            layout=Layout(width='300px')\n", "        )\n", "        \n", "        self.scan_selector = IntSlider(\n", "            value=0,\n", "            min=0,\n", "            max=0,\n", "            step=1,\n", "            description='Scan #:',\n", "            continuous_update=False,\n", "            style={'description_width': '80px'},\n", "            layout=Layout(width='300px')\n", "        )\n", "        \n", "        # Intensity controls\n", "        self.vmin_slider = FloatSlider(\n", "            value=0.0,\n", "            min=0.0,\n", "            max=1.0,\n", "            step=0.01,\n", "            description='Min Int:',\n", "            continuous_update=False,\n", "            style={'description_width': '80px'},\n", "            layout=Layout(width='250px')\n", "        )\n", "        \n", "        self.vmax_slider = FloatSlider(\n", "            value=1.0,\n", "            min=0.0,\n", "            max=1.0,\n", "            step=0.01,\n", "            description='Max Int:',\n", "            continuous_update=False,\n", "            style={'description_width': '80px'},\n", "            layout=Layout(width='250px')\n", "        )\n", "        \n", "        # Processing controls\n", "        self.kernel_size_slider = IntSlider(\n", "            value=1,\n", "            min=1,\n", "            max=21,\n", "            step=2,\n", "            description='Kernel:',\n", "            continuous_update=False,\n", "            style={'description_width': '80px'},\n", "            layout=Layout(width='250px')\n", "        )\n", "        \n", "        # Offset controls\n", "        self.x_offset_slider = FloatSlider(\n", "            value=0.0,\n", "            min=-2.0,\n", "            max=2.0,\n", "            step=0.01,\n", "            description='X Offset:',\n", "            continuous_update=False,\n", "            style={'description_width': '80px'},\n", "            layout=Layout(width='250px')\n", "        )\n", "        \n", "        self.y_offset_slider = FloatSlider(\n", "            value=0.0,\n", "            min=-2.0,\n", "            max=2.0,\n", "            step=0.01,\n", "            description='Y Offset:',\n", "            continuous_update=False,\n", "            style={'description_width': '80px'},\n", "            layout=Layout(width='250px')\n", "        )\n", "        \n", "        # Analysis options\n", "        self.show_peaks_checkbox = Checkbox(\n", "            value=False,\n", "            description='Show Peaks',\n", "            layout=Layout(width='150px')\n", "        )\n", "        \n", "        self.use_contours_checkbox = Checkbox(\n", "            value=False,\n", "            description='Use Contours',\n", "            layout=Layout(width='150px')\n", "        )\n", "        \n", "        self.contour_levels_slider = IntSlider(\n", "            value=20,\n", "            min=5,\n", "            max=50,\n", "            step=5,\n", "            description='Contours:',\n", "            continuous_update=False,\n", "            style={'description_width': '80px'},\n", "            layout=Layout(width='250px')\n", "        )\n", "        \n", "        # Peak detection controls\n", "        self.peak_threshold_slider = FloatSlider(\n", "            value=0.5,\n", "            min=0.0,\n", "            max=1.0,\n", "            step=0.01,\n", "            description='Peak Thr:',\n", "            continuous_update=False,\n", "            style={'description_width': '80px'},\n", "            layout=Layout(width='250px')\n", "        )\n", "        \n", "        self.neighborhood_size_slider = IntSlider(\n", "            value=5,\n", "            min=3,\n", "            max=15,\n", "            step=2,\n", "            description='Neighbor:',\n", "            continuous_update=False,\n", "            style={'description_width': '80px'},\n", "            layout=Layout(width='250px')\n", "        )\n", "        \n", "        self.smoothing_sigma_slider = FloatSlider(\n", "            value=1.0,\n", "            min=0.1,\n", "            max=3.0,\n", "            step=0.1,\n", "            description='Smooth:',\n", "            continuous_update=False,\n", "            style={'description_width': '80px'},\n", "            layout=Layout(width='250px')\n", "        )\n", "        \n", "        # Action buttons\n", "        self.plot_button = Button(\n", "            description='🎨 Generate Plot',\n", "            button_style='success',\n", "            layout=Layout(width='150px', height='35px')\n", "        )\n", "        self.plot_button.on_click(self.generate_plot)\n", "        \n", "        self.export_button = Button(\n", "            description='💾 Export Plot',\n", "            button_style='info',\n", "            layout=Layout(width='150px', height='35px')\n", "        )\n", "        self.export_button.on_click(self.export_plot)\n", "        \n", "        # Status display\n", "        self.status_html = HTML(\n", "            value=\"<b>Status:</b> Ready to load data\",\n", "            layout=Layout(width='100%', height='30px')\n", "        )\n", "        \n", "    def load_data(self, button):\n", "        \"\"\"Load data and update GUI\"\"\"\n", "        self.status_html.value = \"<b>Status:</b> Loading data...\"\n", "        \n", "        success = self.data_loader.load_pxt_files()\n", "        \n", "        if success:\n", "            # Update scan selector range\n", "            self.scan_selector.max = len(self.data_loader.data_proc) - 1\n", "            \n", "            # Update energy range\n", "            E_min, E_max = self.data_loader.get_binding_energy_range()\n", "            self.E_binding_slider.min = E_min\n", "            self.E_binding_slider.max = E_max\n", "            self.E_binding_slider.value = (E_min + E_max) / 2\n", "            \n", "            self.status_html.value = f\"<b>Status:</b> ✅ Loaded {len(self.data_loader.data_proc)} scans\"\n", "        else:\n", "            self.status_html.value = \"<b>Status:</b> ❌ Failed to load data\"\n", "    \n", "    def generate_plot(self, button):\n", "        \"\"\"Generate plot based on current settings\"\"\"\n", "        if not self.data_loader.data_proc:\n", "            self.status_html.value = \"<b>Status:</b> ❌ No data loaded\"\n", "            return\n", "            \n", "        self.status_html.value = \"<b>Status:</b> Generating plot...\"\n", "        \n", "        with self.plot_output:\n", "            clear_output(wait=True)\n", "            \n", "            try:\n", "                mode = self.mode_selector.value\n", "                \n", "                if mode == 'E vs kx':\n", "                    fig = self.plotter.plot_E_vs_kx(\n", "                        scan_number=self.scan_selector.value,\n", "                        vmin=self.vmin_slider.value,\n", "                        vmax=self.vmax_slider.value,\n", "                        kernel_size=self.kernel_size_slider.value,\n", "                        x_offset=self.x_offset_slider.value,\n", "                        y_offset=self.y_offset_slider.value,\n", "                        colorscale=self.colorscale_selector.value,\n", "                        show_peaks=self.show_peaks_checkbox.value,\n", "                        peak_threshold=self.peak_threshold_slider.value,\n", "                        neighborhood_size=self.neighborhood_size_slider.value,\n", "                        smoothing_sigma=self.smoothing_sigma_slider.value\n", "                    )\n", "                    \n", "                elif mode == 'kx vs ky':\n", "                    fig = self.plotter.plot_kx_vs_ky(\n", "                        E_binding=self.E_binding_slider.value,\n", "                        vmin=self.vmin_slider.value,\n", "                        vmax=self.vmax_slider.value,\n", "                        kernel_size=self.kernel_size_slider.value,\n", "                        x_offset=self.x_offset_slider.value,\n", "                        y_offset=self.y_offset_slider.value,\n", "                        colorscale=self.colorscale_selector.value,\n", "                        use_contours=self.use_contours_checkbox.value,\n", "                        contour_levels=self.contour_levels_slider.value,\n", "                        show_peaks=self.show_peaks_checkbox.value,\n", "                        peak_threshold=self.peak_threshold_slider.value,\n", "                        neighborhood_size=self.neighborhood_size_slider.value,\n", "                        smoothing_sigma=self.smoothing_sigma_slider.value\n", "                    )\n", "                    \n", "                else:\n", "                    print(f\"Plot mode '{mode}' not yet implemented\")\n", "                    return\n", "                \n", "                if fig:\n", "                    fig.show()\n", "                    self.status_html.value = \"<b>Status:</b> ✅ Plot generated successfully\"\n", "                else:\n", "                    self.status_html.value = \"<b>Status:</b> ❌ Failed to generate plot\"\n", "                    \n", "            except Exception as e:\n", "                print(f\"Error generating plot: {str(e)}\")\n", "                self.status_html.value = f\"<b>Status:</b> ❌ Error: {str(e)[:50]}...\"\n", "    \n", "    def export_plot(self, button):\n", "        \"\"\"Export current plot\"\"\"\n", "        if self.plotter.current_fig is None:\n", "            self.status_html.value = \"<b>Status:</b> ❌ No plot to export\"\n", "            return\n", "            \n", "        try:\n", "            # Export as HTML\n", "            filename = f\"arpes_plot_{self.mode_selector.value.replace(' ', '_').lower()}.html\"\n", "            self.plotter.current_fig.write_html(filename)\n", "            self.status_html.value = f\"<b>Status:</b> ✅ Plot exported as {filename}\"\n", "        except Exception as e:\n", "            self.status_html.value = f\"<b>Status:</b> ❌ Export failed: {str(e)[:30]}...\"\n", "    \n", "    def create_interface(self):\n", "        \"\"\"Create the complete GUI interface\"\"\"\n", "        \n", "        # Organize widgets into tabs\n", "        data_tab = VBox([\n", "            HTML(\"<h3>📁 Data Loading</h3>\"),\n", "            self.load_button,\n", "            self.data_loader.status_output,\n", "            HTML(\"<hr>\"),\n", "            HTML(\"<h4>Plot Settings</h4>\"),\n", "            HBox([self.mode_selector, self.colorscale_selector]),\n", "            HBox([self.E_binding_slider, self.scan_selector])\n", "        ])\n", "        \n", "        display_tab = VBox([\n", "            HTML(\"<h3>🎨 Display Controls</h3>\"),\n", "            HTML(\"<h4>Intensity Range</h4>\"),\n", "            HBox([self.vmin_slider, self.vmax_slider]),\n", "            HTML(\"<h4>Offsets</h4>\"),\n", "            HBox([self.x_offset_slider, self.y_offset_slider]),\n", "            HTML(\"<h4>Processing</h4>\"),\n", "            self.kernel_size_slider\n", "        ])\n", "        \n", "        analysis_tab = VBox([\n", "            HTML(\"<h3>🔬 Analysis Options</h3>\"),\n", "            HBox([self.show_peaks_checkbox, self.use_contours_checkbox]),\n", "            self.contour_levels_slider,\n", "            HTML(\"<h4>Peak Detection</h4>\"),\n", "            HBox([self.peak_threshold_slider, self.neighborhood_size_slider]),\n", "            self.smoothing_sigma_slider\n", "        ])\n", "        \n", "        # Create tabs\n", "        tabs = Tab(children=[data_tab, display_tab, analysis_tab])\n", "        tabs.set_title(0, '📁 Data & Plot')\n", "        tabs.set_title(1, '🎨 Display')\n", "        tabs.set_title(2, '🔬 Analysis')\n", "        \n", "        # Action buttons and status\n", "        actions = HBox([\n", "            self.plot_button,\n", "            self.export_button\n", "        ])\n", "        \n", "        # Complete interface\n", "        interface = VBox([\n", "            HTML(\"<h1>🔬 Enhanced ARPES Analysis Interface</h1>\"),\n", "            HTML(\"<p>Interactive ARPES data analysis with Plotly visualization</p>\"),\n", "            tabs,\n", "            HTML(\"<hr>\"),\n", "            actions,\n", "            self.status_html,\n", "            HTML(\"<hr>\"),\n", "            self.plot_output\n", "        ])\n", "        \n", "        return interface\n", "\n", "# Create and display the enhanced GUI\n", "gui = ARPESAnalysisGUI(data_loader, plotter)\n", "interface = gui.create_interface()\n", "\n", "print(\"✓ Enhanced GUI interface created!\")\n", "print(\"\\n🚀 Ready to use! Click 'Load PXT Files' to begin.\")"]}, {"cell_type": "code", "execution_count": null, "id": "display_interface", "metadata": {}, "outputs": [], "source": ["# Display the interface\n", "display(interface)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 5}