# 🔬 ARPES Analysis GUI - Standalone Application

A complete standalone GUI application for ARPES (Angle-Resolved Photoemission Spectroscopy) data analysis with interactive Plotly visualizations.

## ✨ Features

### 🎯 **Complete Standalone Application**
- **Native GUI**: Runs in its own window using tkinter (no Jupyter required)
- **All widgets visible**: Every control is properly displayed and functional
- **Real-time updates**: Sliders and controls update plots immediately
- **Cross-platform**: Works on Windows, macOS, and Linux

### 🎨 **Interactive Plotting**
- **Plotly-powered**: Modern, interactive plots with zoom, pan, and hover
- **Multiple colorscales**: Custom rainbow plus built-in options (Viridis, Plasma, etc.)
- **Live preview**: Plots open automatically in your web browser
- **Export functionality**: Save plots as interactive HTML files

### 📊 **Analysis Capabilities**
- **E vs kx plots**: Single scan energy-momentum dispersion
- **kx vs ky plots**: Constant energy cuts and Fermi surface mapping
- **Peak detection**: Automatic peak finding with customizable parameters
- **Contour plotting**: Alternative visualization for constant energy maps
- **Data processing**: Smoothing, offset correction, and intensity normalization

### 🔧 **User-Friendly Interface**
- **Organized sections**: Data Loading, Plot Settings, Display Controls, Analysis Options
- **Real-time feedback**: Status messages and progress indicators
- **Intuitive controls**: Sliders, dropdowns, and checkboxes with clear labels
- **Error handling**: Comprehensive error messages and recovery

## 📋 Requirements

### **Python Packages**
```bash
pip install plotly pandas numpy scipy scikit-image xarray
```

### **ARPES Data Loading**
- Requires `arpes.load_pxt` module for reading PXT files
- Make sure your ARPES analysis environment is properly set up

## 🚀 Quick Start

### **Method 1: Auto-Launcher (Recommended)**
```bash
python launch_arpes_gui.py
```
This script will:
- Check for missing dependencies
- Install them automatically if needed
- Launch the GUI application

### **Method 2: Direct Launch**
```bash
python arpes_analysis_gui.py
```

### **Method 3: From Python**
```python
from arpes_analysis_gui import main
main()
```

## 📖 How to Use

### **1. Launch the Application**
- Run the launcher script or main application
- A new window will open with the ARPES Analysis Interface

### **2. Load Your Data**
- Click "📁 Load PXT Files"
- Select the folder containing your .pxt files
- Watch the progress as files are loaded
- The interface will automatically update with your data ranges

### **3. Configure Your Plot**
- **Plot Mode**: Choose between "E vs kx" or "kx vs ky"
- **Colorscale**: Select from multiple options including your custom rainbow
- **Scan Number**: For E vs kx mode, select which scan to display
- **Binding Energy**: For kx vs ky mode, set the energy slice

### **4. Adjust Display Settings**
- **Intensity Range**: Set min/max values for better contrast
- **Offsets**: Apply X/Y corrections to your data
- **Kernel Size**: Apply smoothing to reduce noise

### **5. Analysis Options**
- **Show Peaks**: Enable automatic peak detection
- **Use Contours**: Switch to contour plot mode for kx vs ky
- **Peak Parameters**: Adjust threshold, neighborhood size, and smoothing

### **6. Generate and Export**
- Click "🎨 Generate Plot" to create your visualization
- Plots automatically open in your web browser
- Use "💾 Export Plot" to save as interactive HTML files

## 🎛️ Interface Sections

### **Data Loading**
- **Load PXT Files**: Browse and load your data files
- **Export Plot**: Save current plot as HTML
- **Generate Plot**: Create visualization with current settings
- **Status Display**: Real-time feedback and progress

### **Plot Settings**
- **Plot Mode**: E vs kx or kx vs ky visualization
- **Colorscale**: Choose from 7 different color schemes
- **Scan Number**: Select specific scan (E vs kx mode)
- **Binding Energy**: Set energy slice (kx vs ky mode)

### **Display Controls**
- **Min/Max Intensity**: Adjust contrast and brightness
- **X/Y Offset**: Apply spatial corrections
- **Kernel Size**: Control smoothing level

### **Analysis Options**
- **Show Peaks**: Toggle peak detection overlay
- **Use Contours**: Switch to contour visualization
- **Contour Levels**: Number of contour lines
- **Peak Threshold**: Sensitivity for peak detection
- **Neighborhood Size**: Peak detection window
- **Smoothing Sigma**: Gaussian smoothing parameter

## 🎨 Available Colorscales

1. **Custom Rainbow**: Your original colormap from Igor
2. **Viridis**: Perceptually uniform, colorblind-friendly
3. **Plasma**: High contrast purple-pink-yellow
4. **Inferno**: Dark theme with warm colors
5. **Magma**: Dark purple to bright yellow
6. **Cividis**: Colorblind-friendly alternative to jet
7. **Turbo**: Google's improved version of jet colormap

## 🔧 Technical Details

### **Architecture**
- **ARPESDataLoader**: Handles PXT file loading and processing
- **ARPESPlotter**: Creates Plotly visualizations
- **ARPESAnalysisGUI**: Main tkinter interface

### **Data Processing**
- Automatic binding energy conversion
- Momentum space calculations (kx, ky, kz)
- Intensity normalization and smoothing
- Peak detection using scipy algorithms

### **Plot Generation**
- Interactive Plotly figures with hover information
- Automatic browser opening for plot display
- HTML export for sharing and publication
- Real-time parameter updates

## 🐛 Troubleshooting

### **Common Issues**

#### **"Module not found" errors**
```bash
pip install plotly pandas numpy scipy scikit-image xarray
```

#### **"No PXT files found"**
- Make sure you're selecting the correct folder
- Check that files have .pxt extension
- Verify file permissions

#### **Plots not opening**
- Check your default web browser settings
- Try manually opening the generated HTML files
- Ensure you have write permissions in the working directory

#### **Performance issues**
- Reduce kernel size for faster processing
- Lower the grid resolution for kx vs ky plots
- Close other applications to free up memory

### **Getting Help**
1. Check the status messages in the interface
2. Look at the console output for detailed error messages
3. Verify your data files are in the correct format
4. Make sure all dependencies are properly installed

## 📁 File Structure

```
arpes_analysis_gui.py      # Main application
launch_arpes_gui.py        # Auto-launcher with dependency checking
ARPES_GUI_README.md        # This documentation
ThesisAnalysis_Fixed.ipynb # Jupyter notebook version (if needed)
```

## 🎯 Advantages Over Jupyter

1. **No widget rendering issues**: Native tkinter widgets always work
2. **Standalone application**: No need for Jupyter server
3. **Better performance**: Direct GUI without browser overhead
4. **Easier distribution**: Single Python file to share
5. **Professional appearance**: Native OS look and feel

---

**Enjoy your enhanced ARPES data analysis experience!** 🚀

For questions or issues, check the troubleshooting section above or examine the console output for detailed error messages.
