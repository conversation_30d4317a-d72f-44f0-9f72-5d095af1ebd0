# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_energy_min = float('inf')
    global_energy_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global energy range
        global_energy_min = min(global_energy_min, np.min(energy_values))
        global_energy_max = max(global_energy_max, np.max(energy_values))

    # Create a figure and axis object once
    fig, ax = plt.subplots(figsize=(10, 6))
    out = Output()

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current axes
        ax.clear()

        # Calculate equally spaced energy indices
        energy_indices = np.linspace(0, len(plot_data['energy_values']) - 1, n, dtype=int)
        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, energy_index in enumerate(energy_indices):
            actual_energy = plot_data['energy_values'][energy_index]
            mdc = plot_data['data_values'][energy_index, :]
            k_parallel = plot_data['k_parallel'][energy_index, :]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(k_parallel, mdc + i * vertical_offset, label=f'E = {actual_energy:.2f} eV')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Split data into left and right sides
                left_mask = k_parallel < 0
                right_mask = k_parallel > 0

                # Fit left side
                if np.any(left_mask):
                    left_k = k_parallel[left_mask]
                    left_mdc = mdc[left_mask]
                    peaks, _ = find_peaks(left_mdc)
                    peak_heights = left_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(left_mdc))

                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'l{j+1}_center'].set(value=left_k[peak], min=left_k.min(), max=left_k.max())
                        params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                    result = model.fit(left_mdc, params, x=left_k)
                    fit = result.best_fit
                    ax.plot(left_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (left)', color=f'C{i}')

                # Fit right side
                if np.any(right_mask):
                    right_k = k_parallel[right_mask]
                    right_mdc = mdc[right_mask]
                    peaks, _ = find_peaks(right_mdc)
                    peak_heights = right_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(right_mdc))

                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'r{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'r{j+1}_center'].set(value=right_k[peak], min=right_k.min(), max=right_k.max())
                        params[f'r{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'r{j+1}_sigma'].set(value=0.1, min=0)

                    result = model.fit(right_mdc, params, x=right_k)
                    fit = result.best_fit
                    ax.plot(right_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (right)', color=f'C{i}')

        ax.set_xlabel(r'$k_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

        with out:
            clear_output(wait=True)
            display(fig)

        return fig, max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        fig, _ = plot_mdc(**current_values)
        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        _, max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Function to display the plot in the output widget
    def display_plot(*args):
        plot_mdc(interactive_plot.children[0].value,
                 interactive_plot.children[1].value,
                 interactive_plot.children[2].value,
                 interactive_plot.children[3].value,
                 interactive_plot.children[4].value,
                 interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_energy_min = float('inf')
    global_energy_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global energy range
        global_energy_min = min(global_energy_min, np.min(energy_values))
        global_energy_max = max(global_energy_max, np.max(energy_values))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Calculate equally spaced energy indices
        energy_indices = np.linspace(0, len(plot_data['energy_values']) - 1, n, dtype=int)
        
        max_intensity = 0  # Initialize max intensity for the displayed curves
        
        for i, energy_index in enumerate(energy_indices):
            actual_energy = plot_data['energy_values'][energy_index]
            mdc = plot_data['data_values'][energy_index, :]
            k_parallel = plot_data['k_parallel'][energy_index, :]
            
            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values
            
            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))
            
            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(k_parallel, mdc + i * vertical_offset, label=f'E = {actual_energy:.2f} eV')
            
            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Split data into left and right sides
                left_mask = k_parallel < 0
                right_mask = k_parallel > 0
                
                # Fit left side
                if np.any(left_mask):
                    left_k = k_parallel[left_mask]
                    left_mdc = mdc[left_mask]
                    peaks, _ = find_peaks(left_mdc)
                    peak_heights = left_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]
                    
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(left_mdc))
                    
                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'l{j+1}_center'].set(value=left_k[peak], min=left_k.min(), max=left_k.max())
                        params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'l{j+1}_sigma'].set(value=0.1, min=0)
                    
                    result = model.fit(left_mdc, params, x=left_k)
                    fit = result.best_fit
                    ax.plot(left_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (left)', color=f'C{i}')
                
                # Fit right side
                if np.any(right_mask):
                    right_k = k_parallel[right_mask]
                    right_mdc = mdc[right_mask]
                    peaks, _ = find_peaks(right_mdc)
                    peak_heights = right_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]
                    
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(right_mdc))
                    
                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'r{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'r{j+1}_center'].set(value=right_k[peak], min=right_k.min(), max=right_k.max())
                        params[f'r{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'r{j+1}_sigma'].set(value=0.1, min=0)
                    
                    result = model.fit(right_mdc, params, x=right_k)
                    fit = result.best_fit
                    ax.plot(right_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (right)', color=f'C{i}')
        
        ax.set_xlabel(r'$k_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig, max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        fig, _ = plot_mdc(**current_values)

        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        _, max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_mdc=Checkbox(value=True, description='Show MDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            fig, _ = plot_mdc(interactive_plot.children[0].value,
                              interactive_plot.children[1].value,
                              interactive_plot.children[2].value,
                              interactive_plot.children[3].value,
                              interactive_plot.children[4].value,
                              interactive_plot.children[5].value)
            display(fig)
            plt.close(fig)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_energy_min = float('inf')
    global_energy_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global energy range
        global_energy_min = min(global_energy_min, np.min(energy_values))
        global_energy_max = max(global_energy_max, np.max(energy_values))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        clear_output(wait=True)
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.clear()

        # Calculate equally spaced energy indices
        energy_indices = np.linspace(0, len(plot_data['energy_values']) - 1, n, dtype=int)
        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, energy_index in enumerate(energy_indices):
            actual_energy = plot_data['energy_values'][energy_index]
            mdc = plot_data['data_values'][energy_index, :]
            k_parallel = plot_data['k_parallel'][energy_index, :]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(k_parallel, mdc + i * vertical_offset, label=f'E = {actual_energy:.2f} eV')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Split data into left and right sides
                left_mask = k_parallel < 0
                right_mask = k_parallel > 0

                # Fit left side
                if np.any(left_mask):
                    left_k = k_parallel[left_mask]
                    left_mdc = mdc[left_mask]
                    peaks, _ = find_peaks(left_mdc)
                    peak_heights = left_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(left_mdc))

                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'l{j+1}_center'].set(value=left_k[peak], min=left_k.min(), max=left_k.max())
                        params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                    result = model.fit(left_mdc, params, x=left_k)
                    fit = result.best_fit
                    ax.plot(left_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (left)', color=f'C{i}')

                # Fit right side
                if np.any(right_mask):
                    right_k = k_parallel[right_mask]
                    right_mdc = mdc[right_mask]
                    peaks, _ = find_peaks(right_mdc)
                    peak_heights = right_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(right_mdc))

                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'r{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'r{j+1}_center'].set(value=right_k[peak], min=right_k.min(), max=right_k.max())
                        params[f'r{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'r{j+1}_sigma'].set(value=0.1, min=0)

                    result = model.fit(right_mdc, params, x=right_k)
                    fit = result.best_fit
                    ax.plot(right_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (right)', color=f'C{i}')

        ax.set_xlabel(r'$k_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

        display(fig)
        plt.close(fig)

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        fig, _ = plot_mdc(**current_values)
        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        _, max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            fig, _ = plot_mdc(interactive_plot.children[0].value,
                              interactive_plot.children[1].value,
                              interactive_plot.children[2].value,
                              interactive_plot.children[3].value,
                              interactive_plot.children[4].value,
                              interactive_plot.children[5].value)
            display(fig)
            plt.close(fig)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_energy_min = float('inf')
    global_energy_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global energy range
        global_energy_min = min(global_energy_min, np.min(energy_values))
        global_energy_max = max(global_energy_max, np.max(energy_values))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Calculate equally spaced energy indices
        energy_indices = np.linspace(0, len(plot_data['energy_values']) - 1, n, dtype=int)
        
        max_intensity = 0  # Initialize max intensity for the displayed curves
        
        for i, energy_index in enumerate(energy_indices):
            actual_energy = plot_data['energy_values'][energy_index]
            mdc = plot_data['data_values'][energy_index, :]
            k_parallel = plot_data['k_parallel'][energy_index, :]
            
            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values
            
            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))
            
            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(k_parallel, mdc + i * vertical_offset, label=f'E = {actual_energy:.2f} eV')
            
            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Split data into left and right sides
                left_mask = k_parallel < 0
                right_mask = k_parallel > 0
                
                # Fit left side
                if np.any(left_mask):
                    left_k = k_parallel[left_mask]
                    left_mdc = mdc[left_mask]
                    peaks, _ = find_peaks(left_mdc)
                    peak_heights = left_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]
                    
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(left_mdc))
                    
                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'l{j+1}_center'].set(value=left_k[peak], min=left_k.min(), max=left_k.max())
                        params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'l{j+1}_sigma'].set(value=0.1, min=0)
                    
                    result = model.fit(left_mdc, params, x=left_k)
                    fit = result.best_fit
                    ax.plot(left_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (left)', color=f'C{i}')
                
                # Fit right side
                if np.any(right_mask):
                    right_k = k_parallel[right_mask]
                    right_mdc = mdc[right_mask]
                    peaks, _ = find_peaks(right_mdc)
                    peak_heights = right_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]
                    
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(right_mdc))
                    
                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'r{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'r{j+1}_center'].set(value=right_k[peak], min=right_k.min(), max=right_k.max())
                        params[f'r{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'r{j+1}_sigma'].set(value=0.1, min=0)
                    
                    result = model.fit(right_mdc, params, x=right_k)
                    fit = result.best_fit
                    ax.plot(right_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (right)', color=f'C{i}')
        
        ax.set_xlabel(r'$k_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig, max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        fig, _ = plot_mdc(**current_values)

        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        _, max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_mdc=Checkbox(value=True, description='Show MDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            fig, _ = plot_mdc(interactive_plot.children[0].value,
                              interactive_plot.children[1].value,
                              interactive_plot.children[2].value,
                              interactive_plot.children[3].value,
                              interactive_plot.children[4].value,
                              interactive_plot.children[5].value)
            display(fig)
            plt.close(fig)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.0": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display, clear_output\nimport tkinter as tk\nfrom tkinter import filedialog\nimport warnings\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nfrom matplotlib import MatplotlibDeprecationWarning\nfrom lmfit.models import LorentzianModel, LinearModel\nfrom scipy.signal import find_peaks\n%\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",  # Set default text color to black\n    \"axes.labelcolor\": \"black\",  # Set default axes label color to black\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\n\n# Suppress specific warnings\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef mdc_plot(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    global_energy_min = float('inf')\n    global_energy_max = float('-inf')\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']  # Photon energy from the attributes\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        # Update global energy range\n        global_energy_min = min(global_energy_min, np.min(energy_values))\n        global_energy_max = max(global_energy_max, np.max(energy_values))\n\n    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):\n        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n        fig, ax = plt.subplots(figsize=(10, 6))\n        \n        # Calculate equally spaced energy indices\n        energy_indices = np.linspace(0, len(plot_data['energy_values']) - 1, n, dtype=int)\n        \n        max_intensity = 0  # Initialize max intensity for the displayed curves\n        \n        for i, energy_index in enumerate(energy_indices):\n            actual_energy = plot_data['energy_values'][energy_index]\n            mdc = plot_data['data_values'][energy_index, :]\n            k_parallel = plot_data['k_parallel'][energy_index, :]\n            \n            # Convert to numpy arrays if they are xarray.DataArray\n            if isinstance(mdc, xr.DataArray):\n                mdc = mdc.values\n            if isinstance(k_parallel, xr.DataArray):\n                k_parallel = k_parallel.values\n            \n            # Update max intensity\n            max_intensity = max(max_intensity, np.max(mdc))\n            \n            # Plot MDC with vertical offset\n            if show_mdc:\n                ax.plot(k_parallel, mdc + i * vertical_offset, label=f'E = {actual_energy:.2f} eV')\n            \n            # Fit MDC with multiple Lorentzian peaks + Linear background\n            if show_fit:\n                # Split data into left and right sides\n                left_mask = k_parallel < 0\n                right_mask = k_parallel > 0\n                \n                # Fit left side\n                if np.any(left_mask):\n                    left_k = k_parallel[left_mask]\n                    left_mdc = mdc[left_mask]\n                    peaks, _ = find_peaks(left_mdc)\n                    peak_heights = left_mdc[peaks]\n                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks\n                    largest_peaks = peaks[sorted_indices]\n                    \n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(left_mdc))\n                    \n                    for j, peak in enumerate(largest_peaks):\n                        lorentzian = LorentzianModel(prefix=f'l{j+1}_')\n                        model += lorentzian\n                        params.update(lorentzian.make_params())\n                        params[f'l{j+1}_center'].set(value=left_k[peak], min=left_k.min(), max=left_k.max())\n                        params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                        params[f'l{j+1}_sigma'].set(value=0.1, min=0)\n                    \n                    result = model.fit(left_mdc, params, x=left_k)\n                    fit = result.best_fit\n                    ax.plot(left_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (left)', color=f'C{i}')\n                \n                # Fit right side\n                if np.any(right_mask):\n                    right_k = k_parallel[right_mask]\n                    right_mdc = mdc[right_mask]\n                    peaks, _ = find_peaks(right_mdc)\n                    peak_heights = right_mdc[peaks]\n                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks\n                    largest_peaks = peaks[sorted_indices]\n                    \n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(right_mdc))\n                    \n                    for j, peak in enumerate(largest_peaks):\n                        lorentzian = LorentzianModel(prefix=f'r{j+1}_')\n                        model += lorentzian\n                        params.update(lorentzian.make_params())\n                        params[f'r{j+1}_center'].set(value=right_k[peak], min=right_k.min(), max=right_k.max())\n                        params[f'r{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                        params[f'r{j+1}_sigma'].set(value=0.1, min=0)\n                    \n                    result = model.fit(right_mdc, params, x=right_k)\n                    fit = result.best_fit\n                    ax.plot(right_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (right)', color=f'C{i}')\n        \n        ax.set_xlabel(r'$k_\\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')\n        ax.set_title(f'MDCs - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.legend()\n        \n        # Adjust tick label font size\n        ax.tick_params(axis='both', which='major', labelsize=10)\n        \n        plt.tight_layout()\n        return fig, max_intensity\n\n    def save_plot(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_mdc': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value\n        }\n\n        # Generate the plot with current values\n        fig, _ = plot_mdc(**current_values)\n\n        # Save the plot\n        filename = f\"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        plt.close(fig)\n        print(f\"Plot saved as {filename}\")\n\n    def update_vertical_offset_range(*args):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_mdc': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value\n        }\n\n        _, max_intensity = plot_mdc(**current_values)\n        interactive_plot.children[2].max = current_values['n'] * max_intensity\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_mdc,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),\n        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),\n        show_mdc=Checkbox(value=True, description='Show MDCs'),\n        show_fit=Checkbox(value=False, description='Show Fits'),\n        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))\n\n    # Update the vertical offset range when the number of MDCs or scan index changes\n    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')\n    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')\n\n    # Create an output widget to capture the plot\n    out = Output()\n\n    # Function to display the plot in the output widget\n    def display_plot(*args):\n        with out:\n            clear_output(wait=True)\n            fig, _ = plot_mdc(interactive_plot.children[0].value,\n                              interactive_plot.children[1].value,\n                              interactive_plot.children[2].value,\n                              interactive_plot.children[3].value,\n                              interactive_plot.children[4].value,\n                              interactive_plot.children[5].value)\n            display(fig)\n            plt.close(fig)\n\n    # Observe changes in the interactive plot and update the output widget\n    interactive_plot.observe(display_plot, names='value')\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    # Combine the interactive plot, output widget, and the save button\n    output = VBox([interactive_plot, out, save_button])\n\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()  # Hide the root window\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()  # Destroy the root window\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n    interactive_plot_with_save = mdc_plot(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")\n", 605)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.0.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.0.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.1": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display, clear_output\nimport tkinter as tk\nfrom tkinter import filedialog\nimport warnings\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nfrom matplotlib import MatplotlibDeprecationWarning\nfrom lmfit.models import LorentzianModel, LinearModel\nfrom scipy.signal import find_peaks\n%matplotlib w\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",  # Set default text color to black\n    \"axes.labelcolor\": \"black\",  # Set default axes label color to black\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\n\n# Suppress specific warnings\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef mdc_plot(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    global_energy_min = float('inf')\n    global_energy_max = float('-inf')\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']  # Photon energy from the attributes\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        # Update global energy range\n        global_energy_min = min(global_energy_min, np.min(energy_values))\n        global_energy_max = max(global_energy_max, np.max(energy_values))\n\n    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):\n        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n        fig, ax = plt.subplots(figsize=(10, 6))\n        \n        # Calculate equally spaced energy indices\n        energy_indices = np.linspace(0, len(plot_data['energy_values']) - 1, n, dtype=int)\n        \n        max_intensity = 0  # Initialize max intensity for the displayed curves\n        \n        for i, energy_index in enumerate(energy_indices):\n            actual_energy = plot_data['energy_values'][energy_index]\n            mdc = plot_data['data_values'][energy_index, :]\n            k_parallel = plot_data['k_parallel'][energy_index, :]\n            \n            # Convert to numpy arrays if they are xarray.DataArray\n            if isinstance(mdc, xr.DataArray):\n                mdc = mdc.values\n            if isinstance(k_parallel, xr.DataArray):\n                k_parallel = k_parallel.values\n            \n            # Update max intensity\n            max_intensity = max(max_intensity, np.max(mdc))\n            \n            # Plot MDC with vertical offset\n            if show_mdc:\n                ax.plot(k_parallel, mdc + i * vertical_offset, label=f'E = {actual_energy:.2f} eV')\n            \n            # Fit MDC with multiple Lorentzian peaks + Linear background\n            if show_fit:\n                # Split data into left and right sides\n                left_mask = k_parallel < 0\n                right_mask = k_parallel > 0\n                \n                # Fit left side\n                if np.any(left_mask):\n                    left_k = k_parallel[left_mask]\n                    left_mdc = mdc[left_mask]\n                    peaks, _ = find_peaks(left_mdc)\n                    peak_heights = left_mdc[peaks]\n                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks\n                    largest_peaks = peaks[sorted_indices]\n                    \n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(left_mdc))\n                    \n                    for j, peak in enumerate(largest_peaks):\n                        lorentzian = LorentzianModel(prefix=f'l{j+1}_')\n                        model += lorentzian\n                        params.update(lorentzian.make_params())\n                        params[f'l{j+1}_center'].set(value=left_k[peak], min=left_k.min(), max=left_k.max())\n                        params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                        params[f'l{j+1}_sigma'].set(value=0.1, min=0)\n                    \n                    result = model.fit(left_mdc, params, x=left_k)\n                    fit = result.best_fit\n                    ax.plot(left_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (left)', color=f'C{i}')\n                \n                # Fit right side\n                if np.any(right_mask):\n                    right_k = k_parallel[right_mask]\n                    right_mdc = mdc[right_mask]\n                    peaks, _ = find_peaks(right_mdc)\n                    peak_heights = right_mdc[peaks]\n                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks\n                    largest_peaks = peaks[sorted_indices]\n                    \n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(right_mdc))\n                    \n                    for j, peak in enumerate(largest_peaks):\n                        lorentzian = LorentzianModel(prefix=f'r{j+1}_')\n                        model += lorentzian\n                        params.update(lorentzian.make_params())\n                        params[f'r{j+1}_center'].set(value=right_k[peak], min=right_k.min(), max=right_k.max())\n                        params[f'r{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                        params[f'r{j+1}_sigma'].set(value=0.1, min=0)\n                    \n                    result = model.fit(right_mdc, params, x=right_k)\n                    fit = result.best_fit\n                    ax.plot(right_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (right)', color=f'C{i}')\n        \n        ax.set_xlabel(r'$k_\\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')\n        ax.set_title(f'MDCs - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.legend()\n        \n        # Adjust tick label font size\n        ax.tick_params(axis='both', which='major', labelsize=10)\n        \n        plt.tight_layout()\n        return fig, max_intensity\n\n    def save_plot(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_mdc': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value\n        }\n\n        # Generate the plot with current values\n        fig, _ = plot_mdc(**current_values)\n\n        # Save the plot\n        filename = f\"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        plt.close(fig)\n        print(f\"Plot saved as {filename}\")\n\n    def update_vertical_offset_range(*args):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_mdc': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value\n        }\n\n        _, max_intensity = plot_mdc(**current_values)\n        interactive_plot.children[2].max = current_values['n'] * max_intensity\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_mdc,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),\n        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),\n        show_mdc=Checkbox(value=True, description='Show MDCs'),\n        show_fit=Checkbox(value=False, description='Show Fits'),\n        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))\n\n    # Update the vertical offset range when the number of MDCs or scan index changes\n    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')\n    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')\n\n    # Create an output widget to capture the plot\n    out = Output()\n\n    # Function to display the plot in the output widget\n    def display_plot(*args):\n        with out:\n            clear_output(wait=True)\n            fig, _ = plot_mdc(interactive_plot.children[0].value,\n                              interactive_plot.children[1].value,\n                              interactive_plot.children[2].value,\n                              interactive_plot.children[3].value,\n                              interactive_plot.children[4].value,\n                              interactive_plot.children[5].value)\n            display(fig)\n            plt.close(fig)\n\n    # Observe changes in the interactive plot and update the output widget\n    interactive_plot.observe(display_plot, names='value')\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    # Combine the interactive plot, output widget, and the save button\n    output = VBox([interactive_plot, out, save_button])\n\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()  # Hide the root window\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()  # Destroy the root window\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n    interactive_plot_with_save = mdc_plot(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")\n", 617)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.1.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.1.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.2": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("import os\nimwith", 16, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.2.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.2.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.3": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display, clear_output\nimport tkinter as tk\nfrom tkinter import filedialog\nimport warnings\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nfrom matplotlib import MatplotlibDeprecationWarning\nfrom lmfit.models import LorentzianModel, LinearModel\nfrom scipy.signal import find_peaks\n%matplotlib wid\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",  # Set default text color to black\n    \"axes.labelcolor\": \"black\",  # Set default axes label color to black\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\n\n# Suppress specific warnings\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef mdc_plot(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    global_energy_min = float('inf')\n    global_energy_max = float('-inf')\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']  # Photon energy from the attributes\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        # Update global energy range\n        global_energy_min = min(global_energy_min, np.min(energy_values))\n        global_energy_max = max(global_energy_max, np.max(energy_values))\n\n    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):\n        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n        fig, ax = plt.subplots(figsize=(10, 6))\n        \n        # Calculate equally spaced energy indices\n        energy_indices = np.linspace(0, len(plot_data['energy_values']) - 1, n, dtype=int)\n        \n        max_intensity = 0  # Initialize max intensity for the displayed curves\n        \n        for i, energy_index in enumerate(energy_indices):\n            actual_energy = plot_data['energy_values'][energy_index]\n            mdc = plot_data['data_values'][energy_index, :]\n            k_parallel = plot_data['k_parallel'][energy_index, :]\n            \n            # Convert to numpy arrays if they are xarray.DataArray\n            if isinstance(mdc, xr.DataArray):\n                mdc = mdc.values\n            if isinstance(k_parallel, xr.DataArray):\n                k_parallel = k_parallel.values\n            \n            # Update max intensity\n            max_intensity = max(max_intensity, np.max(mdc))\n            \n            # Plot MDC with vertical offset\n            if show_mdc:\n                ax.plot(k_parallel, mdc + i * vertical_offset, label=f'E = {actual_energy:.2f} eV')\n            \n            # Fit MDC with multiple Lorentzian peaks + Linear background\n            if show_fit:\n                # Split data into left and right sides\n                left_mask = k_parallel < 0\n                right_mask = k_parallel > 0\n                \n                # Fit left side\n                if np.any(left_mask):\n                    left_k = k_parallel[left_mask]\n                    left_mdc = mdc[left_mask]\n                    peaks, _ = find_peaks(left_mdc)\n                    peak_heights = left_mdc[peaks]\n                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks\n                    largest_peaks = peaks[sorted_indices]\n                    \n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(left_mdc))\n                    \n                    for j, peak in enumerate(largest_peaks):\n                        lorentzian = LorentzianModel(prefix=f'l{j+1}_')\n                        model += lorentzian\n                        params.update(lorentzian.make_params())\n                        params[f'l{j+1}_center'].set(value=left_k[peak], min=left_k.min(), max=left_k.max())\n                        params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                        params[f'l{j+1}_sigma'].set(value=0.1, min=0)\n                    \n                    result = model.fit(left_mdc, params, x=left_k)\n                    fit = result.best_fit\n                    ax.plot(left_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (left)', color=f'C{i}')\n                \n                # Fit right side\n                if np.any(right_mask):\n                    right_k = k_parallel[right_mask]\n                    right_mdc = mdc[right_mask]\n                    peaks, _ = find_peaks(right_mdc)\n                    peak_heights = right_mdc[peaks]\n                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks\n                    largest_peaks = peaks[sorted_indices]\n                    \n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(right_mdc))\n                    \n                    for j, peak in enumerate(largest_peaks):\n                        lorentzian = LorentzianModel(prefix=f'r{j+1}_')\n                        model += lorentzian\n                        params.update(lorentzian.make_params())\n                        params[f'r{j+1}_center'].set(value=right_k[peak], min=right_k.min(), max=right_k.max())\n                        params[f'r{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                        params[f'r{j+1}_sigma'].set(value=0.1, min=0)\n                    \n                    result = model.fit(right_mdc, params, x=right_k)\n                    fit = result.best_fit\n                    ax.plot(right_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (right)', color=f'C{i}')\n        \n        ax.set_xlabel(r'$k_\\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')\n        ax.set_title(f'MDCs - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.legend()\n        \n        # Adjust tick label font size\n        ax.tick_params(axis='both', which='major', labelsize=10)\n        \n        plt.tight_layout()\n        return fig, max_intensity\n\n    def save_plot(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_mdc': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value\n        }\n\n        # Generate the plot with current values\n        fig, _ = plot_mdc(**current_values)\n\n        # Save the plot\n        filename = f\"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        plt.close(fig)\n        print(f\"Plot saved as {filename}\")\n\n    def update_vertical_offset_range(*args):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_mdc': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value\n        }\n\n        _, max_intensity = plot_mdc(**current_values)\n        interactive_plot.children[2].max = current_values['n'] * max_intensity\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_mdc,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),\n        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),\n        show_mdc=Checkbox(value=True, description='Show MDCs'),\n        show_fit=Checkbox(value=False, description='Show Fits'),\n        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))\n\n    # Update the vertical offset range when the number of MDCs or scan index changes\n    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')\n    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')\n\n    # Create an output widget to capture the plot\n    out = Output()\n\n    # Function to display the plot in the output widget\n    def display_plot(*args):\n        with out:\n            clear_output(wait=True)\n            fig, _ = plot_mdc(interactive_plot.children[0].value,\n                              interactive_plot.children[1].value,\n                              interactive_plot.children[2].value,\n                              interactive_plot.children[3].value,\n                              interactive_plot.children[4].value,\n                              interactive_plot.children[5].value)\n            display(fig)\n            plt.close(fig)\n\n    # Observe changes in the interactive plot and update the output widget\n    interactive_plot.observe(display_plot, names='value')\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    # Combine the interactive plot, output widget, and the save button\n    output = VBox([interactive_plot, out, save_button])\n\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()  # Hide the root window\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()  # Destroy the root window\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n    interactive_plot_with_save = mdc_plot(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")\n", 619)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.3.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.3.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.4": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display, clear_output\nimport tkinter as tk\nfrom tkinter import filedialog\nimport warnings\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nfrom matplotlib import MatplotlibDeprecationWarning\nfrom lmfit.models import LorentzianModel, LinearModel\nfrom scipy.signal import find_peaks\n%matplotlib widg\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",  # Set default text color to black\n    \"axes.labelcolor\": \"black\",  # Set default axes label color to black\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\n\n# Suppress specific warnings\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef mdc_plot(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    global_energy_min = float('inf')\n    global_energy_max = float('-inf')\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']  # Photon energy from the attributes\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        # Update global energy range\n        global_energy_min = min(global_energy_min, np.min(energy_values))\n        global_energy_max = max(global_energy_max, np.max(energy_values))\n\n    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):\n        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n        fig, ax = plt.subplots(figsize=(10, 6))\n        \n        # Calculate equally spaced energy indices\n        energy_indices = np.linspace(0, len(plot_data['energy_values']) - 1, n, dtype=int)\n        \n        max_intensity = 0  # Initialize max intensity for the displayed curves\n        \n        for i, energy_index in enumerate(energy_indices):\n            actual_energy = plot_data['energy_values'][energy_index]\n            mdc = plot_data['data_values'][energy_index, :]\n            k_parallel = plot_data['k_parallel'][energy_index, :]\n            \n            # Convert to numpy arrays if they are xarray.DataArray\n            if isinstance(mdc, xr.DataArray):\n                mdc = mdc.values\n            if isinstance(k_parallel, xr.DataArray):\n                k_parallel = k_parallel.values\n            \n            # Update max intensity\n            max_intensity = max(max_intensity, np.max(mdc))\n            \n            # Plot MDC with vertical offset\n            if show_mdc:\n                ax.plot(k_parallel, mdc + i * vertical_offset, label=f'E = {actual_energy:.2f} eV')\n            \n            # Fit MDC with multiple Lorentzian peaks + Linear background\n            if show_fit:\n                # Split data into left and right sides\n                left_mask = k_parallel < 0\n                right_mask = k_parallel > 0\n                \n                # Fit left side\n                if np.any(left_mask):\n                    left_k = k_parallel[left_mask]\n                    left_mdc = mdc[left_mask]\n                    peaks, _ = find_peaks(left_mdc)\n                    peak_heights = left_mdc[peaks]\n                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks\n                    largest_peaks = peaks[sorted_indices]\n                    \n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(left_mdc))\n                    \n                    for j, peak in enumerate(largest_peaks):\n                        lorentzian = LorentzianModel(prefix=f'l{j+1}_')\n                        model += lorentzian\n                        params.update(lorentzian.make_params())\n                        params[f'l{j+1}_center'].set(value=left_k[peak], min=left_k.min(), max=left_k.max())\n                        params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                        params[f'l{j+1}_sigma'].set(value=0.1, min=0)\n                    \n                    result = model.fit(left_mdc, params, x=left_k)\n                    fit = result.best_fit\n                    ax.plot(left_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (left)', color=f'C{i}')\n                \n                # Fit right side\n                if np.any(right_mask):\n                    right_k = k_parallel[right_mask]\n                    right_mdc = mdc[right_mask]\n                    peaks, _ = find_peaks(right_mdc)\n                    peak_heights = right_mdc[peaks]\n                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks\n                    largest_peaks = peaks[sorted_indices]\n                    \n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(right_mdc))\n                    \n                    for j, peak in enumerate(largest_peaks):\n                        lorentzian = LorentzianModel(prefix=f'r{j+1}_')\n                        model += lorentzian\n                        params.update(lorentzian.make_params())\n                        params[f'r{j+1}_center'].set(value=right_k[peak], min=right_k.min(), max=right_k.max())\n                        params[f'r{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                        params[f'r{j+1}_sigma'].set(value=0.1, min=0)\n                    \n                    result = model.fit(right_mdc, params, x=right_k)\n                    fit = result.best_fit\n                    ax.plot(right_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (right)', color=f'C{i}')\n        \n        ax.set_xlabel(r'$k_\\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')\n        ax.set_title(f'MDCs - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.legend()\n        \n        # Adjust tick label font size\n        ax.tick_params(axis='both', which='major', labelsize=10)\n        \n        plt.tight_layout()\n        return fig, max_intensity\n\n    def save_plot(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_mdc': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value\n        }\n\n        # Generate the plot with current values\n        fig, _ = plot_mdc(**current_values)\n\n        # Save the plot\n        filename = f\"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        plt.close(fig)\n        print(f\"Plot saved as {filename}\")\n\n    def update_vertical_offset_range(*args):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_mdc': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value\n        }\n\n        _, max_intensity = plot_mdc(**current_values)\n        interactive_plot.children[2].max = current_values['n'] * max_intensity\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_mdc,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),\n        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),\n        show_mdc=Checkbox(value=True, description='Show MDCs'),\n        show_fit=Checkbox(value=False, description='Show Fits'),\n        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))\n\n    # Update the vertical offset range when the number of MDCs or scan index changes\n    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')\n    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')\n\n    # Create an output widget to capture the plot\n    out = Output()\n\n    # Function to display the plot in the output widget\n    def display_plot(*args):\n        with out:\n            clear_output(wait=True)\n            fig, _ = plot_mdc(interactive_plot.children[0].value,\n                              interactive_plot.children[1].value,\n                              interactive_plot.children[2].value,\n                              interactive_plot.children[3].value,\n                              interactive_plot.children[4].value,\n                              interactive_plot.children[5].value)\n            display(fig)\n            plt.close(fig)\n\n    # Observe changes in the interactive plot and update the output widget\n    interactive_plot.observe(display_plot, names='value')\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    # Combine the interactive plot, output widget, and the save button\n    output = VBox([interactive_plot, out, save_button])\n\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()  # Hide the root window\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()  # Destroy the root window\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n    interactive_plot_with_save = mdc_plot(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")\n", 620)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.4.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.4.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.5": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display, clear_output\nimport tkinter as tk\nfrom tkinter import filedialog\nimport warnings\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nfrom matplotlib import MatplotlibDeprecationWarning\nfrom lmfit.models import LorentzianModel, LinearModel\nfrom scipy.signal import find_peaks\n%matplotlib widget\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",  # Set default text color to black\n    \"axes.labelcolor\": \"black\",  # Set default axes label color to black\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\n\n# Suppress specific warnings\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef mdc_plot(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    global_energy_min = float('inf')\n    global_energy_max = float('-inf')\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']  # Photon energy from the attributes\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        # Update global energy range\n        global_energy_min = min(global_energy_min, np.min(energy_values))\n        global_energy_max = max(global_energy_max, np.max(energy_values))\n\n    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):\n        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n        fig, ax = plt.subplots(figsize=(10, 6))\n        \n        # Calculate equally spaced energy indices\n        energy_indices = np.linspace(0, len(plot_data['energy_values']) - 1, n, dtype=int)\n        \n        max_intensity = 0  # Initialize max intensity for the displayed curves\n        \n        for i, energy_index in enumerate(energy_indices):\n            actual_energy = plot_data['energy_values'][energy_index]\n            mdc = plot_data['data_values'][energy_index, :]\n            k_parallel = plot_data['k_parallel'][energy_index, :]\n            \n            # Convert to numpy arrays if they are xarray.DataArray\n            if isinstance(mdc, xr.DataArray):\n                mdc = mdc.values\n            if isinstance(k_parallel, xr.DataArray):\n                k_parallel = k_parallel.values\n            \n            # Update max intensity\n            max_intensity = max(max_intensity, np.max(mdc))\n            \n            # Plot MDC with vertical offset\n            if show_mdc:\n                ax.plot(k_parallel, mdc + i * vertical_offset, label=f'E = {actual_energy:.2f} eV')\n            \n            # Fit MDC with multiple Lorentzian peaks + Linear background\n            if show_fit:\n                # Split data into left and right sides\n                left_mask = k_parallel < 0\n                right_mask = k_parallel > 0\n                \n                # Fit left side\n                if np.any(left_mask):\n                    left_k = k_parallel[left_mask]\n                    left_mdc = mdc[left_mask]\n                    peaks, _ = find_peaks(left_mdc)\n                    peak_heights = left_mdc[peaks]\n                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks\n                    largest_peaks = peaks[sorted_indices]\n                    \n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(left_mdc))\n                    \n                    for j, peak in enumerate(largest_peaks):\n                        lorentzian = LorentzianModel(prefix=f'l{j+1}_')\n                        model += lorentzian\n                        params.update(lorentzian.make_params())\n                        params[f'l{j+1}_center'].set(value=left_k[peak], min=left_k.min(), max=left_k.max())\n                        params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                        params[f'l{j+1}_sigma'].set(value=0.1, min=0)\n                    \n                    result = model.fit(left_mdc, params, x=left_k)\n                    fit = result.best_fit\n                    ax.plot(left_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (left)', color=f'C{i}')\n                \n                # Fit right side\n                if np.any(right_mask):\n                    right_k = k_parallel[right_mask]\n                    right_mdc = mdc[right_mask]\n                    peaks, _ = find_peaks(right_mdc)\n                    peak_heights = right_mdc[peaks]\n                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks\n                    largest_peaks = peaks[sorted_indices]\n                    \n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(right_mdc))\n                    \n                    for j, peak in enumerate(largest_peaks):\n                        lorentzian = LorentzianModel(prefix=f'r{j+1}_')\n                        model += lorentzian\n                        params.update(lorentzian.make_params())\n                        params[f'r{j+1}_center'].set(value=right_k[peak], min=right_k.min(), max=right_k.max())\n                        params[f'r{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                        params[f'r{j+1}_sigma'].set(value=0.1, min=0)\n                    \n                    result = model.fit(right_mdc, params, x=right_k)\n                    fit = result.best_fit\n                    ax.plot(right_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (right)', color=f'C{i}')\n        \n        ax.set_xlabel(r'$k_\\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')\n        ax.set_title(f'MDCs - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.legend()\n        \n        # Adjust tick label font size\n        ax.tick_params(axis='both', which='major', labelsize=10)\n        \n        plt.tight_layout()\n        return fig, max_intensity\n\n    def save_plot(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_mdc': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value\n        }\n\n        # Generate the plot with current values\n        fig, _ = plot_mdc(**current_values)\n\n        # Save the plot\n        filename = f\"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        plt.close(fig)\n        print(f\"Plot saved as {filename}\")\n\n    def update_vertical_offset_range(*args):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_mdc': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value\n        }\n\n        _, max_intensity = plot_mdc(**current_values)\n        interactive_plot.children[2].max = current_values['n'] * max_intensity\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_mdc,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),\n        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),\n        show_mdc=Checkbox(value=True, description='Show MDCs'),\n        show_fit=Checkbox(value=False, description='Show Fits'),\n        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))\n\n    # Update the vertical offset range when the number of MDCs or scan index changes\n    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')\n    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')\n\n    # Create an output widget to capture the plot\n    out = Output()\n\n    # Function to display the plot in the output widget\n    def display_plot(*args):\n        with out:\n            clear_output(wait=True)\n            fig, _ = plot_mdc(interactive_plot.children[0].value,\n                              interactive_plot.children[1].value,\n                              interactive_plot.children[2].value,\n                              interactive_plot.children[3].value,\n                              interactive_plot.children[4].value,\n                              interactive_plot.children[5].value)\n            display(fig)\n            plt.close(fig)\n\n    # Observe changes in the interactive plot and update the output widget\n    interactive_plot.observe(display_plot, names='value')\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    # Combine the interactive plot, output widget, and the save button\n    output = VBox([interactive_plot, out, save_button])\n\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()  # Hide the root window\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()  # Destroy the root window\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n    interactive_plot_with_save = mdc_plot(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")\n", 622)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.5.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.5.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
get_ipython().run_line_magic('matplotlib', 'widget')
if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_energy_min = float('inf')
    global_energy_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global energy range
        global_energy_min = min(global_energy_min, np.min(energy_values))
        global_energy_max = max(global_energy_max, np.max(energy_values))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Calculate equally spaced energy indices
        energy_indices = np.linspace(0, len(plot_data['energy_values']) - 1, n, dtype=int)
        
        max_intensity = 0  # Initialize max intensity for the displayed curves
        
        for i, energy_index in enumerate(energy_indices):
            actual_energy = plot_data['energy_values'][energy_index]
            mdc = plot_data['data_values'][energy_index, :]
            k_parallel = plot_data['k_parallel'][energy_index, :]
            
            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values
            
            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))
            
            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(k_parallel, mdc + i * vertical_offset, label=f'E = {actual_energy:.2f} eV')
            
            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Split data into left and right sides
                left_mask = k_parallel < 0
                right_mask = k_parallel > 0
                
                # Fit left side
                if np.any(left_mask):
                    left_k = k_parallel[left_mask]
                    left_mdc = mdc[left_mask]
                    peaks, _ = find_peaks(left_mdc)
                    peak_heights = left_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]
                    
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(left_mdc))
                    
                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'l{j+1}_center'].set(value=left_k[peak], min=left_k.min(), max=left_k.max())
                        params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'l{j+1}_sigma'].set(value=0.1, min=0)
                    
                    result = model.fit(left_mdc, params, x=left_k)
                    fit = result.best_fit
                    ax.plot(left_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (left)', color=f'C{i}')
                
                # Fit right side
                if np.any(right_mask):
                    right_k = k_parallel[right_mask]
                    right_mdc = mdc[right_mask]
                    peaks, _ = find_peaks(right_mdc)
                    peak_heights = right_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]
                    
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(right_mdc))
                    
                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'r{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'r{j+1}_center'].set(value=right_k[peak], min=right_k.min(), max=right_k.max())
                        params[f'r{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'r{j+1}_sigma'].set(value=0.1, min=0)
                    
                    result = model.fit(right_mdc, params, x=right_k)
                    fit = result.best_fit
                    ax.plot(right_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (right)', color=f'C{i}')
        
        ax.set_xlabel(r'$k_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig, max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        fig, _ = plot_mdc(**current_values)

        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        _, max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_mdc=Checkbox(value=True, description='Show MDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            fig, _ = plot_mdc(interactive_plot.children[0].value,
                              interactive_plot.children[1].value,
                              interactive_plot.children[2].value,
                              interactive_plot.children[3].value,
                              interactive_plot.children[4].value,
                              interactive_plot.children[5].value)
            display(fig)
            plt.close(fig)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
get_ipython().run_line_magic('matplotlib', 'widget')
if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_energy_min = float('inf')
    global_energy_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global energy range
        global_energy_min = min(global_energy_min, np.min(energy_values))
        global_energy_max = max(global_energy_max, np.max(energy_values))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # Calculate equally spaced energy indices
        energy_indices = np.linspace(0, len(plot_data['energy_values']) - 1, n, dtype=int)
        
        max_intensity = 0  # Initialize max intensity for the displayed curves
        
        for i, energy_index in enumerate(energy_indices):
            actual_energy = plot_data['energy_values'][energy_index]
            mdc = plot_data['data_values'][energy_index, :]
            k_parallel = plot_data['k_parallel'][energy_index, :]
            
            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values
            
            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))
            
            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(k_parallel, mdc + i * vertical_offset, label=f'E = {actual_energy:.2f} eV')
            
            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Split data into left and right sides
                left_mask = k_parallel < 0
                right_mask = k_parallel > 0
                
                # Fit left side
                if np.any(left_mask):
                    left_k = k_parallel[left_mask]
                    left_mdc = mdc[left_mask]
                    peaks, _ = find_peaks(left_mdc)
                    peak_heights = left_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]
                    
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(left_mdc))
                    
                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'l{j+1}_center'].set(value=left_k[peak], min=left_k.min(), max=left_k.max())
                        params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'l{j+1}_sigma'].set(value=0.1, min=0)
                    
                    result = model.fit(left_mdc, params, x=left_k)
                    fit = result.best_fit
                    ax.plot(left_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (left)', color=f'C{i}')
                
                # Fit right side
                if np.any(right_mask):
                    right_k = k_parallel[right_mask]
                    right_mdc = mdc[right_mask]
                    peaks, _ = find_peaks(right_mdc)
                    peak_heights = right_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]
                    
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(right_mdc))
                    
                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'r{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'r{j+1}_center'].set(value=right_k[peak], min=right_k.min(), max=right_k.max())
                        params[f'r{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'r{j+1}_sigma'].set(value=0.1, min=0)
                    
                    result = model.fit(right_mdc, params, x=right_k)
                    fit = result.best_fit
                    ax.plot(right_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (right)', color=f'C{i}')
        
        ax.set_xlabel(r'$k_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig, max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        fig, _ = plot_mdc(**current_values)

        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        _, max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_mdc=Checkbox(value=True, description='Show MDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            fig, _ = plot_mdc(interactive_plot.children[0].value,
                              interactive_plot.children[1].value,
                              interactive_plot.children[2].value,
                              interactive_plot.children[3].value,
                              interactive_plot.children[4].value,
                              interactive_plot.children[5].value)
            display(fig)
            plt.close(fig)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_energy_min = float('inf')
    global_energy_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global energy range
        global_energy_min = min(global_energy_min, np.min(energy_values))
        global_energy_max = max(global_energy_max, np.max(energy_values))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced energy indices
        energy_indices = np.linspace(0, len(plot_data['energy_values']) - 1, n, dtype=int)
        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, energy_index in enumerate(energy_indices):
            actual_energy = plot_data['energy_values'][energy_index]
            mdc = plot_data['data_values'][energy_index, :]
            k_parallel = plot_data['k_parallel'][energy_index, :]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(k_parallel, mdc + i * vertical_offset, label=f'E = {actual_energy:.2f} eV')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Split data into left and right sides
                left_mask = k_parallel < 0
                right_mask = k_parallel > 0

                # Fit left side
                if np.any(left_mask):
                    left_k = k_parallel[left_mask]
                    left_mdc = mdc[left_mask]
                    peaks, _ = find_peaks(left_mdc)
                    peak_heights = left_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(left_mdc))

                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'l{j+1}_center'].set(value=left_k[peak], min=left_k.min(), max=left_k.max())
                        params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                    result = model.fit(left_mdc, params, x=left_k)
                    fit = result.best_fit
                    ax.plot(left_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (left)', color=f'C{i}')

                # Fit right side
                if np.any(right_mask):
                    right_k = k_parallel[right_mask]
                    right_mdc = mdc[right_mask]
                    peaks, _ = find_peaks(right_mdc)
                    peak_heights = right_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(right_mdc))

                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'r{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'r{j+1}_center'].set(value=right_k[peak], min=right_k.min(), max=right_k.max())
                        params[f'r{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'r{j+1}_sigma'].set(value=0.1, min=0)

                    result = model.fit(right_mdc, params, x=right_k)
                    fit = result.best_fit
                    ax.plot(right_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (right)', color=f'C{i}')

        ax.set_xlabel(r'$k_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

        fig.canvas.draw_idle()  # Update the figure

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_mdc(**current_values)

        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_mdc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_energy_min = float('inf')
    global_energy_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global energy range
        global_energy_min = min(global_energy_min, np.min(energy_values))
        global_energy_max = max(global_energy_max, np.max(energy_values))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced energy indices
        energy_indices = np.linspace(0, len(plot_data['energy_values']) - 1, n, dtype=int)
        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, energy_index in enumerate(energy_indices):
            actual_energy = plot_data['energy_values'][energy_index]
            mdc = plot_data['data_values'][energy_index, :]
            k_parallel = plot_data['k_parallel'][energy_index, :]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(k_parallel, mdc + i * vertical_offset, label=f'E = {actual_energy:.2f} eV')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Split data into left and right sides
                left_mask = k_parallel < 0
                right_mask = k_parallel > 0

                # Fit left side
                if np.any(left_mask):
                    left_k = k_parallel[left_mask]
                    left_mdc = mdc[left_mask]
                    peaks, _ = find_peaks(left_mdc)
                    peak_heights = left_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(left_mdc))

                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'l{j+1}_center'].set(value=left_k[peak], min=left_k.min(), max=left_k.max())
                        params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                    result = model.fit(left_mdc, params, x=left_k)
                    fit = result.best_fit
                    ax.plot(left_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (left)', color=f'C{i}')

                # Fit right side
                if np.any(right_mask):
                    right_k = k_parallel[right_mask]
                    right_mdc = mdc[right_mask]
                    peaks, _ = find_peaks(right_mdc)
                    peak_heights = right_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(right_mdc))

                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'r{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'r{j+1}_center'].set(value=right_k[peak], min=right_k.min(), max=right_k.max())
                        params[f'r{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'r{j+1}_sigma'].set(value=0.1, min=0)

                    result = model.fit(right_mdc, params, x=right_k)
                    fit = result.best_fit
                    ax.plot(right_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (right)', color=f'C{i}')

        ax.set_xlabel(r'$k_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_mdc(**current_values)

        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_mdc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
