# IPython log file

from arpes.io import *
from arpes.endstations.plugin.merlin import *
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = np.complex128
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL403')
from arpes.endstations.plugin.igor_plugin import IgorEndstation

import os

file_path = '/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S009.pxt'
print(f"File exists: {os.path.exists(file_path)}")
print(f"File is readable: {os.access(file_path, os.R_OK)}")
endstation = IgorEndstation()
scan_desc = {'file': file_path}
try:
    data = endstation.load(scan_desc)
    print("Data loaded successfully")
except Exception as e:
    print(f"Error loading data: {str(e)}")
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.143": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.endstations.plugin.igor_plugin import IgorEndstation\n\nimport os\n\nfile_path = '/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S009.pxt'\nprint(f\"File exists: {os.path.exists(file_path)}\")\nprint(f\"File is readable: {os.access(file_path, os.R_OK)}\")\nendstation = B()\nscan_desc = {'file': file_path}\ntry:\n    data = endstation.load(scan_desc)\n    print(\"Data loaded successfully\")\nexcept Exception as e:\n    print(f\"Error loading data: {str(e)}\")", 288)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.143.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.143.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.endstations.plugin.igor_plugin import IgorEndstation

import os

file_path = '/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S009.pxt'
print(f"File exists: {os.path.exists(file_path)}")
print(f"File is readable: {os.access(file_path, os.R_OK)}")
endstation = BL403ARPESEndstation()
scan_desc = {'file': file_path}
try:
    data = endstation.load(scan_desc)
    print("Data loaded successfully")
except Exception as e:
    print(f"Error loading data: {str(e)}")
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.144": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pi", 2)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.144.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.144.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.145": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip", 3)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.145.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.145.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.146": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip i", 5)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.146.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.146.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.147": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip instal", 10)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.147.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.147.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.148": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install", 11)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.148.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.148.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.149": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install a", 13)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.149.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.149.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.150": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install arpespy", 18)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.150.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.150.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.151": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install arpespyt", 20)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.151.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.151.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.152": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install arpespyth", 21)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.152.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.152.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.153": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install arpespytho", 22)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.153.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.153.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.154": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install arpespython", 23)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.154.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.154.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.155": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install arpespythont", 24)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.155.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.155.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.156": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install arpespythonto", 25)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.156.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.156.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.157": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install arpespythontoo", 26)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.157.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.157.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.158": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install arpespythontools", 27)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.158.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.158.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
get_ipython().run_line_magic('pip', 'install arpespythontools')
get_ipython().run_line_magic('pip', 'install arpespythontools')
get_ipython().run_line_magic('pip', 'install --upgrade numpy scipy matplotlib astropy jupyterlab')
get_ipython().run_line_magic('cd', 'arpespythontools')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.159": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("c\n", 1)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.159.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.159.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
get_ipython().run_line_magic('cd', '')
get_ipython().run_line_magic('cd', 'arpespythontools')
get_ipython().run_line_magic('pip', 'install --upgrade -r requirements.txt')
import arpespythontools as arp

data, x, y = arp.import_itx('path/to/your/file.pxt')
