# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import GaussianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
from ipywidgets import ToggleButtons

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max, color_mode, show_max_marker):
        plot_data = all_plots[scan_index - 1]
        ax.clear()

        # Get the full data
        data_to_plot = plot_data['data_values'].copy()
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            # Ensure the averaging kernel is 2D and has odd dimensions
            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)

            # Define the range for averaging
            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]
            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]

            # Apply averaging only to the specified range
            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]
            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')

            # Replace the averaged part in the original data
            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data

        k_indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)
        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values
            if not use_canny:
                max_index = np.argmax(kdc)
                kdc = kdc / np.max(kdc)
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))
            if show_edc:
                if color_mode == 'Color':
                    ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')
                else:  # Grayscale
                    ax.plot(energy_values, offset_kdc, color='black')
                
                if show_max_marker and not use_canny:
                    ax.plot(energy_values[max_index], offset_kdc[max_index], 'bo', markersize=10, fillstyle='none')

            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Filter data for fitting
                fit_e_indices = np.where((energy_values >= fit_e_min) & (energy_values <= fit_e_max))[0]
                fit_energy_values = energy_values[fit_e_indices]
                fit_kdc = kdc[fit_e_indices]

                if fit_type == 'Maxima':
                    peaks, _ = find_peaks(fit_kdc)
                    peak_heights = fit_kdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(fit_kdc))

                    for j, peak in enumerate(largest_peaks):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_energy_values[peak], min=fit_energy_values.min(), max=fit_energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                elif fit_type == 'Minima':
                    valleys, _ = find_peaks(-fit_kdc)
                    valley_depths = np.max(fit_kdc) - fit_kdc[valleys]
                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]
                    largest_valleys = valleys[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(fit_kdc))

                    for j, valley in enumerate(largest_valleys):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_energy_values[valley], min=fit_energy_values.min(), max=fit_energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(fit_kdc, params, x=fit_energy_values)
                fit = result.best_fit

                # Interpolate the fit back to the full energy range
                full_fit = np.interp(energy_values, fit_energy_values, fit)
                offset_fit = full_fit + i * vertical_offset

                # Extract sigma values and their uncertainties for label
                sigmas = []
                sigma_errors = []
                for j in range(num_peaks):
                    sigma = abs(result.params[f'g{j+1}_sigma'].value)
                    sigma_error = result.params[f'g{j+1}_sigma'].stderr
                    sigmas.append(sigma)
                    sigma_errors.append(sigma_error)

                sigma_label = ', '.join([fr'$\sigma_{j+1}$={sigma:.3f} $\pm$ {error:.3f}' for j, (sigma, error) in enumerate(zip(sigmas, sigma_errors))])

                if color_mode == 'Color':
                    ax.plot(energy_values, offset_fit, '--', label=fr'Fit $k_\parallel$={actual_k:.2f}, {sigma_label}', color=f'C{i}')
                else:  # Grayscale
                    ax.plot(energy_values, offset_fit, '--', color='black')

                if fit_type == 'Maxima':
                    fit_peaks, _ = find_peaks(full_fit)
                    ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)

                    for j, peak in enumerate(fit_peaks):
                        peak_energy = energy_values[peak]
                        peak_intensity = offset_fit[peak]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_energy = peak_energy - sigma
                        right_sigma_energy = peak_energy + sigma
                        left_sigma_intensity = np.interp(left_sigma_energy, energy_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_energy, energy_values, offset_fit)

                        ax.plot(left_sigma_energy, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_energy, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_energy, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_energy, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')

                elif fit_type == 'Minima':
                    fit_valleys, _ = find_peaks(-full_fit)
                    ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

                    for j, valley in enumerate(fit_valleys):
                        valley_energy = energy_values[valley]
                        valley_intensity = offset_fit[valley]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_energy = valley_energy - sigma
                        right_sigma_energy = valley_energy + sigma
                        left_sigma_intensity = np.interp(left_sigma_energy, energy_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_energy, energy_values, offset_fit)

                        ax.plot(left_sigma_energy, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_energy, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_energy, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_energy, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')

        ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        
        # Only show legend in Color mode
        if color_mode == 'Color':
            ax.legend()
        
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'fit_type': interactive_plot.children[16].value,
            'fit_e_min': interactive_plot.children[17].value,
            'fit_e_max': interactive_plot.children[18].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def export_data(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'fit_type': interactive_plot.children[16].value,
            'fit_e_min': interactive_plot.children[17].value,
            'fit_e_max': interactive_plot.children[18].value
        }

        plot_data = all_plots[current_values['scan_index'] - 1]
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']
        data_values = plot_data['data_values']

        # Convert xarray DataArrays to numpy arrays if necessary
        if isinstance(k_parallel, xr.DataArray):
            k_parallel = k_parallel.values
        if isinstance(energy_values, xr.DataArray):
            energy_values = energy_values.values
        if isinstance(data_values, xr.DataArray):
            data_values = data_values.values

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]
        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]
        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if current_values['use_canny']:
            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])
            data_to_plot = edges.astype(float)

        if current_values['enable_averaging']:
            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')

        k_indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)

        # Get the current axes
        ax = plt.gca()

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            if isinstance(actual_k, np.ndarray):
                actual_k = actual_k.item()
            kdc = data_to_plot[:, k_index]

            # Apply normalization
            if not current_values['use_canny']:
                kdc = kdc / np.max(kdc)

            # Export original/processed data
            np.savetxt(f"data_k_{actual_k:.2f}.dat", np.column_stack((energy_values, kdc)), header="Energy (eV)\tIntensity (arb. units)")

            if current_values['show_fit']:
                # Find the fit line in the plot
                fit_line = None
                for line in ax.lines:
                    if line.get_label().startswith(f"Fit $k_\parallel$={actual_k:.2f}"):
                        fit_line = line
                        break

                if fit_line is not None:
                    # Get the x and y data of the fit line
                    fit_x_data, fit_y_data = fit_line.get_data()
                    
                    # Remove the vertical offset
                    fit_y_data -= i * current_values['vertical_offset']
                    
                    # Export the fit data
                    np.savetxt(f"gaussian_fit_k_{actual_k:.2f}.dat", np.column_stack((fit_x_data, fit_y_data)), header="Energy (eV)\tFitted Intensity (arb. units)")
                else:
                    print(f"No fit found for k = {actual_k:.2f}")

        print("Data export completed.")


    interactive_plot = interactive(
        plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=35, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),
        use_canny=Checkbox(value=False, description='Use Canny Filter'),
        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),
        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),
        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),
        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min (Å⁻¹)', continuous_update=True),
        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max (Å⁻¹)', continuous_update=True),
        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),
        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),
        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True),
        color_mode=ToggleButtons(options=['Color', 'Grayscale'], description='Color Mode'),
        show_max_marker=Checkbox(value=False, description='Show Max Marker')
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    export_button = Button(description="Export Data")
    export_button.on_click(export_data)

    output = VBox([interactive_plot, HBox([save_button, export_button])])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import GaussianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
from ipywidgets import ToggleButtons

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max, color_mode, show_max_marker, show_second_max_marker, division_point):
        plot_data = all_plots[scan_index - 1]
        ax.clear()

        # Get the full data
        data_to_plot = plot_data['data_values'].copy()
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            # Ensure the averaging kernel is 2D and has odd dimensions
            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)

            # Define the range for averaging
            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]
            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]

            # Apply averaging only to the specified range
            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]
            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')

            # Replace the averaged part in the original data
            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data

        k_indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)
        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values
            if not use_canny:
                max_index = np.argmax(kdc)
                kdc = kdc / np.max(kdc)
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))
            if show_edc:
                if color_mode == 'Color':
                    ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')
                else:  # Grayscale
                    ax.plot(energy_values, offset_kdc, color='black')
                
                if show_max_marker and not use_canny:
                    ax.plot(energy_values[max_index], offset_kdc[max_index], 'bo', markersize=10, fillstyle='none')
                
                if show_second_max_marker and not use_canny:
                    left_max_index = np.argmax(kdc[energy_values <= division_point])
                    right_max_index = len(energy_values) - 1 - np.argmax(kdc[energy_values > division_point][::-1])
                    ax.plot(energy_values[left_max_index], offset_kdc[left_max_index], 'ro', markersize=10, fillstyle='none')
                    ax.plot(energy_values[right_max_index], offset_kdc[right_max_index], 'bo', markersize=10, fillstyle='none')

            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Filter data for fitting
                fit_e_indices = np.where((energy_values >= fit_e_min) & (energy_values <= fit_e_max))[0]
                fit_energy_values = energy_values[fit_e_indices]
                fit_kdc = kdc[fit_e_indices]

                if fit_type == 'Maxima':
                    peaks, _ = find_peaks(fit_kdc)
                    peak_heights = fit_kdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(fit_kdc))

                    for j, peak in enumerate(largest_peaks):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_energy_values[peak], min=fit_energy_values.min(), max=fit_energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                elif fit_type == 'Minima':
                    valleys, _ = find_peaks(-fit_kdc)
                    valley_depths = np.max(fit_kdc) - fit_kdc[valleys]
                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]
                    largest_valleys = valleys[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(fit_kdc))

                    for j, valley in enumerate(largest_valleys):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_energy_values[valley], min=fit_energy_values.min(), max=fit_energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(fit_kdc, params, x=fit_energy_values)
                fit = result.best_fit

                # Interpolate the fit back to the full energy range
                full_fit = np.interp(energy_values, fit_energy_values, fit)
                offset_fit = full_fit + i * vertical_offset

                # Extract sigma values and their uncertainties for label
                sigmas = []
                sigma_errors = []
                for j in range(num_peaks):
                    sigma = abs(result.params[f'g{j+1}_sigma'].value)
                    sigma_error = result.params[f'g{j+1}_sigma'].stderr
                    sigmas.append(sigma)
                    sigma_errors.append(sigma_error)

                sigma_label = ', '.join([fr'$\sigma_{j+1}$={sigma:.3f} $\pm$ {error:.3f}' for j, (sigma, error) in enumerate(zip(sigmas, sigma_errors))])

                if color_mode == 'Color':
                    ax.plot(energy_values, offset_fit, '--', label=fr'Fit $k_\parallel$={actual_k:.2f}, {sigma_label}', color=f'C{i}')
                else:  # Grayscale
                    ax.plot(energy_values, offset_fit, '--', color='black')

                if fit_type == 'Maxima':
                    fit_peaks, _ = find_peaks(full_fit)
                    ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)

                    for j, peak in enumerate(fit_peaks):
                        peak_energy = energy_values[peak]
                        peak_intensity = offset_fit[peak]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_energy = peak_energy - sigma
                        right_sigma_energy = peak_energy + sigma
                        left_sigma_intensity = np.interp(left_sigma_energy, energy_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_energy, energy_values, offset_fit)

                        ax.plot(left_sigma_energy, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_energy, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_energy, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_energy, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')

                elif fit_type == 'Minima':
                    fit_valleys, _ = find_peaks(-full_fit)
                    ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

                    for j, valley in enumerate(fit_valleys):
                        valley_energy = energy_values[valley]
                        valley_intensity = offset_fit[valley]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_energy = valley_energy - sigma
                        right_sigma_energy = valley_energy + sigma
                        left_sigma_intensity = np.interp(left_sigma_energy, energy_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_energy, energy_values, offset_fit)

                        ax.plot(left_sigma_energy, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_energy, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_energy, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_energy, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')

        # Add a vertical line at the division point
        if show_second_max_marker:
            ax.axvline(x=division_point, color='green', linestyle='--', alpha=0.5)

        ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        
        # Only show legend in Color mode
        if color_mode == 'Color':
            ax.legend()
        
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'fit_type': interactive_plot.children[16].value,
            'fit_e_min': interactive_plot.children[17].value,
            'fit_e_max': interactive_plot.children[18].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def export_data(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'fit_type': interactive_plot.children[16].value,
            'fit_e_min': interactive_plot.children[17].value,
            'fit_e_max': interactive_plot.children[18].value
        }

        plot_data = all_plots[current_values['scan_index'] - 1]
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']
        data_values = plot_data['data_values']

        # Convert xarray DataArrays to numpy arrays if necessary
        if isinstance(k_parallel, xr.DataArray):
            k_parallel = k_parallel.values
        if isinstance(energy_values, xr.DataArray):
            energy_values = energy_values.values
        if isinstance(data_values, xr.DataArray):
            data_values = data_values.values

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]
        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]
        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if current_values['use_canny']:
            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])
            data_to_plot = edges.astype(float)

        if current_values['enable_averaging']:
            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')

        k_indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)

        # Get the current axes
        ax = plt.gca()

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            if isinstance(actual_k, np.ndarray):
                actual_k = actual_k.item()
            kdc = data_to_plot[:, k_index]

            # Apply normalization
            if not current_values['use_canny']:
                kdc = kdc / np.max(kdc)

            # Export original/processed data
            np.savetxt(f"data_k_{actual_k:.2f}.dat", np.column_stack((energy_values, kdc)), header="Energy (eV)\tIntensity (arb. units)")

            if current_values['show_fit']:
                # Find the fit line in the plot
                fit_line = None
                for line in ax.lines:
                    if line.get_label().startswith(f"Fit $k_\parallel$={actual_k:.2f}"):
                        fit_line = line
                        break

                if fit_line is not None:
                    # Get the x and y data of the fit line
                    fit_x_data, fit_y_data = fit_line.get_data()
                    
                    # Remove the vertical offset
                    fit_y_data -= i * current_values['vertical_offset']
                    
                    # Export the fit data
                    np.savetxt(f"gaussian_fit_k_{actual_k:.2f}.dat", np.column_stack((fit_x_data, fit_y_data)), header="Energy (eV)\tFitted Intensity (arb. units)")
                else:
                    print(f"No fit found for k = {actual_k:.2f}")

        print("Data export completed.")


    interactive_plot = interactive(
        plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),
        use_canny=Checkbox(value=False, description='Use Canny Filter'),
        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),
        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),
        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),
        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min (Å⁻¹)', continuous_update=True),
        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max (Å⁻¹)', continuous_update=True),
        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),
        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),
        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True),
        color_mode=ToggleButtons(options=['Color', 'Grayscale'], description='Color Mode'),
        show_max_marker=Checkbox(value=False, description='Show Max Marker'),
        show_second_max_marker=Checkbox(value=False, description='Show Dual Max Markers'),
        division_point=FloatSlider(value=(global_e_min + global_e_max) / 2, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Division Point (eV)', continuous_update=True)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    export_button = Button(description="Export Data")
    export_button.on_click(export_data)

    output = VBox([interactive_plot, HBox([save_button, export_button])])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import GaussianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
from ipywidgets import ToggleButtons

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max, color_mode, show_max_marker, show_second_max_marker, division_point):
        plot_data = all_plots[scan_index - 1]
        ax.clear()

        # Get the full data
        data_to_plot = plot_data['data_values'].copy()
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            # Ensure the averaging kernel is 2D and has odd dimensions
            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)

            # Define the range for averaging
            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]
            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]

            # Apply averaging only to the specified range
            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]
            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')

            # Replace the averaged part in the original data
            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data

        k_indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)
        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values
            if not use_canny:
                max_index = np.argmax(kdc)
                kdc = kdc / np.max(kdc)
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))
            if show_edc:
                if color_mode == 'Color':
                    ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')
                else:  # Grayscale
                    ax.plot(energy_values, offset_kdc, color='black')
                
                if show_max_marker and not use_canny:
                    ax.plot(energy_values[max_index], offset_kdc[max_index], 'bo', markersize=10, fillstyle='none')
                
                if show_second_max_marker and not use_canny:
                    left_max_index = np.argmax(kdc[energy_values <= division_point])
                    right_max_index = len(energy_values) - 1 - np.argmax(kdc[energy_values > division_point][::-1])
                    ax.plot(energy_values[left_max_index], offset_kdc[left_max_index], 'ro', markersize=10, fillstyle='none')
                    ax.plot(energy_values[right_max_index], offset_kdc[right_max_index], 'bo', markersize=10, fillstyle='none')

            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Filter data for fitting
                fit_e_indices = np.where((energy_values >= fit_e_min) & (energy_values <= fit_e_max))[0]
                fit_energy_values = energy_values[fit_e_indices]
                fit_kdc = kdc[fit_e_indices]

                if fit_type == 'Maxima':
                    peaks, _ = find_peaks(fit_kdc)
                    peak_heights = fit_kdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(fit_kdc))

                    for j, peak in enumerate(largest_peaks):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_energy_values[peak], min=fit_energy_values.min(), max=fit_energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                elif fit_type == 'Minima':
                    valleys, _ = find_peaks(-fit_kdc)
                    valley_depths = np.max(fit_kdc) - fit_kdc[valleys]
                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]
                    largest_valleys = valleys[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(fit_kdc))

                    for j, valley in enumerate(largest_valleys):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_energy_values[valley], min=fit_energy_values.min(), max=fit_energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(fit_kdc, params, x=fit_energy_values)
                fit = result.best_fit

                # Interpolate the fit back to the full energy range
                full_fit = np.interp(energy_values, fit_energy_values, fit)
                offset_fit = full_fit + i * vertical_offset

                # Extract sigma values and their uncertainties for label
                sigmas = []
                sigma_errors = []
                for j in range(num_peaks):
                    sigma = abs(result.params[f'g{j+1}_sigma'].value)
                    sigma_error = result.params[f'g{j+1}_sigma'].stderr
                    sigmas.append(sigma)
                    sigma_errors.append(sigma_error)

                sigma_label = ', '.join([fr'$\sigma_{j+1}$={sigma:.3f} $\pm$ {error:.3f}' for j, (sigma, error) in enumerate(zip(sigmas, sigma_errors))])

                if color_mode == 'Color':
                    ax.plot(energy_values, offset_fit, '--', label=fr'Fit $k_\parallel$={actual_k:.2f}, {sigma_label}', color=f'C{i}')
                else:  # Grayscale
                    ax.plot(energy_values, offset_fit, '--', color='black')

                if fit_type == 'Maxima':
                    fit_peaks, _ = find_peaks(full_fit)
                    ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)

                    for j, peak in enumerate(fit_peaks):
                        peak_energy = energy_values[peak]
                        peak_intensity = offset_fit[peak]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_energy = peak_energy - sigma
                        right_sigma_energy = peak_energy + sigma
                        left_sigma_intensity = np.interp(left_sigma_energy, energy_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_energy, energy_values, offset_fit)

                        ax.plot(left_sigma_energy, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_energy, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_energy, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_energy, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')

                elif fit_type == 'Minima':
                    fit_valleys, _ = find_peaks(-full_fit)
                    ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

                    for j, valley in enumerate(fit_valleys):
                        valley_energy = energy_values[valley]
                        valley_intensity = offset_fit[valley]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_energy = valley_energy - sigma
                        right_sigma_energy = valley_energy + sigma
                        left_sigma_intensity = np.interp(left_sigma_energy, energy_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_energy, energy_values, offset_fit)

                        ax.plot(left_sigma_energy, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_energy, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_energy, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_energy, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')

        # Add a vertical line at the division point
        if show_second_max_marker:
            ax.axvline(x=division_point, color='green', linestyle='--', alpha=0.5)

        ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        
        # Only show legend in Color mode
        if color_mode == 'Color':
            ax.legend()
        
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'fit_type': interactive_plot.children[16].value,
            'fit_e_min': interactive_plot.children[17].value,
            'fit_e_max': interactive_plot.children[18].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def export_data(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'fit_type': interactive_plot.children[16].value,
            'fit_e_min': interactive_plot.children[17].value,
            'fit_e_max': interactive_plot.children[18].value
        }

        plot_data = all_plots[current_values['scan_index'] - 1]
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']
        data_values = plot_data['data_values']

        # Convert xarray DataArrays to numpy arrays if necessary
        if isinstance(k_parallel, xr.DataArray):
            k_parallel = k_parallel.values
        if isinstance(energy_values, xr.DataArray):
            energy_values = energy_values.values
        if isinstance(data_values, xr.DataArray):
            data_values = data_values.values

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]
        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]
        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if current_values['use_canny']:
            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])
            data_to_plot = edges.astype(float)

        if current_values['enable_averaging']:
            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')

        k_indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)

        # Get the current axes
        ax = plt.gca()

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            if isinstance(actual_k, np.ndarray):
                actual_k = actual_k.item()
            kdc = data_to_plot[:, k_index]

            # Apply normalization
            if not current_values['use_canny']:
                kdc = kdc / np.max(kdc)

            # Export original/processed data
            np.savetxt(f"data_k_{actual_k:.2f}.dat", np.column_stack((energy_values, kdc)), header="Energy (eV)\tIntensity (arb. units)")

            if current_values['show_fit']:
                # Find the fit line in the plot
                fit_line = None
                for line in ax.lines:
                    if line.get_label().startswith(f"Fit $k_\parallel$={actual_k:.2f}"):
                        fit_line = line
                        break

                if fit_line is not None:
                    # Get the x and y data of the fit line
                    fit_x_data, fit_y_data = fit_line.get_data()
                    
                    # Remove the vertical offset
                    fit_y_data -= i * current_values['vertical_offset']
                    
                    # Export the fit data
                    np.savetxt(f"gaussian_fit_k_{actual_k:.2f}.dat", np.column_stack((fit_x_data, fit_y_data)), header="Energy (eV)\tFitted Intensity (arb. units)")
                else:
                    print(f"No fit found for k = {actual_k:.2f}")

        print("Data export completed.")


    interactive_plot = interactive(
        plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=40, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),
        use_canny=Checkbox(value=False, description='Use Canny Filter'),
        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),
        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),
        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),
        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min (Å⁻¹)', continuous_update=True),
        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max (Å⁻¹)', continuous_update=True),
        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),
        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),
        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True),
        color_mode=ToggleButtons(options=['Color', 'Grayscale'], description='Color Mode'),
        show_max_marker=Checkbox(value=False, description='Show Max Marker'),
        show_second_max_marker=Checkbox(value=False, description='Show Dual Max Markers'),
        division_point=FloatSlider(value=(global_e_min + global_e_max) / 2, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Division Point (eV)', continuous_update=True)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    export_button = Button(description="Export Data")
    export_button.on_click(export_data)

    output = VBox([interactive_plot, HBox([save_button, export_button])])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.0": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons\nfrom matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm\nfrom IPython.display import display, clear_output\nimport tkinter as tk\nfrom tkinter import filedialog\nimport warnings\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nfrom matplotlib import MatplotlibDeprecationWarning\nfrom lmfit.models import GaussianModel, LinearModel\nfrom scipy.signal import find_peaks\nfrom skimage.feature import canny\nfrom scipy.ndimage import convolve\nfrom ipywidgets import ToggleButtons\n\n%matplotlib widget\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",\n    \"axes.labelcolor\": \"black\",\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()\n    return [os.path.join(folder_path, f) for f in data_files]\n\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef edc_plot(data_files, work_function):\n    # Constants\n    hbar = 1.054571817e-34  # J*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    global_k_min = float('inf')\n    global_k_max = float('-inf')\n    global_e_min = float('inf')\n    global_e_max = float('-inf')\n    global_intensity_max = float('-inf')\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']\n\n        # Calculate kinetic energy and momentum\n        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19\n        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10\n\n        # Get the energy values\n        energy_values = -data.eV.values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        # Update global ranges\n        global_k_min = min(global_k_min, np.min(k_parallel))\n        global_k_max = max(global_k_max, np.max(k_parallel))\n        global_e_min = min(global_e_min, np.min(energy_values))\n        global_e_max = max(global_e_max, np.max(energy_values))\n        global_intensity_max = max(global_intensity_max, np.max(data.values))\n\n    global_k_range = global_k_max - global_k_min\n    global_e_range = global_e_max - global_e_min\n\n    fig, ax = plt.subplots(figsize=(10, 8))\n\n    def plot_curves(scan_index, n, vertical_offset, show_curves, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max, color_mode, show_max_marker, show_second_max_marker, division_point, curve_type):\n        plot_data = all_plots[scan_index - 1]\n        ax.clear()\n\n        # Get the full data\n        data_to_plot = plot_data['data_values'].copy()\n        k_parallel = plot_data['k_parallel'][0]\n        energy_values = plot_data['energy_values']\n\n        # Apply filters only to the selected range\n        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]\n        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]\n        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]\n        k_parallel = k_parallel[valid_k_indices]\n        energy_values = energy_values[valid_e_indices]\n\n        if use_canny:\n            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)\n            data_to_plot = edges.astype(float)\n\n        if enable_averaging:\n            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)\n            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)\n            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]\n            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]\n            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]\n            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')\n            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data\n\n        if curve_type == 'EDC':\n            indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)\n            x_values = energy_values\n            y_label = 'Intensity (arb. units)'\n            x_label = r'$E-E_f$ (eV)'\n        else:  # MDC\n            indices = np.linspace(0, len(valid_e_indices) - 1, n, dtype=int)\n            x_values = k_parallel\n            y_label = 'Intensity (arb. units)'\n            x_label = r'$k_\\parallel$ (Å⁻¹)'\n\n        max_intensity = float('-inf')\n        min_intensity = float('inf')\n\n        for i, index in enumerate(indices):\n            if curve_type == 'EDC':\n                curve = data_to_plot[:, index]\n                actual_value = k_parallel[index]\n                label = fr'$k_\\parallel$ = {actual_value:.2f}'\n            else:  # MDC\n                curve = data_to_plot[index, :]\n                actual_value = energy_values[index]\n                label = fr'$E-E_f$ = {actual_value:.2f}'\n\n            if isinstance(curve, xr.DataArray):\n                curve = curve.values\n            if isinstance(x_values, xr.DataArray):\n                x_values = x_values.values\n\n            if not use_canny:\n                max_index = np.argmax(curve)\n                curve = curve / np.max(curve)\n\n            offset_curve = curve + i * vertical_offset\n            max_intensity = max(max_intensity, np.max(offset_curve))\n            min_intensity = min(min_intensity, np.min(offset_curve))\n\n            if show_curves:\n                if color_mode == 'Color':\n                    ax.plot(x_values, offset_curve, label=label)\n                else:  # Grayscale\n                    ax.plot(x_values, offset_curve, color='black')\n                \n                if show_max_marker and not use_canny:\n                    ax.plot(x_values[max_index], offset_curve[max_index], 'bo', markersize=10, fillstyle='none')\n                \n                if show_second_max_marker and not use_canny:\n                    left_max_index = np.argmax(curve[x_values <= division_point])\n                    right_max_index = len(x_values) - 1 - np.argmax(curve[x_values > division_point][::-1])\n                    ax.plot(x_values[left_max_index], offset_curve[left_max_index], 'ro', markersize=10, fillstyle='none')\n                    ax.plot(x_values[right_max_index], offset_curve[right_max_index], 'bo', markersize=10, fillstyle='none')\n\n            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)\n\n            if show_fit:\n                # Implement fitting logic here (similar to the original, but adapted for both EDC and MDC)\n                pass\n\n        # Add a vertical line at the division point\n        if show_second_max_marker:\n            ax.axvline(x=division_point, color='green', linestyle='--', alpha=0.5)\n\n        ax.set_xlabel(x_label, fontsize=12, fontweight='bold')\n        ax.set_ylabel(y_label, fontsize=12, fontweight='bold')\n        ax.set_title(f'{curve_type}s - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        \n        # Only show legend in Color mode\n        if color_mode == 'Color':\n            ax.legend()\n        \n        ax.tick_params(axis='both', which='major', labelsize=10)\n        ax.set_xlim(e_min if curve_type == 'EDC' else k_min, e_max if curve_type == 'EDC' else k_max)\n        y_range = max_intensity - min_intensity\n        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)\n\n        plt.tight_layout()\n        fig.canvas.draw_idle()\n\n        return max_intensity - min_intensity\n    def save_plot(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_curves': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'use_canny': interactive_plot.children[10].value,\n            'sigma': interactive_plot.children[11].value,\n            'low_threshold': interactive_plot.children[12].value,\n            'high_threshold': interactive_plot.children[13].value,\n            'enable_averaging': interactive_plot.children[14].value,\n            'averaging_kernel_size': interactive_plot.children[15].value,\n            'avg_e_min': interactive_plot.children[16].value,\n            'avg_e_max': interactive_plot.children[17].value,\n            'avg_k_min': interactive_plot.children[18].value,\n            'avg_k_max': interactive_plot.children[19].value,\n            'fit_type': interactive_plot.children[20].value,\n            'fit_e_min': interactive_plot.children[21].value,\n            'fit_e_max': interactive_plot.children[22].value,\n            'color_mode': interactive_plot.children[23].value,\n            'show_max_marker': interactive_plot.children[24].value,\n            'show_second_max_marker': interactive_plot.children[25].value,\n            'division_point': interactive_plot.children[26].value,\n            'curve_type': interactive_plot.children[27].value\n        }\n        plot_curves(**current_values)\n        filename = f\"{current_values['curve_type']}_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        print(f\"Plot saved as {filename}\")\n\n    def export_data(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_curves': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'use_canny': interactive_plot.children[10].value,\n            'sigma': interactive_plot.children[11].value,\n            'low_threshold': interactive_plot.children[12].value,\n            'high_threshold': interactive_plot.children[13].value,\n            'enable_averaging': interactive_plot.children[14].value,\n            'averaging_kernel_size': interactive_plot.children[15].value,\n            'avg_e_min': interactive_plot.children[16].value,\n            'avg_e_max': interactive_plot.children[17].value,\n            'avg_k_min': interactive_plot.children[18].value,\n            'avg_k_max': interactive_plot.children[19].value,\n            'fit_type': interactive_plot.children[20].value,\n            'fit_e_min': interactive_plot.children[21].value,\n            'fit_e_max': interactive_plot.children[22].value,\n            'color_mode': interactive_plot.children[23].value,\n            'show_max_marker': interactive_plot.children[24].value,\n            'show_second_max_marker': interactive_plot.children[25].value,\n            'division_point': interactive_plot.children[26].value,\n            'curve_type': interactive_plot.children[27].value\n        }\n        \n        plot_data = all_plots[current_values['scan_index'] - 1]\n        k_parallel = plot_data['k_parallel'][0]\n        energy_values = plot_data['energy_values']\n        data_values = plot_data['data_values']\n\n        # Convert xarray DataArrays to numpy arrays if necessary\n        if isinstance(k_parallel, xr.DataArray):\n            k_parallel = k_parallel.values\n        if isinstance(energy_values, xr.DataArray):\n            energy_values = energy_values.values\n        if isinstance(data_values, xr.DataArray):\n            data_values = data_values.values\n\n        # Apply filters only to the selected range\n        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]\n        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]\n        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]\n        k_parallel = k_parallel[valid_k_indices]\n        energy_values = energy_values[valid_e_indices]\n\n        if current_values['use_canny']:\n            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])\n            data_to_plot = edges.astype(float)\n\n        if current_values['enable_averaging']:\n            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)\n            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)\n            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')\n\n        if current_values['curve_type'] == 'EDC':\n            indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)\n            x_values = energy_values\n            y_label = 'Intensity (arb. units)'\n            x_label = 'Energy (eV)'\n        else:  # MDC\n            indices = np.linspace(0, len(valid_e_indices) - 1, current_values['n'], dtype=int)\n            x_values = k_parallel\n            y_label = 'Intensity (arb. units)'\n            x_label = 'k_parallel (Å⁻¹)'\n\n        # Get the current axes\n        ax = plt.gca()\n\n        for i, index in enumerate(indices):\n            if current_values['curve_type'] == 'EDC':\n                curve = data_to_plot[:, index]\n                actual_value = k_parallel[index]\n            else:  # MDC\n                curve = data_to_plot[index, :]\n                actual_value = energy_values[index]\n\n            if isinstance(actual_value, np.ndarray):\n                actual_value = actual_value.item()\n\n            # Apply normalization if not using Canny filter\n            if not current_values['use_canny']:\n                curve = curve / np.max(curve)\n\n            # Export original/processed data\n            np.savetxt(f\"{current_values['curve_type']}_{actual_value:.2f}.dat\", np.column_stack((x_values, curve)), header=f\"{x_label}\\t{y_label}\")\n\n            if current_values['show_fit']:\n                # Find the fit line in the plot\n                fit_line = None\n                for line in ax.lines:\n                    if line.get_label().startswith(f\"Fit {current_values['curve_type']}={actual_value:.2f}\"):\n                        fit_line = line\n                        break\n\n                if fit_line is not None:\n                    # Get the x and y data of the fit line\n                    fit_x_data, fit_y_data = fit_line.get_data()\n                    # Remove the vertical offset\n                    fit_y_data -= i * current_values['vertical_offset']\n                    # Export the fit data\n                    np.savetxt(f\"gaussian_fit_{current_values['curve_type']}_{actual_value:.2f}.dat\", np.column_stack((fit_x_data, fit_y_data)), header=f\"{x_label}\\tFitted {y_label}\")\n                else:\n                    print(f\"No fit found for {current_values['curve_type']} = {actual_value:.2f}\")\n\n        print(\"Data export completed.\")\n\n    interactive_plot = interactive(\n        plot_curves,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of Curves', continuous_update=True),\n        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),\n        show_curves=Checkbox(value=True, description='Show Curves'),\n        show_fit=Checkbox(value=False, description='Show Fits'),\n        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),\n        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),\n        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),\n        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),\n        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),\n        use_canny=Checkbox(value=False, description='Use Canny Filter'),\n        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),\n        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),\n        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),\n        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),\n        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),\n        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),\n        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),\n        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min (Å⁻¹)', continuous_update=True),\n        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max (Å⁻¹)', continuous_update=True),\n        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),\n        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),\n        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True),\n        color_mode=ToggleButtons(options=['Color', 'Grayscale'], description='Color Mode'),\n        show_max_marker=Checkbox(value=False, description='Show Max Marker'),\n        show_second_max_marker=Checkbox(value=False, description='Show Dual Max Markers'),\n        division_point=FloatSlider(value=(global_e_min + global_e_max) / 2, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Division Point', continuous_update=True),\n        curve_type=ToggleButtons(options=['EDC', 'MDC'], description='Curve Type')\n    )\n\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    export_button = Button(description=\"Export Data\")\n    export_button.on_click(export_data)\n\n    output = VBox([interactive_plot, HBox([save_button, export_button])])\n    r", 20259)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.0.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.0.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.1": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons\nfrom matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm\nfrom IPython.display import display, clear_output\nimport tkinter as tk\nfrom tkinter import filedialog\nimport warnings\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nfrom matplotlib import MatplotlibDeprecationWarning\nfrom lmfit.models import GaussianModel, LinearModel\nfrom scipy.signal import find_peaks\nfrom skimage.feature import canny\nfrom scipy.ndimage import convolve\nfrom ipywidgets import ToggleButtons\n\n%matplotlib widget\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",\n    \"axes.labelcolor\": \"black\",\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()\n    return [os.path.join(folder_path, f) for f in data_files]\n\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef edc_plot(data_files, work_function):\n    # Constants\n    hbar = 1.054571817e-34  # J*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    global_k_min = float('inf')\n    global_k_max = float('-inf')\n    global_e_min = float('inf')\n    global_e_max = float('-inf')\n    global_intensity_max = float('-inf')\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']\n\n        # Calculate kinetic energy and momentum\n        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19\n        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10\n\n        # Get the energy values\n        energy_values = -data.eV.values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        # Update global ranges\n        global_k_min = min(global_k_min, np.min(k_parallel))\n        global_k_max = max(global_k_max, np.max(k_parallel))\n        global_e_min = min(global_e_min, np.min(energy_values))\n        global_e_max = max(global_e_max, np.max(energy_values))\n        global_intensity_max = max(global_intensity_max, np.max(data.values))\n\n    global_k_range = global_k_max - global_k_min\n    global_e_range = global_e_max - global_e_min\n\n    fig, ax = plt.subplots(figsize=(10, 8))\n\n    def plot_curves(scan_index, n, vertical_offset, show_curves, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max, color_mode, show_max_marker, show_second_max_marker, division_point, curve_type):\n        plot_data = all_plots[scan_index - 1]\n        ax.clear()\n\n        # Get the full data\n        data_to_plot = plot_data['data_values'].copy()\n        k_parallel = plot_data['k_parallel'][0]\n        energy_values = plot_data['energy_values']\n\n        # Apply filters only to the selected range\n        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]\n        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]\n        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]\n        k_parallel = k_parallel[valid_k_indices]\n        energy_values = energy_values[valid_e_indices]\n\n        if use_canny:\n            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)\n            data_to_plot = edges.astype(float)\n\n        if enable_averaging:\n            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)\n            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)\n            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]\n            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]\n            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]\n            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')\n            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data\n\n        if curve_type == 'EDC':\n            indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)\n            x_values = energy_values\n            y_label = 'Intensity (arb. units)'\n            x_label = r'$E-E_f$ (eV)'\n        else:  # MDC\n            indices = np.linspace(0, len(valid_e_indices) - 1, n, dtype=int)\n            x_values = k_parallel\n            y_label = 'Intensity (arb. units)'\n            x_label = r'$k_\\parallel$ (Å⁻¹)'\n\n        max_intensity = float('-inf')\n        min_intensity = float('inf')\n\n        for i, index in enumerate(indices):\n            if curve_type == 'EDC':\n                curve = data_to_plot[:, index]\n                actual_value = k_parallel[index]\n                label = fr'$k_\\parallel$ = {actual_value:.2f}'\n            else:  # MDC\n                curve = data_to_plot[index, :]\n                actual_value = energy_values[index]\n                label = fr'$E-E_f$ = {actual_value:.2f}'\n\n            if isinstance(curve, xr.DataArray):\n                curve = curve.values\n            if isinstance(x_values, xr.DataArray):\n                x_values = x_values.values\n\n            if not use_canny:\n                max_index = np.argmax(curve)\n                curve = curve / np.max(curve)\n\n            offset_curve = curve + i * vertical_offset\n            max_intensity = max(max_intensity, np.max(offset_curve))\n            min_intensity = min(min_intensity, np.min(offset_curve))\n\n            if show_curves:\n                if color_mode == 'Color':\n                    ax.plot(x_values, offset_curve, label=label)\n                else:  # Grayscale\n                    ax.plot(x_values, offset_curve, color='black')\n                \n                if show_max_marker and not use_canny:\n                    ax.plot(x_values[max_index], offset_curve[max_index], 'bo', markersize=10, fillstyle='none')\n                \n                if show_second_max_marker and not use_canny:\n                    left_max_index = np.argmax(curve[x_values <= division_point])\n                    right_max_index = len(x_values) - 1 - np.argmax(curve[x_values > division_point][::-1])\n                    ax.plot(x_values[left_max_index], offset_curve[left_max_index], 'ro', markersize=10, fillstyle='none')\n                    ax.plot(x_values[right_max_index], offset_curve[right_max_index], 'bo', markersize=10, fillstyle='none')\n\n            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)\n\n            if show_fit:\n                # Implement fitting logic here (similar to the original, but adapted for both EDC and MDC)\n                pass\n\n        # Add a vertical line at the division point\n        if show_second_max_marker:\n            ax.axvline(x=division_point, color='green', linestyle='--', alpha=0.5)\n\n        ax.set_xlabel(x_label, fontsize=12, fontweight='bold')\n        ax.set_ylabel(y_label, fontsize=12, fontweight='bold')\n        ax.set_title(f'{curve_type}s - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        \n        # Only show legend in Color mode\n        if color_mode == 'Color':\n            ax.legend()\n        \n        ax.tick_params(axis='both', which='major', labelsize=10)\n        ax.set_xlim(e_min if curve_type == 'EDC' else k_min, e_max if curve_type == 'EDC' else k_max)\n        y_range = max_intensity - min_intensity\n        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)\n\n        plt.tight_layout()\n        fig.canvas.draw_idle()\n\n        return max_intensity - min_intensity\n    def save_plot(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_curves': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'use_canny': interactive_plot.children[10].value,\n            'sigma': interactive_plot.children[11].value,\n            'low_threshold': interactive_plot.children[12].value,\n            'high_threshold': interactive_plot.children[13].value,\n            'enable_averaging': interactive_plot.children[14].value,\n            'averaging_kernel_size': interactive_plot.children[15].value,\n            'avg_e_min': interactive_plot.children[16].value,\n            'avg_e_max': interactive_plot.children[17].value,\n            'avg_k_min': interactive_plot.children[18].value,\n            'avg_k_max': interactive_plot.children[19].value,\n            'fit_type': interactive_plot.children[20].value,\n            'fit_e_min': interactive_plot.children[21].value,\n            'fit_e_max': interactive_plot.children[22].value,\n            'color_mode': interactive_plot.children[23].value,\n            'show_max_marker': interactive_plot.children[24].value,\n            'show_second_max_marker': interactive_plot.children[25].value,\n            'division_point': interactive_plot.children[26].value,\n            'curve_type': interactive_plot.children[27].value\n        }\n        plot_curves(**current_values)\n        filename = f\"{current_values['curve_type']}_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        print(f\"Plot saved as {filename}\")\n\n    def export_data(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_curves': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'use_canny': interactive_plot.children[10].value,\n            'sigma': interactive_plot.children[11].value,\n            'low_threshold': interactive_plot.children[12].value,\n            'high_threshold': interactive_plot.children[13].value,\n            'enable_averaging': interactive_plot.children[14].value,\n            'averaging_kernel_size': interactive_plot.children[15].value,\n            'avg_e_min': interactive_plot.children[16].value,\n            'avg_e_max': interactive_plot.children[17].value,\n            'avg_k_min': interactive_plot.children[18].value,\n            'avg_k_max': interactive_plot.children[19].value,\n            'fit_type': interactive_plot.children[20].value,\n            'fit_e_min': interactive_plot.children[21].value,\n            'fit_e_max': interactive_plot.children[22].value,\n            'color_mode': interactive_plot.children[23].value,\n            'show_max_marker': interactive_plot.children[24].value,\n            'show_second_max_marker': interactive_plot.children[25].value,\n            'division_point': interactive_plot.children[26].value,\n            'curve_type': interactive_plot.children[27].value\n        }\n        \n        plot_data = all_plots[current_values['scan_index'] - 1]\n        k_parallel = plot_data['k_parallel'][0]\n        energy_values = plot_data['energy_values']\n        data_values = plot_data['data_values']\n\n        # Convert xarray DataArrays to numpy arrays if necessary\n        if isinstance(k_parallel, xr.DataArray):\n            k_parallel = k_parallel.values\n        if isinstance(energy_values, xr.DataArray):\n            energy_values = energy_values.values\n        if isinstance(data_values, xr.DataArray):\n            data_values = data_values.values\n\n        # Apply filters only to the selected range\n        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]\n        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]\n        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]\n        k_parallel = k_parallel[valid_k_indices]\n        energy_values = energy_values[valid_e_indices]\n\n        if current_values['use_canny']:\n            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])\n            data_to_plot = edges.astype(float)\n\n        if current_values['enable_averaging']:\n            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)\n            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)\n            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')\n\n        if current_values['curve_type'] == 'EDC':\n            indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)\n            x_values = energy_values\n            y_label = 'Intensity (arb. units)'\n            x_label = 'Energy (eV)'\n        else:  # MDC\n            indices = np.linspace(0, len(valid_e_indices) - 1, current_values['n'], dtype=int)\n            x_values = k_parallel\n            y_label = 'Intensity (arb. units)'\n            x_label = 'k_parallel (Å⁻¹)'\n\n        # Get the current axes\n        ax = plt.gca()\n\n        for i, index in enumerate(indices):\n            if current_values['curve_type'] == 'EDC':\n                curve = data_to_plot[:, index]\n                actual_value = k_parallel[index]\n            else:  # MDC\n                curve = data_to_plot[index, :]\n                actual_value = energy_values[index]\n\n            if isinstance(actual_value, np.ndarray):\n                actual_value = actual_value.item()\n\n            # Apply normalization if not using Canny filter\n            if not current_values['use_canny']:\n                curve = curve / np.max(curve)\n\n            # Export original/processed data\n            np.savetxt(f\"{current_values['curve_type']}_{actual_value:.2f}.dat\", np.column_stack((x_values, curve)), header=f\"{x_label}\\t{y_label}\")\n\n            if current_values['show_fit']:\n                # Find the fit line in the plot\n                fit_line = None\n                for line in ax.lines:\n                    if line.get_label().startswith(f\"Fit {current_values['curve_type']}={actual_value:.2f}\"):\n                        fit_line = line\n                        break\n\n                if fit_line is not None:\n                    # Get the x and y data of the fit line\n                    fit_x_data, fit_y_data = fit_line.get_data()\n                    # Remove the vertical offset\n                    fit_y_data -= i * current_values['vertical_offset']\n                    # Export the fit data\n                    np.savetxt(f\"gaussian_fit_{current_values['curve_type']}_{actual_value:.2f}.dat\", np.column_stack((fit_x_data, fit_y_data)), header=f\"{x_label}\\tFitted {y_label}\")\n                else:\n                    print(f\"No fit found for {current_values['curve_type']} = {actual_value:.2f}\")\n\n        print(\"Data export completed.\")\n\n    interactive_plot = interactive(\n        plot_curves,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of Curves', continuous_update=True),\n        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),\n        show_curves=Checkbox(value=True, description='Show Curves'),\n        show_fit=Checkbox(value=False, description='Show Fits'),\n        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),\n        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),\n        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),\n        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),\n        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),\n        use_canny=Checkbox(value=False, description='Use Canny Filter'),\n        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),\n        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),\n        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),\n        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),\n        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),\n        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),\n        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),\n        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min (Å⁻¹)', continuous_update=True),\n        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max (Å⁻¹)', continuous_update=True),\n        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),\n        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),\n        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True),\n        color_mode=ToggleButtons(options=['Color', 'Grayscale'], description='Color Mode'),\n        show_max_marker=Checkbox(value=False, description='Show Max Marker'),\n        show_second_max_marker=Checkbox(value=False, description='Show Dual Max Markers'),\n        division_point=FloatSlider(value=(global_e_min + global_e_max) / 2, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Division Point', continuous_update=True),\n        curve_type=ToggleButtons(options=['EDC', 'MDC'], description='Curve Type')\n    )\n\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    export_button = Button(description=\"Export Data\")\n    export_button.on_click(export_data)\n\n    output = VBox([interactive_plot, HBox([save_button, export_button])])\n    return o", 20266)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.1.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.1.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import GaussianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
from ipywidgets import ToggleButtons

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_curves(scan_index, n, vertical_offset, show_curves, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max, color_mode, show_max_marker, show_second_max_marker, division_point, curve_type):
        plot_data = all_plots[scan_index - 1]
        ax.clear()

        # Get the full data
        data_to_plot = plot_data['data_values'].copy()
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]
            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]
            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]
            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')
            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data

        if curve_type == 'EDC':
            indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)
            x_values = energy_values
            y_label = 'Intensity (arb. units)'
            x_label = r'$E-E_f$ (eV)'
        else:  # MDC
            indices = np.linspace(0, len(valid_e_indices) - 1, n, dtype=int)
            x_values = k_parallel
            y_label = 'Intensity (arb. units)'
            x_label = r'$k_\parallel$ (Å⁻¹)'

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, index in enumerate(indices):
            if curve_type == 'EDC':
                curve = data_to_plot[:, index]
                actual_value = k_parallel[index]
                label = fr'$k_\parallel$ = {actual_value:.2f}'
            else:  # MDC
                curve = data_to_plot[index, :]
                actual_value = energy_values[index]
                label = fr'$E-E_f$ = {actual_value:.2f}'

            if isinstance(curve, xr.DataArray):
                curve = curve.values
            if isinstance(x_values, xr.DataArray):
                x_values = x_values.values

            if not use_canny:
                max_index = np.argmax(curve)
                curve = curve / np.max(curve)

            offset_curve = curve + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_curve))
            min_intensity = min(min_intensity, np.min(offset_curve))

            if show_curves:
                if color_mode == 'Color':
                    ax.plot(x_values, offset_curve, label=label)
                else:  # Grayscale
                    ax.plot(x_values, offset_curve, color='black')
                
                if show_max_marker and not use_canny:
                    ax.plot(x_values[max_index], offset_curve[max_index], 'bo', markersize=10, fillstyle='none')
                
                if show_second_max_marker and not use_canny:
                    left_max_index = np.argmax(curve[x_values <= division_point])
                    right_max_index = len(x_values) - 1 - np.argmax(curve[x_values > division_point][::-1])
                    ax.plot(x_values[left_max_index], offset_curve[left_max_index], 'ro', markersize=10, fillstyle='none')
                    ax.plot(x_values[right_max_index], offset_curve[right_max_index], 'bo', markersize=10, fillstyle='none')

            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Implement fitting logic here (similar to the original, but adapted for both EDC and MDC)
                pass

        # Add a vertical line at the division point
        if show_second_max_marker:
            ax.axvline(x=division_point, color='green', linestyle='--', alpha=0.5)

        ax.set_xlabel(x_label, fontsize=12, fontweight='bold')
        ax.set_ylabel(y_label, fontsize=12, fontweight='bold')
        ax.set_title(f'{curve_type}s - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        
        # Only show legend in Color mode
        if color_mode == 'Color':
            ax.legend()
        
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_xlim(e_min if curve_type == 'EDC' else k_min, e_max if curve_type == 'EDC' else k_max)
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity
    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_curves': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'avg_e_min': interactive_plot.children[16].value,
            'avg_e_max': interactive_plot.children[17].value,
            'avg_k_min': interactive_plot.children[18].value,
            'avg_k_max': interactive_plot.children[19].value,
            'fit_type': interactive_plot.children[20].value,
            'fit_e_min': interactive_plot.children[21].value,
            'fit_e_max': interactive_plot.children[22].value,
            'color_mode': interactive_plot.children[23].value,
            'show_max_marker': interactive_plot.children[24].value,
            'show_second_max_marker': interactive_plot.children[25].value,
            'division_point': interactive_plot.children[26].value,
            'curve_type': interactive_plot.children[27].value
        }
        plot_curves(**current_values)
        filename = f"{current_values['curve_type']}_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def export_data(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_curves': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'avg_e_min': interactive_plot.children[16].value,
            'avg_e_max': interactive_plot.children[17].value,
            'avg_k_min': interactive_plot.children[18].value,
            'avg_k_max': interactive_plot.children[19].value,
            'fit_type': interactive_plot.children[20].value,
            'fit_e_min': interactive_plot.children[21].value,
            'fit_e_max': interactive_plot.children[22].value,
            'color_mode': interactive_plot.children[23].value,
            'show_max_marker': interactive_plot.children[24].value,
            'show_second_max_marker': interactive_plot.children[25].value,
            'division_point': interactive_plot.children[26].value,
            'curve_type': interactive_plot.children[27].value
        }
        
        plot_data = all_plots[current_values['scan_index'] - 1]
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']
        data_values = plot_data['data_values']

        # Convert xarray DataArrays to numpy arrays if necessary
        if isinstance(k_parallel, xr.DataArray):
            k_parallel = k_parallel.values
        if isinstance(energy_values, xr.DataArray):
            energy_values = energy_values.values
        if isinstance(data_values, xr.DataArray):
            data_values = data_values.values

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]
        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]
        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if current_values['use_canny']:
            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])
            data_to_plot = edges.astype(float)

        if current_values['enable_averaging']:
            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')

        if current_values['curve_type'] == 'EDC':
            indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)
            x_values = energy_values
            y_label = 'Intensity (arb. units)'
            x_label = 'Energy (eV)'
        else:  # MDC
            indices = np.linspace(0, len(valid_e_indices) - 1, current_values['n'], dtype=int)
            x_values = k_parallel
            y_label = 'Intensity (arb. units)'
            x_label = 'k_parallel (Å⁻¹)'

        # Get the current axes
        ax = plt.gca()

        for i, index in enumerate(indices):
            if current_values['curve_type'] == 'EDC':
                curve = data_to_plot[:, index]
                actual_value = k_parallel[index]
            else:  # MDC
                curve = data_to_plot[index, :]
                actual_value = energy_values[index]

            if isinstance(actual_value, np.ndarray):
                actual_value = actual_value.item()

            # Apply normalization if not using Canny filter
            if not current_values['use_canny']:
                curve = curve / np.max(curve)

            # Export original/processed data
            np.savetxt(f"{current_values['curve_type']}_{actual_value:.2f}.dat", np.column_stack((x_values, curve)), header=f"{x_label}\t{y_label}")

            if current_values['show_fit']:
                # Find the fit line in the plot
                fit_line = None
                for line in ax.lines:
                    if line.get_label().startswith(f"Fit {current_values['curve_type']}={actual_value:.2f}"):
                        fit_line = line
                        break

                if fit_line is not None:
                    # Get the x and y data of the fit line
                    fit_x_data, fit_y_data = fit_line.get_data()
                    # Remove the vertical offset
                    fit_y_data -= i * current_values['vertical_offset']
                    # Export the fit data
                    np.savetxt(f"gaussian_fit_{current_values['curve_type']}_{actual_value:.2f}.dat", np.column_stack((fit_x_data, fit_y_data)), header=f"{x_label}\tFitted {y_label}")
                else:
                    print(f"No fit found for {current_values['curve_type']} = {actual_value:.2f}")

        print("Data export completed.")

    interactive_plot = interactive(
        plot_curves,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of Curves', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
        show_curves=Checkbox(value=True, description='Show Curves'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),
        use_canny=Checkbox(value=False, description='Use Canny Filter'),
        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),
        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),
        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),
        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min (Å⁻¹)', continuous_update=True),
        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max (Å⁻¹)', continuous_update=True),
        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),
        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),
        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True),
        color_mode=ToggleButtons(options=['Color', 'Grayscale'], description='Color Mode'),
        show_max_marker=Checkbox(value=False, description='Show Max Marker'),
        show_second_max_marker=Checkbox(value=False, description='Show Dual Max Markers'),
        division_point=FloatSlider(value=(global_e_min + global_e_max) / 2, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Division Point', continuous_update=True),
        curve_type=ToggleButtons(options=['EDC', 'MDC'], description='Curve Type')
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    export_button = Button(description="Export Data")
    export_button.on_click(export_data)

    output = VBox([interactive_plot, HBox([save_button, export_button])])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import GaussianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
from ipywidgets import ToggleButtons

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_curves(scan_index, n, vertical_offset, show_curves, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max, color_mode, show_max_marker, show_second_max_marker, division_point, curve_type):
        plot_data = all_plots[scan_index - 1]
        ax.clear()

        # Get the full data
        data_to_plot = plot_data['data_values'].copy()
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]
            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]
            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]
            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')
            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data

        if curve_type == 'EDC':
            indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)
            x_values = energy_values
            y_label = 'Intensity (arb. units)'
            x_label = r'$E-E_f$ (eV)'
        else:  # MDC
            indices = np.linspace(0, len(valid_e_indices) - 1, n, dtype=int)
            x_values = k_parallel
            y_label = 'Intensity (arb. units)'
            x_label = r'$k_\parallel$ ()'

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, index in enumerate(indices):
            if curve_type == 'EDC':
                curve = data_to_plot[:, index]
                actual_value = k_parallel[index]
                label = fr'$k_\parallel$ = {actual_value:.2f}'
            else:  # MDC
                curve = data_to_plot[index, :]
                actual_value = energy_values[index]
                label = fr'$E-E_f$ = {actual_value:.2f}'

            if isinstance(curve, xr.DataArray):
                curve = curve.values
            if isinstance(x_values, xr.DataArray):
                x_values = x_values.values

            if not use_canny:
                max_index = np.argmax(curve)
                curve = curve / np.max(curve)

            offset_curve = curve + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_curve))
            min_intensity = min(min_intensity, np.min(offset_curve))

            if show_curves:
                if color_mode == 'Color':
                    ax.plot(x_values, offset_curve, label=label)
                else:  # Grayscale
                    ax.plot(x_values, offset_curve, color='black')
                
                if show_max_marker and not use_canny:
                    ax.plot(x_values[max_index], offset_curve[max_index], 'bo', markersize=10, fillstyle='none')
                
                if show_second_max_marker and not use_canny:
                    left_max_index = np.argmax(curve[x_values <= division_point])
                    right_max_index = len(x_values) - 1 - np.argmax(curve[x_values > division_point][::-1])
                    ax.plot(x_values[left_max_index], offset_curve[left_max_index], 'ro', markersize=10, fillstyle='none')
                    ax.plot(x_values[right_max_index], offset_curve[right_max_index], 'bo', markersize=10, fillstyle='none')

            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Implement fitting logic here (similar to the original, but adapted for both EDC and MDC)
                pass

        # Add a vertical line at the division point
        if show_second_max_marker:
            ax.axvline(x=division_point, color='green', linestyle='--', alpha=0.5)

        ax.set_xlabel(x_label, fontsize=12, fontweight='bold')
        ax.set_ylabel(y_label, fontsize=12, fontweight='bold')
        ax.set_title(f'{curve_type}s - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        
        # Only show legend in Color mode
        if color_mode == 'Color':
            ax.legend()
        
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_xlim(e_min if curve_type == 'EDC' else k_min, e_max if curve_type == 'EDC' else k_max)
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity
    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_curves': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'avg_e_min': interactive_plot.children[16].value,
            'avg_e_max': interactive_plot.children[17].value,
            'avg_k_min': interactive_plot.children[18].value,
            'avg_k_max': interactive_plot.children[19].value,
            'fit_type': interactive_plot.children[20].value,
            'fit_e_min': interactive_plot.children[21].value,
            'fit_e_max': interactive_plot.children[22].value,
            'color_mode': interactive_plot.children[23].value,
            'show_max_marker': interactive_plot.children[24].value,
            'show_second_max_marker': interactive_plot.children[25].value,
            'division_point': interactive_plot.children[26].value,
            'curve_type': interactive_plot.children[27].value
        }
        plot_curves(**current_values)
        filename = f"{current_values['curve_type']}_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def export_data(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_curves': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'avg_e_min': interactive_plot.children[16].value,
            'avg_e_max': interactive_plot.children[17].value,
            'avg_k_min': interactive_plot.children[18].value,
            'avg_k_max': interactive_plot.children[19].value,
            'fit_type': interactive_plot.children[20].value,
            'fit_e_min': interactive_plot.children[21].value,
            'fit_e_max': interactive_plot.children[22].value,
            'color_mode': interactive_plot.children[23].value,
            'show_max_marker': interactive_plot.children[24].value,
            'show_second_max_marker': interactive_plot.children[25].value,
            'division_point': interactive_plot.children[26].value,
            'curve_type': interactive_plot.children[27].value
        }
        
        plot_data = all_plots[current_values['scan_index'] - 1]
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']
        data_values = plot_data['data_values']

        # Convert xarray DataArrays to numpy arrays if necessary
        if isinstance(k_parallel, xr.DataArray):
            k_parallel = k_parallel.values
        if isinstance(energy_values, xr.DataArray):
            energy_values = energy_values.values
        if isinstance(data_values, xr.DataArray):
            data_values = data_values.values

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]
        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]
        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if current_values['use_canny']:
            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])
            data_to_plot = edges.astype(float)

        if current_values['enable_averaging']:
            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')

        if current_values['curve_type'] == 'EDC':
            indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)
            x_values = energy_values
            y_label = 'Intensity (arb. units)'
            x_label = 'Energy (eV)'
        else:  # MDC
            indices = np.linspace(0, len(valid_e_indices) - 1, current_values['n'], dtype=int)
            x_values = k_parallel
            y_label = 'Intensity (arb. units)'
            x_label = 'k_parallel (Å⁻¹)'

        # Get the current axes
        ax = plt.gca()

        for i, index in enumerate(indices):
            if current_values['curve_type'] == 'EDC':
                curve = data_to_plot[:, index]
                actual_value = k_parallel[index]
            else:  # MDC
                curve = data_to_plot[index, :]
                actual_value = energy_values[index]

            if isinstance(actual_value, np.ndarray):
                actual_value = actual_value.item()

            # Apply normalization if not using Canny filter
            if not current_values['use_canny']:
                curve = curve / np.max(curve)

            # Export original/processed data
            np.savetxt(f"{current_values['curve_type']}_{actual_value:.2f}.dat", np.column_stack((x_values, curve)), header=f"{x_label}\t{y_label}")

            if current_values['show_fit']:
                # Find the fit line in the plot
                fit_line = None
                for line in ax.lines:
                    if line.get_label().startswith(f"Fit {current_values['curve_type']}={actual_value:.2f}"):
                        fit_line = line
                        break

                if fit_line is not None:
                    # Get the x and y data of the fit line
                    fit_x_data, fit_y_data = fit_line.get_data()
                    # Remove the vertical offset
                    fit_y_data -= i * current_values['vertical_offset']
                    # Export the fit data
                    np.savetxt(f"gaussian_fit_{current_values['curve_type']}_{actual_value:.2f}.dat", np.column_stack((fit_x_data, fit_y_data)), header=f"{x_label}\tFitted {y_label}")
                else:
                    print(f"No fit found for {current_values['curve_type']} = {actual_value:.2f}")

        print("Data export completed.")

    interactive_plot = interactive(
        plot_curves,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of Curves', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
        show_curves=Checkbox(value=True, description='Show Curves'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min ()', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max ()', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),
        use_canny=Checkbox(value=False, description='Use Canny Filter'),
        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),
        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),
        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),
        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min ()', continuous_update=True),
        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max ()', continuous_update=True),
        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),
        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),
        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True),
        color_mode=ToggleButtons(options=['Color', 'Grayscale'], description='Color Mode'),
        show_max_marker=Checkbox(value=False, description='Show Max Marker'),
        show_second_max_marker=Checkbox(value=False, description='Show Dual Max Markers'),
        division_point=FloatSlider(value=(global_e_min + global_e_max) / 2, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Division Point', continuous_update=True),
        curve_type=ToggleButtons(options=['EDC', 'MDC'], description='Curve Type')
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    export_button = Button(description="Export Data")
    export_button.on_click(export_data)

    output = VBox([interactive_plot, HBox([save_button, export_button])])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import GaussianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
from ipywidgets import ToggleButtons

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_curves(scan_index, n, vertical_offset, show_curves, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max, color_mode, show_max_marker, show_second_max_marker, division_point, curve_type):
        plot_data = all_plots[scan_index - 1]
        ax.clear()

        # Get the full data
        data_to_plot = plot_data['data_values'].copy()
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]
            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]
            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]
            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')
            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data

        if curve_type == 'EDC':
            indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)
            x_values = energy_values
            y_label = 'Intensity (arb. units)'
            x_label = r'$E-E_f$ (eV)'
            division_min, division_max = e_min, e_max
        else:  # MDC
            indices = np.linspace(0, len(valid_e_indices) - 1, n, dtype=int)
            x_values = k_parallel
            y_label = 'Intensity (arb. units)'
            x_label = r'$k_\parallel$ (Å⁻¹)'
            division_min, division_max = k_min, k_max

        # Ensure division_point is within the current bounds
        division_point = max(division_min, min(division_max, division_point))

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, index in enumerate(indices):
            if curve_type == 'EDC':
                curve = data_to_plot[:, index]
                actual_value = k_parallel[index]
                label = fr'$k_\parallel$ = {actual_value:.2f}'
            else:  # MDC
                curve = data_to_plot[index, :]
                actual_value = energy_values[index]
                label = fr'$E-E_f$ = {actual_value:.2f}'

            if isinstance(curve, xr.DataArray):
                curve = curve.values
            if isinstance(x_values, xr.DataArray):
                x_values = x_values.values

            if not use_canny:
                max_index = np.argmax(curve)
                curve = curve / np.max(curve)

            offset_curve = curve + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_curve))
            min_intensity = min(min_intensity, np.min(offset_curve))

            if show_curves:
                if color_mode == 'Color':
                    ax.plot(x_values, offset_curve, label=label)
                else:  # Grayscale
                    ax.plot(x_values, offset_curve, color='black')
                
                if show_max_marker and not use_canny:
                    ax.plot(x_values[max_index], offset_curve[max_index], 'bo', markersize=10, fillstyle='none')
                
                if show_second_max_marker and not use_canny:
                    left_max_index = np.argmax(curve[x_values <= division_point])
                    right_max_index = len(x_values) - 1 - np.argmax(curve[x_values > division_point][::-1])
                    ax.plot(x_values[left_max_index], offset_curve[left_max_index], 'ro', markersize=10, fillstyle='none')
                    ax.plot(x_values[right_max_index], offset_curve[right_max_index], 'bo', markersize=10, fillstyle='none')

            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Filter data for fitting
                fit_indices = np.where((x_values >= fit_e_min) & (x_values <= fit_e_max))[0]
                fit_x_values = x_values[fit_indices]
                fit_curve = curve[fit_indices]

                if fit_type == 'Maxima':
                    peaks, _ = find_peaks(fit_curve)
                    peak_heights = fit_curve[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(fit_curve))

                    for j, peak in enumerate(largest_peaks):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_x_values[peak], min=fit_x_values.min(), max=fit_x_values.max())
                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                elif fit_type == 'Minima':
                    valleys, _ = find_peaks(-fit_curve)
                    valley_depths = np.max(fit_curve) - fit_curve[valleys]
                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]
                    largest_valleys = valleys[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(fit_curve))

                    for j, valley in enumerate(largest_valleys):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_x_values[valley], min=fit_x_values.min(), max=fit_x_values.max())
                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(fit_curve, params, x=fit_x_values)
                fit = result.best_fit

                # Interpolate the fit back to the full x range
                full_fit = np.interp(x_values, fit_x_values, fit)
                offset_fit = full_fit + i * vertical_offset

                # Extract sigma values and their uncertainties for label
                sigmas = []
                sigma_errors = []
                for j in range(num_peaks):
                    sigma = abs(result.params[f'g{j+1}_sigma'].value)
                    sigma_error = result.params[f'g{j+1}_sigma'].stderr
                    sigmas.append(sigma)
                    sigma_errors.append(sigma_error)

                sigma_label = ', '.join([fr'$\sigma_{j+1}$={sigma:.3f} $\pm$ {error:.3f}' for j, (sigma, error) in enumerate(zip(sigmas, sigma_errors))])

                if color_mode == 'Color':
                    ax.plot(x_values, offset_fit, '--', label=fr'Fit {curve_type}={actual_value:.2f}, {sigma_label}', color=f'C{i}')
                else:  # Grayscale
                    ax.plot(x_values, offset_fit, '--', color='black')

                if fit_type == 'Maxima':
                    fit_peaks, _ = find_peaks(full_fit)
                    ax.plot(x_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)

                    for j, peak in enumerate(fit_peaks):
                        peak_x = x_values[peak]
                        peak_intensity = offset_fit[peak]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_x = peak_x - sigma
                        right_sigma_x = peak_x + sigma
                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)

                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')

                elif fit_type == 'Minima':
                    fit_valleys, _ = find_peaks(-full_fit)
                    ax.plot(x_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

                    for j, valley in enumerate(fit_valleys):
                        valley_x = x_values[valley]
                        valley_intensity = offset_fit[valley]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_x = valley_x - sigma
                        right_sigma_x = valley_x + sigma
                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)

                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')

        # Add a vertical line at the division point
        if show_second_max_marker:
            ax.axvline(x=division_point, color='green', linestyle='--', alpha=0.5)

        ax.set_xlabel(x_label, fontsize=12, fontweight='bold')
        ax.set_ylabel(y_label, fontsize=12, fontweight='bold')
        ax.set_title(f'{curve_type}s - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        
        # Only show legend in Color mode
        if color_mode == 'Color':
            ax.legend()
        
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_xlim(e_min if curve_type == 'EDC' else k_min, e_max if curve_type == 'EDC' else k_max)
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity
    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_curves': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'avg_e_min': interactive_plot.children[16].value,
            'avg_e_max': interactive_plot.children[17].value,
            'avg_k_min': interactive_plot.children[18].value,
            'avg_k_max': interactive_plot.children[19].value,
            'fit_type': interactive_plot.children[20].value,
            'fit_e_min': interactive_plot.children[21].value,
            'fit_e_max': interactive_plot.children[22].value,
            'color_mode': interactive_plot.children[23].value,
            'show_max_marker': interactive_plot.children[24].value,
            'show_second_max_marker': interactive_plot.children[25].value,
            'division_point': interactive_plot.children[26].value,
            'curve_type': interactive_plot.children[27].value
        }
        plot_curves(**current_values)
        filename = f"{current_values['curve_type']}_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def export_data(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_curves': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'avg_e_min': interactive_plot.children[16].value,
            'avg_e_max': interactive_plot.children[17].value,
            'avg_k_min': interactive_plot.children[18].value,
            'avg_k_max': interactive_plot.children[19].value,
            'fit_type': interactive_plot.children[20].value,
            'fit_e_min': interactive_plot.children[21].value,
            'fit_e_max': interactive_plot.children[22].value,
            'color_mode': interactive_plot.children[23].value,
            'show_max_marker': interactive_plot.children[24].value,
            'show_second_max_marker': interactive_plot.children[25].value,
            'division_point': interactive_plot.children[26].value,
            'curve_type': interactive_plot.children[27].value
        }
        
        plot_data = all_plots[current_values['scan_index'] - 1]
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']
        data_values = plot_data['data_values']

        # Convert xarray DataArrays to numpy arrays if necessary
        if isinstance(k_parallel, xr.DataArray):
            k_parallel = k_parallel.values
        if isinstance(energy_values, xr.DataArray):
            energy_values = energy_values.values
        if isinstance(data_values, xr.DataArray):
            data_values = data_values.values

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]
        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]
        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if current_values['use_canny']:
            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])
            data_to_plot = edges.astype(float)

        if current_values['enable_averaging']:
            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')

        if current_values['curve_type'] == 'EDC':
            indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)
            x_values = energy_values
            y_label = 'Intensity (arb. units)'
            x_label = 'Energy (eV)'
        else:  # MDC
            indices = np.linspace(0, len(valid_e_indices) - 1, current_values['n'], dtype=int)
            x_values = k_parallel
            y_label = 'Intensity (arb. units)'
            x_label = 'k_parallel (Å⁻¹)'

        # Get the current axes
        ax = plt.gca()

        for i, index in enumerate(indices):
            if current_values['curve_type'] == 'EDC':
                curve = data_to_plot[:, index]
                actual_value = k_parallel[index]
            else:  # MDC
                curve = data_to_plot[index, :]
                actual_value = energy_values[index]

            if isinstance(actual_value, np.ndarray):
                actual_value = actual_value.item()

            # Apply normalization if not using Canny filter
            if not current_values['use_canny']:
                curve = curve / np.max(curve)

            # Export original/processed data
            np.savetxt(f"{current_values['curve_type']}_{actual_value:.2f}.dat", np.column_stack((x_values, curve)), header=f"{x_label}\t{y_label}")

            if current_values['show_fit']:
                # Find the fit line in the plot
                fit_line = None
                for line in ax.lines:
                    if line.get_label().startswith(f"Fit {current_values['curve_type']}={actual_value:.2f}"):
                        fit_line = line
                        break

                if fit_line is not None:
                    # Get the x and y data of the fit line
                    fit_x_data, fit_y_data = fit_line.get_data()
                    # Remove the vertical offset
                    fit_y_data -= i * current_values['vertical_offset']
                    # Export the fit data
                    np.savetxt(f"gaussian_fit_{current_values['curve_type']}_{actual_value:.2f}.dat", np.column_stack((fit_x_data, fit_y_data)), header=f"{x_label}\tFitted {y_label}")
                else:
                    print(f"No fit found for {current_values['curve_type']} = {actual_value:.2f}")

        print("Data export completed.")

    class DynamicFloatSlider(FloatSlider):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.observe(self._update_bounds, names=['value'])

        def _update_bounds(self, change):
            self.min = min(self.min, change['new'])
            self.max = max(self.max, change['new'])

    def update_division_point(*args):
        curve_type = interactive_plot.children[-1].value
        if curve_type == 'EDC':
            e_min = interactive_plot.children[8].value
            e_max = interactive_plot.children[9].value
            interactive_plot.children[-2].min = e_min
            interactive_plot.children[-2].max = e_max
        else:  # MDC
            k_min = interactive_plot.children[6].value
            k_max = interactive_plot.children[7].value
            interactive_plot.children[-2].min = k_min
            interactive_plot.children[-2].max = k_max

    interactive_plot = interactive(
        plot_curves,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of Curves', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
        show_curves=Checkbox(value=True, description='Show Curves'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),
        use_canny=Checkbox(value=False, description='Use Canny Filter'),
        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),
        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),
        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),
        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min (Å⁻¹)', continuous_update=True),
        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max (Å⁻¹)', continuous_update=True),
        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),
        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),
        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True),
        color_mode=ToggleButtons(options=['Color', 'Grayscale'], description='Color Mode'),
        show_max_marker=Checkbox(value=False, description='Show Max Marker'),
        show_second_max_marker=Checkbox(value=False, description='Show Dual Max Markers'),
        division_point=DynamicFloatSlider(value=(global_e_min + global_e_max) / 2, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Division Point', continuous_update=True),
        curve_type=ToggleButtons(options=['EDC', 'MDC'], description='Curve Type')
    )

    # Observe changes in curve_type, e_min, e_max, k_min, and k_max
    interactive_plot.children[-1].observe(update_division_point, names='value')
    interactive_plot.children[6].observe(update_division_point, names='value')
    interactive_plot.children[7].observe(update_division_point, names='value')
    interactive_plot.children[8].observe(update_division_point, names='value')
    interactive_plot.children[9].observe(update_division_point, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    export_button = Button(description="Export Data")
    export_button.on_click(export_data)

    output = VBox([interactive_plot, HBox([save_button, export_button])])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.2": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons\nfrom matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm\nfrom IPython.display import display, clear_output\nimport tkinter as tk\nfrom tkinter import filedialog\nimport warnings\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nfrom matplotlib import MatplotlibDeprecationWarning\nfrom lmfit.models import GaussianModel, LinearModel\nfrom scipy.signal import find_peaks\nfrom skimage.feature import canny\nfrom scipy.ndimage import convolve\nfrom ipywidgets import ToggleButtons\n\n%matplotlib widget\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",\n    \"axes.labelcolor\": \"black\",\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()\n    return [os.path.join(folder_path, f) for f in data_files]\n\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef edc_plot(data_files, work_function):\n    # Constants\n    hbar = 1.054571817e-34  # J*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    global_k_min = float('inf')\n    global_k_max = float('-inf')\n    global_e_min = float('inf')\n    global_e_max = float('-inf')\n    global_intensity_max = float('-inf')\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']\n\n        # Calculate kinetic energy and momentum\n        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19\n        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10\n\n        # Get the energy values\n        energy_values = -data.eV.values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        # Update global ranges\n        global_k_min = min(global_k_min, np.min(k_parallel))\n        global_k_max = max(global_k_max, np.max(k_parallel))\n        global_e_min = min(global_e_min, np.min(energy_values))\n        global_e_max = max(global_e_max, np.max(energy_values))\n        global_intensity_max = max(global_intensity_max, np.max(data.values))\n\n    global_k_range = global_k_max - global_k_min\n    global_e_range = global_e_max - global_e_min\n\n    fig, ax = plt.subplots(figsize=(10, 8))\n\n    def plot_curves(scan_index, n, vertical_offset, show_curves, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max, color_mode, show_max_marker, show_second_max_marker, division_point, curve_type):\n        plot_data = all_plots[scan_index - 1]\n        ax.clear()\n\n        # Get the full data\n        data_to_plot = plot_data['data_values'].copy()\n        k_parallel = plot_data['k_parallel'][0]\n        energy_values = plot_data['energy_values']\n\n        # Apply filters only to the selected range\n        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]\n        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]\n        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]\n        k_parallel = k_parallel[valid_k_indices]\n        energy_values = energy_values[valid_e_indices]\n\n        if use_canny:\n            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)\n            data_to_plot = edges.astype(float)\n\n        if enable_averaging:\n            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)\n            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)\n            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]\n            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]\n            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]\n            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')\n            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data\n\n        if curve_type == 'EDC':\n            indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)\n            x_values = energy_values\n            y_label = 'Intensity (arb. units)'\n            x_label = r'$E-E_f$ (eV)'\n            division_min, division_max = e_min, e_max\n        else:  # MDC\n            indices = np.linspace(0, len(valid_e_indices) - 1, n, dtype=int)\n            x_values = k_parallel\n            y_label = 'Intensity (arb. units)'\n            x_label = r'$k_\\parallel$ ($\\AA^{-1}$)'\n            division_min, division_max = k_min, k_max\n\n        # Ensure division_point is within the current bounds\n        division_point = max(division_min, min(division_max, division_point))\n\n        max_intensity = float('-inf')\n        min_intensity = float('inf')\n\n        for i, index in enumerate(indices):\n            if curve_type == 'EDC':\n                curve = data_to_plot[:, index]\n                actual_value = k_parallel[index]\n                label = fr'$k_\\parallel$ = {actual_value:.2f}'\n            else:  # MDC\n                curve = data_to_plot[index, :]\n                actual_value = energy_values[index]\n                label = fr'$E-E_f$ = {actual_value:.2f}'\n\n            if isinstance(curve, xr.DataArray):\n                curve = curve.values\n            if isinstance(x_values, xr.DataArray):\n                x_values = x_values.values\n\n            if not use_canny:\n                max_index = np.argmax(curve)\n                curve = curve / np.max(curve)\n\n            offset_curve = curve + i * vertical_offset\n            max_intensity = max(max_intensity, np.max(offset_curve))\n            min_intensity = min(min_intensity, np.min(offset_curve))\n\n            if show_curves:\n                if color_mode == 'Color':\n                    ax.plot(x_values, offset_curve, label=label)\n                else:  # Grayscale\n                    ax.plot(x_values, offset_curve, color='black')\n                \n                if show_max_marker and not use_canny:\n                    ax.plot(x_values[max_index], offset_curve[max_index], 'bo', markersize=10, fillstyle='none')\n                \n                if show_second_max_marker and not use_canny:\n                    left_max_index = np.argmax(curve[x_values <= division_point])\n                    right_max_index = len(x_values) - 1 - np.argmax(curve[x_values > division_point][::-1])\n                    ax.plot(x_values[left_max_index], offset_curve[left_max_index], 'ro', markersize=10, fillstyle='none')\n                    ax.plot(x_values[right_max_index], offset_curve[right_max_index], 'bo', markersize=10, fillstyle='none')\n\n            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)\n\n            if show_fit:\n                # Filter data for fitting\n                fit_indices = np.where((x_values >= fit_e_min) & (x_values <= fit_e_max))[0]\n                fit_x_values = x_values[fit_indices]\n                fit_curve = curve[fit_indices]\n\n                if fit_type == 'Maxima':\n                    peaks, _ = find_peaks(fit_curve)\n                    peak_heights = fit_curve[peaks]\n                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]\n                    largest_peaks = peaks[sorted_indices]\n\n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(fit_curve))\n\n                    for j, peak in enumerate(largest_peaks):\n                        gaussian = GaussianModel(prefix=f'g{j+1}_')\n                        model += gaussian\n                        params.update(gaussian.make_params())\n                        params[f'g{j+1}_center'].set(value=fit_x_values[peak], min=fit_x_values.min(), max=fit_x_values.max())\n                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)\n\n                elif fit_type == 'Minima':\n                    valleys, _ = find_peaks(-fit_curve)\n                    valley_depths = np.max(fit_curve) - fit_curve[valleys]\n                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]\n                    largest_valleys = valleys[sorted_indices]\n\n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(fit_curve))\n\n                    for j, valley in enumerate(largest_valleys):\n                        gaussian = GaussianModel(prefix=f'g{j+1}_')\n                        model += gaussian\n                        params.update(gaussian.make_params())\n                        params[f'g{j+1}_center'].set(value=fit_x_values[valley], min=fit_x_values.min(), max=fit_x_values.max())\n                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])\n                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)\n\n                result = model.fit(fit_curve, params, x=fit_x_values)\n                fit = result.best_fit\n\n                # Interpolate the fit back to the full x range\n                full_fit = np.interp(x_values, fit_x_values, fit)\n                offset_fit = full_fit + i * vertical_offset\n\n                # Extract sigma values and their uncertainties for label\n                sigmas = []\n                sigma_errors = []\n                for j in range(num_peaks):\n                    sigma = abs(result.params[f'g{j+1}_sigma'].value)\n                    sigma_error = result.params[f'g{j+1}_sigma'].stderr\n                    sigmas.append(sigma)\n                    sigma_errors.append(sigma_error)\n\n                sigma_label = ', '.join([fr'$\\sigma_{j+1}$={sigma:.3f} $\\pm$ {error:.3f}' for j, (sigma, error) in enumerate(zip(sigmas, sigma_errors))])\n\n                if color_mode == 'Color':\n                    ax.plot(x_values, offset_fit, '--', label=fr'Fit {curve_type}={actual_value:.2f}, {sigma_label}', color=f'C{i}')\n                else:  # Grayscale\n                    ax.plot(x_values, offset_fit, '--', color='black')\n\n                if fit_type == 'Maxima':\n                    fit_peaks, _ = find_peaks(full_fit)\n                    ax.plot(x_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)\n\n                    for j, peak in enumerate(fit_peaks):\n                        peak_x = x_values[peak]\n                        peak_intensity = offset_fit[peak]\n                        sigma = sigmas[j]\n                        sigma_error = sigma_errors[j]\n                        left_sigma_x = peak_x - sigma\n                        right_sigma_x = peak_x + sigma\n                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)\n                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)\n\n                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)\n                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)\n                        ax.annotate(fr'$-\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')\n                        ax.annotate(fr'$+\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')\n\n                elif fit_type == 'Minima':\n                    fit_valleys, _ = find_peaks(-full_fit)\n                    ax.plot(x_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)\n\n                    for j, valley in enumerate(fit_valleys):\n                        valley_x = x_values[valley]\n                        valley_intensity = offset_fit[valley]\n                        sigma = sigmas[j]\n                        sigma_error = sigma_errors[j]\n                        left_sigma_x = valley_x - sigma\n                        right_sigma_x = valley_x + sigma\n                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)\n                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)\n\n                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)\n                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)\n                        ax.annotate(fr'$-\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')\n                        ax.annotate(fr'$+\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')\n\n        # Add a vertical line at the division point\n        if show_second_max_marker:\n            ax.axvline(x=division_point, color='green', linestyle='--', alpha=0.5)\n\n        ax.set_xlabel(x_label, fontsize=12, fontweight='bold')\n        ax.set_ylabel(y_label, fontsize=12, fontweight='bold')\n        ax.set_title(f'{curve_type}s - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        \n        # Only show legend in Color mode\n        if color_mode == 'Color':\n            ax.legend()\n        \n        ax.tick_params(axis='both', which='major', labelsize=10)\n        ax.set_xlim(e_min if curve_type == 'EDC' else k_min, e_max if curve_type == 'EDC' else k_max)\n        y_range = max_intensity - min_intensity\n        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)\n\n        plt.tight_layout()\n        fig.canvas.draw_idle()\n\n        return max_intensity - min_intensity\n    def save_plot(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_curves': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'use_canny': interactive_plot.children[10].value,\n            'sigma': interactive_plot.children[11].value,\n            'low_threshold': interactive_plot.children[12].value,\n            'high_threshold': interactive_plot.children[13].value,\n            'enable_averaging': interactive_plot.children[14].value,\n            'averaging_kernel_size': interactive_plot.children[15].value,\n            'avg_e_min': interactive_plot.children[16].value,\n            'avg_e_max': interactive_plot.children[17].value,\n            'avg_k_min': interactive_plot.children[18].value,\n            'avg_k_max': interactive_plot.children[19].value,\n            'fit_type': interactive_plot.children[20].value,\n            'fit_e_min': interactive_plot.children[21].value,\n            'fit_e_max': interactive_plot.children[22].value,\n            'color_mode': interactive_plot.children[23].value,\n            'show_max_marker': interactive_plot.children[24].value,\n            'show_second_max_marker': interactive_plot.children[25].value,\n            'division_point': interactive_plot.children[26].value,\n            'curve_type': interactive_plot.children[27].value\n        }\n        plot_curves(**current_values)\n        filename = f\"{current_values['curve_type']}_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        print(f\"Plot saved as {filename}\")\n\n    def export_data(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_curves': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'use_canny': interactive_plot.children[10].value,\n            'sigma': interactive_plot.children[11].value,\n            'low_threshold': interactive_plot.children[12].value,\n            'high_threshold': interactive_plot.children[13].value,\n            'enable_averaging': interactive_plot.children[14].value,\n            'averaging_kernel_size': interactive_plot.children[15].value,\n            'avg_e_min': interactive_plot.children[16].value,\n            'avg_e_max': interactive_plot.children[17].value,\n            'avg_k_min': interactive_plot.children[18].value,\n            'avg_k_max': interactive_plot.children[19].value,\n            'fit_type': interactive_plot.children[20].value,\n            'fit_e_min': interactive_plot.children[21].value,\n            'fit_e_max': interactive_plot.children[22].value,\n            'color_mode': interactive_plot.children[23].value,\n            'show_max_marker': interactive_plot.children[24].value,\n            'show_second_max_marker': interactive_plot.children[25].value,\n            'division_point': interactive_plot.children[26].value,\n            'curve_type': interactive_plot.children[27].value\n        }\n        \n        plot_data = all_plots[current_values['scan_index'] - 1]\n        k_parallel = plot_data['k_parallel'][0]\n        energy_values = plot_data['energy_values']\n        data_values = plot_data['data_values']\n\n        # Convert xarray DataArrays to numpy arrays if necessary\n        if isinstance(k_parallel, xr.DataArray):\n            k_parallel = k_parallel.values\n        if isinstance(energy_values, xr.DataArray):\n            energy_values = energy_values.values\n        if isinstance(data_values, xr.DataArray):\n            data_values = data_values.values\n\n        # Apply filters only to the selected range\n        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]\n        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]\n        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]\n        k_parallel = k_parallel[valid_k_indices]\n        energy_values = energy_values[valid_e_indices]\n\n        if current_values['use_canny']:\n            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])\n            data_to_plot = edges.astype(float)\n\n        if current_values['enable_averaging']:\n            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)\n            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)\n            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')\n\n        if current_values['curve_type'] == 'EDC':\n            indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)\n            x_values = energy_values\n            y_label = 'Intensity (arb. units)'\n            x_label = 'Energy (eV)'\n        else:  # MDC\n            indices = np.linspace(0, len(valid_e_indices) - 1, current_values['n'], dtype=int)\n            x_values = k_parallel\n            y_label = 'Intensity (arb. units)'\n            x_label = \\f'k_parallel ($\\AA^{-1}$)'\n\n        # Get the current axes\n        ax = plt.gca()\n\n        for i, index in enumerate(indices):\n            if current_values['curve_type'] == 'EDC':\n                curve = data_to_plot[:, index]\n                actual_value = k_parallel[index]\n            else:  # MDC\n                curve = data_to_plot[index, :]\n                actual_value = energy_values[index]\n\n            if isinstance(actual_value, np.ndarray):\n                actual_value = actual_value.item()\n\n            # Apply normalization if not using Canny filter\n            if not current_values['use_canny']:\n                curve = curve / np.max(curve)\n\n            # Export original/processed data\n            np.savetxt(f\"{current_values['curve_type']}_{actual_value:.2f}.dat\", np.column_stack((x_values, curve)), header=f\"{x_label}\\t{y_label}\")\n\n            if current_values['show_fit']:\n                # Find the fit line in the plot\n                fit_line = None\n                for line in ax.lines:\n                    if line.get_label().startswith(f\"Fit {current_values['curve_type']}={actual_value:.2f}\"):\n                        fit_line = line\n                        break\n\n                if fit_line is not None:\n                    # Get the x and y data of the fit line\n                    fit_x_data, fit_y_data = fit_line.get_data()\n                    # Remove the vertical offset\n                    fit_y_data -= i * current_values['vertical_offset']\n                    # Export the fit data\n                    np.savetxt(f\"gaussian_fit_{current_values['curve_type']}_{actual_value:.2f}.dat\", np.column_stack((fit_x_data, fit_y_data)), header=f\"{x_label}\\tFitted {y_label}\")\n                else:\n                    print(f\"No fit found for {current_values['curve_type']} = {actual_value:.2f}\")\n\n        print(\"Data export completed.\")\n\n    class DynamicFloatSlider(FloatSlider):\n        def __init__(self, *args, **kwargs):\n            super().__init__(*args, **kwargs)\n            self.observe(self._update_bounds, names=['value'])\n\n        def _update_bounds(self, change):\n            self.min = min(self.min, change['new'])\n            self.max = max(self.max, change['new'])\n\n    def update_division_point(*args):\n        curve_type = interactive_plot.children[-1].value\n        if curve_type == 'EDC':\n            e_min = interactive_plot.children[8].value\n            e_max = interactive_plot.children[9].value\n            interactive_plot.children[-2].min = e_min\n            interactive_plot.children[-2].max = e_max\n        else:  # MDC\n            k_min = interactive_plot.children[6].value\n            k_max = interactive_plot.children[7].value\n            interactive_plot.children[-2].min = k_min\n            interactive_plot.children[-2].max = k_max\n\n    interactive_plot = interactive(\n        plot_curves,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n        n=IntSlider(value=5, min=1, max=40, step=1, description='Number of Curves', continuous_update=True),\n        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),\n        show_curves=Checkbox(value=True, description='Show Curves'),\n        show_fit=Checkbox(value=False, description='Show Fits'),\n        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),\n        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),\n        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),\n        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),\n        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),\n        use_canny=Checkbox(value=False, description='Use Canny Filter'),\n        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),\n        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),\n        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),\n        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),\n        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),\n        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),\n        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),\n        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min (Å⁻¹)', continuous_update=True),\n        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max (Å⁻¹)', continuous_update=True),\n        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),\n        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),\n        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True),\n        color_mode=ToggleButtons(options=['Color', 'Grayscale'], description='Color Mode'),\n        show_max_marker=Checkbox(value=False, description='Show Max Marker'),\n        show_second_max_marker=Checkbox(value=False, description='Show Dual Max Markers'),\n        division_point=DynamicFloatSlider(value=(global_e_min + global_e_max) / 2, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Division Point', continuous_update=True),\n        curve_type=ToggleButtons(options=['EDC', 'MDC'], description='Curve Type')\n    )\n\n    # Observe changes in curve_type, e_min, e_max, k_min, and k_max\n    interactive_plot.children[-1].observe(update_division_point, names='value')\n    interactive_plot.children[6].observe(update_division_point, names='value')\n    interactive_plot.children[7].observe(update_division_point, names='value')\n    interactive_plot.children[8].observe(update_division_point, names='value')\n    interactive_plot.children[9].observe(update_division_point, names='value')\n\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    export_button = Button(description=\"Export Data\")\n    export_button.on_click(export_data)\n\n    output = VBox([interactive_plot, HBox([save_button, export_button])])\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n    interactive_plot_with_save = edc_plot(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")", 20655)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.2.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.2.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.3": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons\nfrom matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm\nfrom IPython.display import display, clear_output\nimport tkinter as tk\nfrom tkinter import filedialog\nimport warnings\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nfrom matplotlib import MatplotlibDeprecationWarning\nfrom lmfit.models import GaussianModel, LinearModel\nfrom scipy.signal import find_peaks\nfrom skimage.feature import canny\nfrom scipy.ndimage import convolve\nfrom ipywidgets import ToggleButtons\n\n%matplotlib widget\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",\n    \"axes.labelcolor\": \"black\",\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()\n    return [os.path.join(folder_path, f) for f in data_files]\n\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef edc_plot(data_files, work_function):\n    # Constants\n    hbar = 1.054571817e-34  # J*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    global_k_min = float('inf')\n    global_k_max = float('-inf')\n    global_e_min = float('inf')\n    global_e_max = float('-inf')\n    global_intensity_max = float('-inf')\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']\n\n        # Calculate kinetic energy and momentum\n        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19\n        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10\n\n        # Get the energy values\n        energy_values = -data.eV.values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        # Update global ranges\n        global_k_min = min(global_k_min, np.min(k_parallel))\n        global_k_max = max(global_k_max, np.max(k_parallel))\n        global_e_min = min(global_e_min, np.min(energy_values))\n        global_e_max = max(global_e_max, np.max(energy_values))\n        global_intensity_max = max(global_intensity_max, np.max(data.values))\n\n    global_k_range = global_k_max - global_k_min\n    global_e_range = global_e_max - global_e_min\n\n    fig, ax = plt.subplots(figsize=(10, 8))\n\n    def plot_curves(scan_index, n, vertical_offset, show_curves, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max, color_mode, show_max_marker, show_second_max_marker, division_point, curve_type):\n        plot_data = all_plots[scan_index - 1]\n        ax.clear()\n\n        # Get the full data\n        data_to_plot = plot_data['data_values'].copy()\n        k_parallel = plot_data['k_parallel'][0]\n        energy_values = plot_data['energy_values']\n\n        # Apply filters only to the selected range\n        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]\n        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]\n        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]\n        k_parallel = k_parallel[valid_k_indices]\n        energy_values = energy_values[valid_e_indices]\n\n        if use_canny:\n            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)\n            data_to_plot = edges.astype(float)\n\n        if enable_averaging:\n            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)\n            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)\n            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]\n            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]\n            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]\n            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')\n            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data\n\n        if curve_type == 'EDC':\n            indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)\n            x_values = energy_values\n            y_label = 'Intensity (arb. units)'\n            x_label = r'$E-E_f$ (eV)'\n            division_min, division_max = e_min, e_max\n        else:  # MDC\n            indices = np.linspace(0, len(valid_e_indices) - 1, n, dtype=int)\n            x_values = k_parallel\n            y_label = 'Intensity (arb. units)'\n            x_label = r'$k_\\parallel$ ($\\AA^{-1}$)'\n            division_min, division_max = k_min, k_max\n\n        # Ensure division_point is within the current bounds\n        division_point = max(division_min, min(division_max, division_point))\n\n        max_intensity = float('-inf')\n        min_intensity = float('inf')\n\n        for i, index in enumerate(indices):\n            if curve_type == 'EDC':\n                curve = data_to_plot[:, index]\n                actual_value = k_parallel[index]\n                label = fr'$k_\\parallel$ = {actual_value:.2f}'\n            else:  # MDC\n                curve = data_to_plot[index, :]\n                actual_value = energy_values[index]\n                label = fr'$E-E_f$ = {actual_value:.2f}'\n\n            if isinstance(curve, xr.DataArray):\n                curve = curve.values\n            if isinstance(x_values, xr.DataArray):\n                x_values = x_values.values\n\n            if not use_canny:\n                max_index = np.argmax(curve)\n                curve = curve / np.max(curve)\n\n            offset_curve = curve + i * vertical_offset\n            max_intensity = max(max_intensity, np.max(offset_curve))\n            min_intensity = min(min_intensity, np.min(offset_curve))\n\n            if show_curves:\n                if color_mode == 'Color':\n                    ax.plot(x_values, offset_curve, label=label)\n                else:  # Grayscale\n                    ax.plot(x_values, offset_curve, color='black')\n                \n                if show_max_marker and not use_canny:\n                    ax.plot(x_values[max_index], offset_curve[max_index], 'bo', markersize=10, fillstyle='none')\n                \n                if show_second_max_marker and not use_canny:\n                    left_max_index = np.argmax(curve[x_values <= division_point])\n                    right_max_index = len(x_values) - 1 - np.argmax(curve[x_values > division_point][::-1])\n                    ax.plot(x_values[left_max_index], offset_curve[left_max_index], 'ro', markersize=10, fillstyle='none')\n                    ax.plot(x_values[right_max_index], offset_curve[right_max_index], 'bo', markersize=10, fillstyle='none')\n\n            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)\n\n            if show_fit:\n                # Filter data for fitting\n                fit_indices = np.where((x_values >= fit_e_min) & (x_values <= fit_e_max))[0]\n                fit_x_values = x_values[fit_indices]\n                fit_curve = curve[fit_indices]\n\n                if fit_type == 'Maxima':\n                    peaks, _ = find_peaks(fit_curve)\n                    peak_heights = fit_curve[peaks]\n                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]\n                    largest_peaks = peaks[sorted_indices]\n\n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(fit_curve))\n\n                    for j, peak in enumerate(largest_peaks):\n                        gaussian = GaussianModel(prefix=f'g{j+1}_')\n                        model += gaussian\n                        params.update(gaussian.make_params())\n                        params[f'g{j+1}_center'].set(value=fit_x_values[peak], min=fit_x_values.min(), max=fit_x_values.max())\n                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)\n\n                elif fit_type == 'Minima':\n                    valleys, _ = find_peaks(-fit_curve)\n                    valley_depths = np.max(fit_curve) - fit_curve[valleys]\n                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]\n                    largest_valleys = valleys[sorted_indices]\n\n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(fit_curve))\n\n                    for j, valley in enumerate(largest_valleys):\n                        gaussian = GaussianModel(prefix=f'g{j+1}_')\n                        model += gaussian\n                        params.update(gaussian.make_params())\n                        params[f'g{j+1}_center'].set(value=fit_x_values[valley], min=fit_x_values.min(), max=fit_x_values.max())\n                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])\n                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)\n\n                result = model.fit(fit_curve, params, x=fit_x_values)\n                fit = result.best_fit\n\n                # Interpolate the fit back to the full x range\n                full_fit = np.interp(x_values, fit_x_values, fit)\n                offset_fit = full_fit + i * vertical_offset\n\n                # Extract sigma values and their uncertainties for label\n                sigmas = []\n                sigma_errors = []\n                for j in range(num_peaks):\n                    sigma = abs(result.params[f'g{j+1}_sigma'].value)\n                    sigma_error = result.params[f'g{j+1}_sigma'].stderr\n                    sigmas.append(sigma)\n                    sigma_errors.append(sigma_error)\n\n                sigma_label = ', '.join([fr'$\\sigma_{j+1}$={sigma:.3f} $\\pm$ {error:.3f}' for j, (sigma, error) in enumerate(zip(sigmas, sigma_errors))])\n\n                if color_mode == 'Color':\n                    ax.plot(x_values, offset_fit, '--', label=fr'Fit {curve_type}={actual_value:.2f}, {sigma_label}', color=f'C{i}')\n                else:  # Grayscale\n                    ax.plot(x_values, offset_fit, '--', color='black')\n\n                if fit_type == 'Maxima':\n                    fit_peaks, _ = find_peaks(full_fit)\n                    ax.plot(x_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)\n\n                    for j, peak in enumerate(fit_peaks):\n                        peak_x = x_values[peak]\n                        peak_intensity = offset_fit[peak]\n                        sigma = sigmas[j]\n                        sigma_error = sigma_errors[j]\n                        left_sigma_x = peak_x - sigma\n                        right_sigma_x = peak_x + sigma\n                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)\n                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)\n\n                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)\n                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)\n                        ax.annotate(fr'$-\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')\n                        ax.annotate(fr'$+\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')\n\n                elif fit_type == 'Minima':\n                    fit_valleys, _ = find_peaks(-full_fit)\n                    ax.plot(x_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)\n\n                    for j, valley in enumerate(fit_valleys):\n                        valley_x = x_values[valley]\n                        valley_intensity = offset_fit[valley]\n                        sigma = sigmas[j]\n                        sigma_error = sigma_errors[j]\n                        left_sigma_x = valley_x - sigma\n                        right_sigma_x = valley_x + sigma\n                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)\n                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)\n\n                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)\n                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)\n                        ax.annotate(fr'$-\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')\n                        ax.annotate(fr'$+\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')\n\n        # Add a vertical line at the division point\n        if show_second_max_marker:\n            ax.axvline(x=division_point, color='green', linestyle='--', alpha=0.5)\n\n        ax.set_xlabel(x_label, fontsize=12, fontweight='bold')\n        ax.set_ylabel(y_label, fontsize=12, fontweight='bold')\n        ax.set_title(f'{curve_type}s - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        \n        # Only show legend in Color mode\n        if color_mode == 'Color':\n            ax.legend()\n        \n        ax.tick_params(axis='both', which='major', labelsize=10)\n        ax.set_xlim(e_min if curve_type == 'EDC' else k_min, e_max if curve_type == 'EDC' else k_max)\n        y_range = max_intensity - min_intensity\n        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)\n\n        plt.tight_layout()\n        fig.canvas.draw_idle()\n\n        return max_intensity - min_intensity\n    def save_plot(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_curves': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'use_canny': interactive_plot.children[10].value,\n            'sigma': interactive_plot.children[11].value,\n            'low_threshold': interactive_plot.children[12].value,\n            'high_threshold': interactive_plot.children[13].value,\n            'enable_averaging': interactive_plot.children[14].value,\n            'averaging_kernel_size': interactive_plot.children[15].value,\n            'avg_e_min': interactive_plot.children[16].value,\n            'avg_e_max': interactive_plot.children[17].value,\n            'avg_k_min': interactive_plot.children[18].value,\n            'avg_k_max': interactive_plot.children[19].value,\n            'fit_type': interactive_plot.children[20].value,\n            'fit_e_min': interactive_plot.children[21].value,\n            'fit_e_max': interactive_plot.children[22].value,\n            'color_mode': interactive_plot.children[23].value,\n            'show_max_marker': interactive_plot.children[24].value,\n            'show_second_max_marker': interactive_plot.children[25].value,\n            'division_point': interactive_plot.children[26].value,\n            'curve_type': interactive_plot.children[27].value\n        }\n        plot_curves(**current_values)\n        filename = f\"{current_values['curve_type']}_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        print(f\"Plot saved as {filename}\")\n\n    def export_data(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_curves': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'use_canny': interactive_plot.children[10].value,\n            'sigma': interactive_plot.children[11].value,\n            'low_threshold': interactive_plot.children[12].value,\n            'high_threshold': interactive_plot.children[13].value,\n            'enable_averaging': interactive_plot.children[14].value,\n            'averaging_kernel_size': interactive_plot.children[15].value,\n            'avg_e_min': interactive_plot.children[16].value,\n            'avg_e_max': interactive_plot.children[17].value,\n            'avg_k_min': interactive_plot.children[18].value,\n            'avg_k_max': interactive_plot.children[19].value,\n            'fit_type': interactive_plot.children[20].value,\n            'fit_e_min': interactive_plot.children[21].value,\n            'fit_e_max': interactive_plot.children[22].value,\n            'color_mode': interactive_plot.children[23].value,\n            'show_max_marker': interactive_plot.children[24].value,\n            'show_second_max_marker': interactive_plot.children[25].value,\n            'division_point': interactive_plot.children[26].value,\n            'curve_type': interactive_plot.children[27].value\n        }\n        \n        plot_data = all_plots[current_values['scan_index'] - 1]\n        k_parallel = plot_data['k_parallel'][0]\n        energy_values = plot_data['energy_values']\n        data_values = plot_data['data_values']\n\n        # Convert xarray DataArrays to numpy arrays if necessary\n        if isinstance(k_parallel, xr.DataArray):\n            k_parallel = k_parallel.values\n        if isinstance(energy_values, xr.DataArray):\n            energy_values = energy_values.values\n        if isinstance(data_values, xr.DataArray):\n            data_values = data_values.values\n\n        # Apply filters only to the selected range\n        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]\n        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]\n        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]\n        k_parallel = k_parallel[valid_k_indices]\n        energy_values = energy_values[valid_e_indices]\n\n        if current_values['use_canny']:\n            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])\n            data_to_plot = edges.astype(float)\n\n        if current_values['enable_averaging']:\n            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)\n            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)\n            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')\n\n        if current_values['curve_type'] == 'EDC':\n            indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)\n            x_values = energy_values\n            y_label = 'Intensity (arb. units)'\n            x_label = 'Energy (eV)'\n        else:  # MDC\n            indices = np.linspace(0, len(valid_e_indices) - 1, current_values['n'], dtype=int)\n            x_values = k_parallel\n            y_label = 'Intensity (arb. units)'\n            x_label = rf'k_parallel ($\\AA^{-1}$)'\n\n        # Get the current axes\n        ax = plt.gca()\n\n        for i, index in enumerate(indices):\n            if current_values['curve_type'] == 'EDC':\n                curve = data_to_plot[:, index]\n                actual_value = k_parallel[index]\n            else:  # MDC\n                curve = data_to_plot[index, :]\n                actual_value = energy_values[index]\n\n            if isinstance(actual_value, np.ndarray):\n                actual_value = actual_value.item()\n\n            # Apply normalization if not using Canny filter\n            if not current_values['use_canny']:\n                curve = curve / np.max(curve)\n\n            # Export original/processed data\n            np.savetxt(f\"{current_values['curve_type']}_{actual_value:.2f}.dat\", np.column_stack((x_values, curve)), header=f\"{x_label}\\t{y_label}\")\n\n            if current_values['show_fit']:\n                # Find the fit line in the plot\n                fit_line = None\n                for line in ax.lines:\n                    if line.get_label().startswith(f\"Fit {current_values['curve_type']}={actual_value:.2f}\"):\n                        fit_line = line\n                        break\n\n                if fit_line is not None:\n                    # Get the x and y data of the fit line\n                    fit_x_data, fit_y_data = fit_line.get_data()\n                    # Remove the vertical offset\n                    fit_y_data -= i * current_values['vertical_offset']\n                    # Export the fit data\n                    np.savetxt(f\"gaussian_fit_{current_values['curve_type']}_{actual_value:.2f}.dat\", np.column_stack((fit_x_data, fit_y_data)), header=f\"{x_label}\\tFitted {y_label}\")\n                else:\n                    print(f\"No fit found for {current_values['curve_type']} = {actual_value:.2f}\")\n\n        print(\"Data export completed.\")\n\n    class DynamicFloatSlider(FloatSlider):\n        def __init__(self, *args, **kwargs):\n            super().__init__(*args, **kwargs)\n            self.observe(self._update_bounds, names=['value'])\n\n        def _update_bounds(self, change):\n            self.min = min(self.min, change['new'])\n            self.max = max(self.max, change['new'])\n\n    def update_division_point(*args):\n        curve_type = interactive_plot.children[-1].value\n        if curve_type == 'EDC':\n            e_min = interactive_plot.children[8].value\n            e_max = interactive_plot.children[9].value\n            interactive_plot.children[-2].min = e_min\n            interactive_plot.children[-2].max = e_max\n        else:  # MDC\n            k_min = interactive_plot.children[6].value\n            k_max = interactive_plot.children[7].value\n            interactive_plot.children[-2].min = k_min\n            interactive_plot.children[-2].max = k_max\n\n    interactive_plot = interactive(\n        plot_curves,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n        n=IntSlider(value=5, min=1, max=40, step=1, description='Number of Curves', continuous_update=True),\n        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),\n        show_curves=Checkbox(value=True, description='Show Curves'),\n        show_fit=Checkbox(value=False, description='Show Fits'),\n        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),\n        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),\n        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),\n        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),\n        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),\n        use_canny=Checkbox(value=False, description='Use Canny Filter'),\n        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),\n        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),\n        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),\n        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),\n        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),\n        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),\n        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),\n        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description=rf'Average $k_{min}$ ($\\AA^{-1}$)', continuous_update=True),\n        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description=rf'Average k_{m} ($\\AA^{-1}$)', continuous_update=True),\n        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),\n        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),\n        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True),\n        color_mode=ToggleButtons(options=['Color', 'Grayscale'], description='Color Mode'),\n        show_max_marker=Checkbox(value=False, description='Show Max Marker'),\n        show_second_max_marker=Checkbox(value=False, description='Show Dual Max Markers'),\n        division_point=DynamicFloatSlider(value=(global_e_min + global_e_max) / 2, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Division Point', continuous_update=True),\n        curve_type=ToggleButtons(options=['EDC', 'MDC'], description='Curve Type')\n    )\n\n    # Observe changes in curve_type, e_min, e_max, k_min, and k_max\n    interactive_plot.children[-1].observe(update_division_point, names='value')\n    interactive_plot.children[6].observe(update_division_point, names='value')\n    interactive_plot.children[7].observe(update_division_point, names='value')\n    interactive_plot.children[8].observe(update_division_point, names='value')\n    interactive_plot.children[9].observe(update_division_point, names='value')\n\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    export_button = Button(description=\"Export Data\")\n    export_button.on_click(export_data)\n\n    output = VBox([interactive_plot, HBox([save_button, export_button])])\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n    interactive_plot_with_save = edc_plot(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")", 26105)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.3.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.3.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.4": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons\nfrom matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm\nfrom IPython.display import display, clear_output\nimport tkinter as tk\nfrom tkinter import filedialog\nimport warnings\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nfrom matplotlib import MatplotlibDeprecationWarning\nfrom lmfit.models import GaussianModel, LinearModel\nfrom scipy.signal import find_peaks\nfrom skimage.feature import canny\nfrom scipy.ndimage import convolve\nfrom ipywidgets import ToggleButtons\n\n%matplotlib widget\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",\n    \"axes.labelcolor\": \"black\",\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()\n    return [os.path.join(folder_path, f) for f in data_files]\n\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef edc_plot(data_files, work_function):\n    # Constants\n    hbar = 1.054571817e-34  # J*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    global_k_min = float('inf')\n    global_k_max = float('-inf')\n    global_e_min = float('inf')\n    global_e_max = float('-inf')\n    global_intensity_max = float('-inf')\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']\n\n        # Calculate kinetic energy and momentum\n        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19\n        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10\n\n        # Get the energy values\n        energy_values = -data.eV.values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        # Update global ranges\n        global_k_min = min(global_k_min, np.min(k_parallel))\n        global_k_max = max(global_k_max, np.max(k_parallel))\n        global_e_min = min(global_e_min, np.min(energy_values))\n        global_e_max = max(global_e_max, np.max(energy_values))\n        global_intensity_max = max(global_intensity_max, np.max(data.values))\n\n    global_k_range = global_k_max - global_k_min\n    global_e_range = global_e_max - global_e_min\n\n    fig, ax = plt.subplots(figsize=(10, 8))\n\n    def plot_curves(scan_index, n, vertical_offset, show_curves, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max, color_mode, show_max_marker, show_second_max_marker, division_point, curve_type):\n        plot_data = all_plots[scan_index - 1]\n        ax.clear()\n\n        # Get the full data\n        data_to_plot = plot_data['data_values'].copy()\n        k_parallel = plot_data['k_parallel'][0]\n        energy_values = plot_data['energy_values']\n\n        # Apply filters only to the selected range\n        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]\n        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]\n        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]\n        k_parallel = k_parallel[valid_k_indices]\n        energy_values = energy_values[valid_e_indices]\n\n        if use_canny:\n            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)\n            data_to_plot = edges.astype(float)\n\n        if enable_averaging:\n            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)\n            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)\n            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]\n            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]\n            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]\n            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')\n            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data\n\n        if curve_type == 'EDC':\n            indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)\n            x_values = energy_values\n            y_label = 'Intensity (arb. units)'\n            x_label = r'$E-E_f$ (eV)'\n            division_min, division_max = e_min, e_max\n        else:  # MDC\n            indices = np.linspace(0, len(valid_e_indices) - 1, n, dtype=int)\n            x_values = k_parallel\n            y_label = 'Intensity (arb. units)'\n            x_label = r'$k_\\parallel$ ($\\AA^{-1}$)'\n            division_min, division_max = k_min, k_max\n\n        # Ensure division_point is within the current bounds\n        division_point = max(division_min, min(division_max, division_point))\n\n        max_intensity = float('-inf')\n        min_intensity = float('inf')\n\n        for i, index in enumerate(indices):\n            if curve_type == 'EDC':\n                curve = data_to_plot[:, index]\n                actual_value = k_parallel[index]\n                label = fr'$k_\\parallel$ = {actual_value:.2f}'\n            else:  # MDC\n                curve = data_to_plot[index, :]\n                actual_value = energy_values[index]\n                label = fr'$E-E_f$ = {actual_value:.2f}'\n\n            if isinstance(curve, xr.DataArray):\n                curve = curve.values\n            if isinstance(x_values, xr.DataArray):\n                x_values = x_values.values\n\n            if not use_canny:\n                max_index = np.argmax(curve)\n                curve = curve / np.max(curve)\n\n            offset_curve = curve + i * vertical_offset\n            max_intensity = max(max_intensity, np.max(offset_curve))\n            min_intensity = min(min_intensity, np.min(offset_curve))\n\n            if show_curves:\n                if color_mode == 'Color':\n                    ax.plot(x_values, offset_curve, label=label)\n                else:  # Grayscale\n                    ax.plot(x_values, offset_curve, color='black')\n                \n                if show_max_marker and not use_canny:\n                    ax.plot(x_values[max_index], offset_curve[max_index], 'bo', markersize=10, fillstyle='none')\n                \n                if show_second_max_marker and not use_canny:\n                    left_max_index = np.argmax(curve[x_values <= division_point])\n                    right_max_index = len(x_values) - 1 - np.argmax(curve[x_values > division_point][::-1])\n                    ax.plot(x_values[left_max_index], offset_curve[left_max_index], 'ro', markersize=10, fillstyle='none')\n                    ax.plot(x_values[right_max_index], offset_curve[right_max_index], 'bo', markersize=10, fillstyle='none')\n\n            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)\n\n            if show_fit:\n                # Filter data for fitting\n                fit_indices = np.where((x_values >= fit_e_min) & (x_values <= fit_e_max))[0]\n                fit_x_values = x_values[fit_indices]\n                fit_curve = curve[fit_indices]\n\n                if fit_type == 'Maxima':\n                    peaks, _ = find_peaks(fit_curve)\n                    peak_heights = fit_curve[peaks]\n                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]\n                    largest_peaks = peaks[sorted_indices]\n\n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(fit_curve))\n\n                    for j, peak in enumerate(largest_peaks):\n                        gaussian = GaussianModel(prefix=f'g{j+1}_')\n                        model += gaussian\n                        params.update(gaussian.make_params())\n                        params[f'g{j+1}_center'].set(value=fit_x_values[peak], min=fit_x_values.min(), max=fit_x_values.max())\n                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)\n\n                elif fit_type == 'Minima':\n                    valleys, _ = find_peaks(-fit_curve)\n                    valley_depths = np.max(fit_curve) - fit_curve[valleys]\n                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]\n                    largest_valleys = valleys[sorted_indices]\n\n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(fit_curve))\n\n                    for j, valley in enumerate(largest_valleys):\n                        gaussian = GaussianModel(prefix=f'g{j+1}_')\n                        model += gaussian\n                        params.update(gaussian.make_params())\n                        params[f'g{j+1}_center'].set(value=fit_x_values[valley], min=fit_x_values.min(), max=fit_x_values.max())\n                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])\n                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)\n\n                result = model.fit(fit_curve, params, x=fit_x_values)\n                fit = result.best_fit\n\n                # Interpolate the fit back to the full x range\n                full_fit = np.interp(x_values, fit_x_values, fit)\n                offset_fit = full_fit + i * vertical_offset\n\n                # Extract sigma values and their uncertainties for label\n                sigmas = []\n                sigma_errors = []\n                for j in range(num_peaks):\n                    sigma = abs(result.params[f'g{j+1}_sigma'].value)\n                    sigma_error = result.params[f'g{j+1}_sigma'].stderr\n                    sigmas.append(sigma)\n                    sigma_errors.append(sigma_error)\n\n                sigma_label = ', '.join([fr'$\\sigma_{j+1}$={sigma:.3f} $\\pm$ {error:.3f}' for j, (sigma, error) in enumerate(zip(sigmas, sigma_errors))])\n\n                if color_mode == 'Color':\n                    ax.plot(x_values, offset_fit, '--', label=fr'Fit {curve_type}={actual_value:.2f}, {sigma_label}', color=f'C{i}')\n                else:  # Grayscale\n                    ax.plot(x_values, offset_fit, '--', color='black')\n\n                if fit_type == 'Maxima':\n                    fit_peaks, _ = find_peaks(full_fit)\n                    ax.plot(x_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)\n\n                    for j, peak in enumerate(fit_peaks):\n                        peak_x = x_values[peak]\n                        peak_intensity = offset_fit[peak]\n                        sigma = sigmas[j]\n                        sigma_error = sigma_errors[j]\n                        left_sigma_x = peak_x - sigma\n                        right_sigma_x = peak_x + sigma\n                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)\n                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)\n\n                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)\n                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)\n                        ax.annotate(fr'$-\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')\n                        ax.annotate(fr'$+\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')\n\n                elif fit_type == 'Minima':\n                    fit_valleys, _ = find_peaks(-full_fit)\n                    ax.plot(x_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)\n\n                    for j, valley in enumerate(fit_valleys):\n                        valley_x = x_values[valley]\n                        valley_intensity = offset_fit[valley]\n                        sigma = sigmas[j]\n                        sigma_error = sigma_errors[j]\n                        left_sigma_x = valley_x - sigma\n                        right_sigma_x = valley_x + sigma\n                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)\n                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)\n\n                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)\n                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)\n                        ax.annotate(fr'$-\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')\n                        ax.annotate(fr'$+\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')\n\n        # Add a vertical line at the division point\n        if show_second_max_marker:\n            ax.axvline(x=division_point, color='green', linestyle='--', alpha=0.5)\n\n        ax.set_xlabel(x_label, fontsize=12, fontweight='bold')\n        ax.set_ylabel(y_label, fontsize=12, fontweight='bold')\n        ax.set_title(f'{curve_type}s - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        \n        # Only show legend in Color mode\n        if color_mode == 'Color':\n            ax.legend()\n        \n        ax.tick_params(axis='both', which='major', labelsize=10)\n        ax.set_xlim(e_min if curve_type == 'EDC' else k_min, e_max if curve_type == 'EDC' else k_max)\n        y_range = max_intensity - min_intensity\n        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)\n\n        plt.tight_layout()\n        fig.canvas.draw_idle()\n\n        return max_intensity - min_intensity\n    def save_plot(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_curves': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'use_canny': interactive_plot.children[10].value,\n            'sigma': interactive_plot.children[11].value,\n            'low_threshold': interactive_plot.children[12].value,\n            'high_threshold': interactive_plot.children[13].value,\n            'enable_averaging': interactive_plot.children[14].value,\n            'averaging_kernel_size': interactive_plot.children[15].value,\n            'avg_e_min': interactive_plot.children[16].value,\n            'avg_e_max': interactive_plot.children[17].value,\n            'avg_k_min': interactive_plot.children[18].value,\n            'avg_k_max': interactive_plot.children[19].value,\n            'fit_type': interactive_plot.children[20].value,\n            'fit_e_min': interactive_plot.children[21].value,\n            'fit_e_max': interactive_plot.children[22].value,\n            'color_mode': interactive_plot.children[23].value,\n            'show_max_marker': interactive_plot.children[24].value,\n            'show_second_max_marker': interactive_plot.children[25].value,\n            'division_point': interactive_plot.children[26].value,\n            'curve_type': interactive_plot.children[27].value\n        }\n        plot_curves(**current_values)\n        filename = f\"{current_values['curve_type']}_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        print(f\"Plot saved as {filename}\")\n\n    def export_data(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_curves': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'use_canny': interactive_plot.children[10].value,\n            'sigma': interactive_plot.children[11].value,\n            'low_threshold': interactive_plot.children[12].value,\n            'high_threshold': interactive_plot.children[13].value,\n            'enable_averaging': interactive_plot.children[14].value,\n            'averaging_kernel_size': interactive_plot.children[15].value,\n            'avg_e_min': interactive_plot.children[16].value,\n            'avg_e_max': interactive_plot.children[17].value,\n            'avg_k_min': interactive_plot.children[18].value,\n            'avg_k_max': interactive_plot.children[19].value,\n            'fit_type': interactive_plot.children[20].value,\n            'fit_e_min': interactive_plot.children[21].value,\n            'fit_e_max': interactive_plot.children[22].value,\n            'color_mode': interactive_plot.children[23].value,\n            'show_max_marker': interactive_plot.children[24].value,\n            'show_second_max_marker': interactive_plot.children[25].value,\n            'division_point': interactive_plot.children[26].value,\n            'curve_type': interactive_plot.children[27].value\n        }\n        \n        plot_data = all_plots[current_values['scan_index'] - 1]\n        k_parallel = plot_data['k_parallel'][0]\n        energy_values = plot_data['energy_values']\n        data_values = plot_data['data_values']\n\n        # Convert xarray DataArrays to numpy arrays if necessary\n        if isinstance(k_parallel, xr.DataArray):\n            k_parallel = k_parallel.values\n        if isinstance(energy_values, xr.DataArray):\n            energy_values = energy_values.values\n        if isinstance(data_values, xr.DataArray):\n            data_values = data_values.values\n\n        # Apply filters only to the selected range\n        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]\n        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]\n        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]\n        k_parallel = k_parallel[valid_k_indices]\n        energy_values = energy_values[valid_e_indices]\n\n        if current_values['use_canny']:\n            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])\n            data_to_plot = edges.astype(float)\n\n        if current_values['enable_averaging']:\n            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)\n            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)\n            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')\n\n        if current_values['curve_type'] == 'EDC':\n            indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)\n            x_values = energy_values\n            y_label = 'Intensity (arb. units)'\n            x_label = 'Energy (eV)'\n        else:  # MDC\n            indices = np.linspace(0, len(valid_e_indices) - 1, current_values['n'], dtype=int)\n            x_values = k_parallel\n            y_label = 'Intensity (arb. units)'\n            x_label = rf'k_parallel ($\\AA^{-1}$)'\n\n        # Get the current axes\n        ax = plt.gca()\n\n        for i, index in enumerate(indices):\n            if current_values['curve_type'] == 'EDC':\n                curve = data_to_plot[:, index]\n                actual_value = k_parallel[index]\n            else:  # MDC\n                curve = data_to_plot[index, :]\n                actual_value = energy_values[index]\n\n            if isinstance(actual_value, np.ndarray):\n                actual_value = actual_value.item()\n\n            # Apply normalization if not using Canny filter\n            if not current_values['use_canny']:\n                curve = curve / np.max(curve)\n\n            # Export original/processed data\n            np.savetxt(f\"{current_values['curve_type']}_{actual_value:.2f}.dat\", np.column_stack((x_values, curve)), header=f\"{x_label}\\t{y_label}\")\n\n            if current_values['show_fit']:\n                # Find the fit line in the plot\n                fit_line = None\n                for line in ax.lines:\n                    if line.get_label().startswith(f\"Fit {current_values['curve_type']}={actual_value:.2f}\"):\n                        fit_line = line\n                        break\n\n                if fit_line is not None:\n                    # Get the x and y data of the fit line\n                    fit_x_data, fit_y_data = fit_line.get_data()\n                    # Remove the vertical offset\n                    fit_y_data -= i * current_values['vertical_offset']\n                    # Export the fit data\n                    np.savetxt(f\"gaussian_fit_{current_values['curve_type']}_{actual_value:.2f}.dat\", np.column_stack((fit_x_data, fit_y_data)), header=f\"{x_label}\\tFitted {y_label}\")\n                else:\n                    print(f\"No fit found for {current_values['curve_type']} = {actual_value:.2f}\")\n\n        print(\"Data export completed.\")\n\n    class DynamicFloatSlider(FloatSlider):\n        def __init__(self, *args, **kwargs):\n            super().__init__(*args, **kwargs)\n            self.observe(self._update_bounds, names=['value'])\n\n        def _update_bounds(self, change):\n            self.min = min(self.min, change['new'])\n            self.max = max(self.max, change['new'])\n\n    def update_division_point(*args):\n        curve_type = interactive_plot.children[-1].value\n        if curve_type == 'EDC':\n            e_min = interactive_plot.children[8].value\n            e_max = interactive_plot.children[9].value\n            interactive_plot.children[-2].min = e_min\n            interactive_plot.children[-2].max = e_max\n        else:  # MDC\n            k_min = interactive_plot.children[6].value\n            k_max = interactive_plot.children[7].value\n            interactive_plot.children[-2].min = k_min\n            interactive_plot.children[-2].max = k_max\n\n    interactive_plot = interactive(\n        plot_curves,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n        n=IntSlider(value=5, min=1, max=40, step=1, description='Number of Curves', continuous_update=True),\n        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),\n        show_curves=Checkbox(value=True, description='Show Curves'),\n        show_fit=Checkbox(value=False, description='Show Fits'),\n        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),\n        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),\n        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),\n        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),\n        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),\n        use_canny=Checkbox(value=False, description='Use Canny Filter'),\n        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),\n        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),\n        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),\n        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),\n        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),\n        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),\n        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),\n        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description=tf'Average $k_{min}$ ($\\AA^{-1}$)', continuous_update=True),\n        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description=rf'Average $k_{max}$ ($\\AA^{-1}$)', continuous_update=True),\n        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),\n        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),\n        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True),\n        color_mode=ToggleButtons(options=['Color', 'Grayscale'], description='Color Mode'),\n        show_max_marker=Checkbox(value=False, description='Show Max Marker'),\n        show_second_max_marker=Checkbox(value=False, description='Show Dual Max Markers'),\n        division_point=DynamicFloatSlider(value=(global_e_min + global_e_max) / 2, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Division Point', continuous_update=True),\n        curve_type=ToggleButtons(options=['EDC', 'MDC'], description='Curve Type')\n    )\n\n    # Observe changes in curve_type, e_min, e_max, k_min, and k_max\n    interactive_plot.children[-1].observe(update_division_point, names='value')\n    interactive_plot.children[6].observe(update_division_point, names='value')\n    interactive_plot.children[7].observe(update_division_point, names='value')\n    interactive_plot.children[8].observe(update_division_point, names='value')\n    interactive_plot.children[9].observe(update_division_point, names='value')\n\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    export_button = Button(description=\"Export Data\")\n    export_button.on_click(export_data)\n\n    output = VBox([interactive_plot, HBox([save_button, export_button])])\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n    interactive_plot_with_save = edc_plot(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")", 25906)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.4.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.4.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import GaussianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
from ipywidgets import ToggleButtons

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_curves(scan_index, n, vertical_offset, show_curves, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max, color_mode, show_max_marker, show_second_max_marker, division_point, curve_type):
        plot_data = all_plots[scan_index - 1]
        ax.clear()

        # Get the full data
        data_to_plot = plot_data['data_values'].copy()
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]
            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]
            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]
            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')
            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data

        if curve_type == 'EDC':
            indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)
            x_values = energy_values
            y_label = 'Intensity (arb. units)'
            x_label = r'$E-E_f$ (eV)'
            division_min, division_max = e_min, e_max
        else:  # MDC
            indices = np.linspace(0, len(valid_e_indices) - 1, n, dtype=int)
            x_values = k_parallel
            y_label = 'Intensity (arb. units)'
            x_label = r'$k_\parallel$ ($\AA^{-1}$)'
            division_min, division_max = k_min, k_max

        # Ensure division_point is within the current bounds
        division_point = max(division_min, min(division_max, division_point))

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, index in enumerate(indices):
            if curve_type == 'EDC':
                curve = data_to_plot[:, index]
                actual_value = k_parallel[index]
                label = fr'$k_\parallel$ = {actual_value:.2f}'
            else:  # MDC
                curve = data_to_plot[index, :]
                actual_value = energy_values[index]
                label = fr'$E-E_f$ = {actual_value:.2f}'

            if isinstance(curve, xr.DataArray):
                curve = curve.values
            if isinstance(x_values, xr.DataArray):
                x_values = x_values.values

            if not use_canny:
                max_index = np.argmax(curve)
                curve = curve / np.max(curve)

            offset_curve = curve + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_curve))
            min_intensity = min(min_intensity, np.min(offset_curve))

            if show_curves:
                if color_mode == 'Color':
                    ax.plot(x_values, offset_curve, label=label)
                else:  # Grayscale
                    ax.plot(x_values, offset_curve, color='black')
                
                if show_max_marker and not use_canny:
                    ax.plot(x_values[max_index], offset_curve[max_index], 'bo', markersize=10, fillstyle='none')
                
                if show_second_max_marker and not use_canny:
                    left_max_index = np.argmax(curve[x_values <= division_point])
                    right_max_index = len(x_values) - 1 - np.argmax(curve[x_values > division_point][::-1])
                    ax.plot(x_values[left_max_index], offset_curve[left_max_index], 'ro', markersize=10, fillstyle='none')
                    ax.plot(x_values[right_max_index], offset_curve[right_max_index], 'bo', markersize=10, fillstyle='none')

            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Filter data for fitting
                fit_indices = np.where((x_values >= fit_e_min) & (x_values <= fit_e_max))[0]
                fit_x_values = x_values[fit_indices]
                fit_curve = curve[fit_indices]

                if fit_type == 'Maxima':
                    peaks, _ = find_peaks(fit_curve)
                    peak_heights = fit_curve[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(fit_curve))

                    for j, peak in enumerate(largest_peaks):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_x_values[peak], min=fit_x_values.min(), max=fit_x_values.max())
                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                elif fit_type == 'Minima':
                    valleys, _ = find_peaks(-fit_curve)
                    valley_depths = np.max(fit_curve) - fit_curve[valleys]
                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]
                    largest_valleys = valleys[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(fit_curve))

                    for j, valley in enumerate(largest_valleys):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_x_values[valley], min=fit_x_values.min(), max=fit_x_values.max())
                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(fit_curve, params, x=fit_x_values)
                fit = result.best_fit

                # Interpolate the fit back to the full x range
                full_fit = np.interp(x_values, fit_x_values, fit)
                offset_fit = full_fit + i * vertical_offset

                # Extract sigma values and their uncertainties for label
                sigmas = []
                sigma_errors = []
                for j in range(num_peaks):
                    sigma = abs(result.params[f'g{j+1}_sigma'].value)
                    sigma_error = result.params[f'g{j+1}_sigma'].stderr
                    sigmas.append(sigma)
                    sigma_errors.append(sigma_error)

                sigma_label = ', '.join([fr'$\sigma_{j+1}$={sigma:.3f} $\pm$ {error:.3f}' for j, (sigma, error) in enumerate(zip(sigmas, sigma_errors))])

                if color_mode == 'Color':
                    ax.plot(x_values, offset_fit, '--', label=fr'Fit {curve_type}={actual_value:.2f}, {sigma_label}', color=f'C{i}')
                else:  # Grayscale
                    ax.plot(x_values, offset_fit, '--', color='black')

                if fit_type == 'Maxima':
                    fit_peaks, _ = find_peaks(full_fit)
                    ax.plot(x_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)

                    for j, peak in enumerate(fit_peaks):
                        peak_x = x_values[peak]
                        peak_intensity = offset_fit[peak]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_x = peak_x - sigma
                        right_sigma_x = peak_x + sigma
                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)

                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')

                elif fit_type == 'Minima':
                    fit_valleys, _ = find_peaks(-full_fit)
                    ax.plot(x_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

                    for j, valley in enumerate(fit_valleys):
                        valley_x = x_values[valley]
                        valley_intensity = offset_fit[valley]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_x = valley_x - sigma
                        right_sigma_x = valley_x + sigma
                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)

                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')

        # Add a vertical line at the division point
        if show_second_max_marker:
            ax.axvline(x=division_point, color='green', linestyle='--', alpha=0.5)

        ax.set_xlabel(x_label, fontsize=12, fontweight='bold')
        ax.set_ylabel(y_label, fontsize=12, fontweight='bold')
        ax.set_title(f'{curve_type}s - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        
        # Only show legend in Color mode
        if color_mode == 'Color':
            ax.legend()
        
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_xlim(e_min if curve_type == 'EDC' else k_min, e_max if curve_type == 'EDC' else k_max)
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity
    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_curves': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'avg_e_min': interactive_plot.children[16].value,
            'avg_e_max': interactive_plot.children[17].value,
            'avg_k_min': interactive_plot.children[18].value,
            'avg_k_max': interactive_plot.children[19].value,
            'fit_type': interactive_plot.children[20].value,
            'fit_e_min': interactive_plot.children[21].value,
            'fit_e_max': interactive_plot.children[22].value,
            'color_mode': interactive_plot.children[23].value,
            'show_max_marker': interactive_plot.children[24].value,
            'show_second_max_marker': interactive_plot.children[25].value,
            'division_point': interactive_plot.children[26].value,
            'curve_type': interactive_plot.children[27].value
        }
        plot_curves(**current_values)
        filename = f"{current_values['curve_type']}_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def export_data(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_curves': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'avg_e_min': interactive_plot.children[16].value,
            'avg_e_max': interactive_plot.children[17].value,
            'avg_k_min': interactive_plot.children[18].value,
            'avg_k_max': interactive_plot.children[19].value,
            'fit_type': interactive_plot.children[20].value,
            'fit_e_min': interactive_plot.children[21].value,
            'fit_e_max': interactive_plot.children[22].value,
            'color_mode': interactive_plot.children[23].value,
            'show_max_marker': interactive_plot.children[24].value,
            'show_second_max_marker': interactive_plot.children[25].value,
            'division_point': interactive_plot.children[26].value,
            'curve_type': interactive_plot.children[27].value
        }
        
        plot_data = all_plots[current_values['scan_index'] - 1]
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']
        data_values = plot_data['data_values']

        # Convert xarray DataArrays to numpy arrays if necessary
        if isinstance(k_parallel, xr.DataArray):
            k_parallel = k_parallel.values
        if isinstance(energy_values, xr.DataArray):
            energy_values = energy_values.values
        if isinstance(data_values, xr.DataArray):
            data_values = data_values.values

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]
        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]
        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if current_values['use_canny']:
            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])
            data_to_plot = edges.astype(float)

        if current_values['enable_averaging']:
            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')

        if current_values['curve_type'] == 'EDC':
            indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)
            x_values = energy_values
            y_label = 'Intensity (arb. units)'
            x_label = 'Energy (eV)'
        else:  # MDC
            indices = np.linspace(0, len(valid_e_indices) - 1, current_values['n'], dtype=int)
            x_values = k_parallel
            y_label = 'Intensity (arb. units)'
            x_label = rf'k_parallel ($\AA^{-1}$)'

        # Get the current axes
        ax = plt.gca()

        for i, index in enumerate(indices):
            if current_values['curve_type'] == 'EDC':
                curve = data_to_plot[:, index]
                actual_value = k_parallel[index]
            else:  # MDC
                curve = data_to_plot[index, :]
                actual_value = energy_values[index]

            if isinstance(actual_value, np.ndarray):
                actual_value = actual_value.item()

            # Apply normalization if not using Canny filter
            if not current_values['use_canny']:
                curve = curve / np.max(curve)

            # Export original/processed data
            np.savetxt(f"{current_values['curve_type']}_{actual_value:.2f}.dat", np.column_stack((x_values, curve)), header=f"{x_label}\t{y_label}")

            if current_values['show_fit']:
                # Find the fit line in the plot
                fit_line = None
                for line in ax.lines:
                    if line.get_label().startswith(f"Fit {current_values['curve_type']}={actual_value:.2f}"):
                        fit_line = line
                        break

                if fit_line is not None:
                    # Get the x and y data of the fit line
                    fit_x_data, fit_y_data = fit_line.get_data()
                    # Remove the vertical offset
                    fit_y_data -= i * current_values['vertical_offset']
                    # Export the fit data
                    np.savetxt(f"gaussian_fit_{current_values['curve_type']}_{actual_value:.2f}.dat", np.column_stack((fit_x_data, fit_y_data)), header=f"{x_label}\tFitted {y_label}")
                else:
                    print(f"No fit found for {current_values['curve_type']} = {actual_value:.2f}")

        print("Data export completed.")

    class DynamicFloatSlider(FloatSlider):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.observe(self._update_bounds, names=['value'])

        def _update_bounds(self, change):
            self.min = min(self.min, change['new'])
            self.max = max(self.max, change['new'])

    def update_division_point(*args):
        curve_type = interactive_plot.children[-1].value
        if curve_type == 'EDC':
            e_min = interactive_plot.children[8].value
            e_max = interactive_plot.children[9].value
            interactive_plot.children[-2].min = e_min
            interactive_plot.children[-2].max = e_max
        else:  # MDC
            k_min = interactive_plot.children[6].value
            k_max = interactive_plot.children[7].value
            interactive_plot.children[-2].min = k_min
            interactive_plot.children[-2].max = k_max

    interactive_plot = interactive(
        plot_curves,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=40, step=1, description='Number of Curves', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
        show_curves=Checkbox(value=True, description='Show Curves'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),
        use_canny=Checkbox(value=False, description='Use Canny Filter'),
        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),
        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),
        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),
        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description=rf'Average $k_{min}$ ($\AA^{-1}$)', continuous_update=True),
        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description=rf'Average $k_{max}$ ($\AA^{-1}$)', continuous_update=True),
        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),
        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),
        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True),
        color_mode=ToggleButtons(options=['Color', 'Grayscale'], description='Color Mode'),
        show_max_marker=Checkbox(value=False, description='Show Max Marker'),
        show_second_max_marker=Checkbox(value=False, description='Show Dual Max Markers'),
        division_point=DynamicFloatSlider(value=(global_e_min + global_e_max) / 2, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Division Point', continuous_update=True),
        curve_type=ToggleButtons(options=['EDC', 'MDC'], description='Curve Type')
    )

    # Observe changes in curve_type, e_min, e_max, k_min, and k_max
    interactive_plot.children[-1].observe(update_division_point, names='value')
    interactive_plot.children[6].observe(update_division_point, names='value')
    interactive_plot.children[7].observe(update_division_point, names='value')
    interactive_plot.children[8].observe(update_division_point, names='value')
    interactive_plot.children[9].observe(update_division_point, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    export_button = Button(description="Export Data")
    export_button.on_click(export_data)

    output = VBox([interactive_plot, HBox([save_button, export_button])])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.5": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons\nfrom matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm\nfrom IPython.display import display, clear_output\nimport tkinter as tk\nfrom tkinter import filedialog\nimport warnings\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nfrom matplotlib import MatplotlibDeprecationWarning\nfrom lmfit.models import GaussianModel, LinearModel\nfrom scipy.signal import find_peaks\nfrom skimage.feature import canny\nfrom scipy.ndimage import convolve\nfrom ipywidgets import ToggleButtons\n\n%matplotlib widget\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",\n    \"axes.labelcolor\": \"black\",\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()\n    return [os.path.join(folder_path, f) for f in data_files]\n\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef edc_plot(data_files, work_function):\n    # Constants\n    hbar = 1.054571817e-34  # J*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    global_k_min = float('inf')\n    global_k_max = float('-inf')\n    global_e_min = float('inf')\n    global_e_max = float('-inf')\n    global_intensity_max = float('-inf')\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']\n\n        # Calculate kinetic energy and momentum\n        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19\n        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10\n\n        # Get the energy values\n        energy_values = -data.eV.values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        # Update global ranges\n        global_k_min = min(global_k_min, np.min(k_parallel))\n        global_k_max = max(global_k_max, np.max(k_parallel))\n        global_e_min = min(global_e_min, np.min(energy_values))\n        global_e_max = max(global_e_max, np.max(energy_values))\n        global_intensity_max = max(global_intensity_max, np.max(data.values))\n\n    global_k_range = global_k_max - global_k_min\n    global_e_range = global_e_max - global_e_min\n\n    fig, ax = plt.subplots(figsize=(10, 8))\n\n    def plot_curves(scan_index, n, vertical_offset, show_curves, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max, color_mode, marker_type, division_point, curve_type):\n        plot_data = all_plots[scan_index - 1]\n        ax.clear()\n\n        # Get the full data\n        data_to_plot = plot_data['data_values'].copy()\n        k_parallel = plot_data['k_parallel'][0]\n        energy_values = plot_data['energy_values']\n\n        # Apply filters only to the selected range\n        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]\n        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]\n        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]\n        k_parallel = k_parallel[valid_k_indices]\n        energy_values = energy_values[valid_e_indices]\n\n        if use_canny:\n            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)\n            data_to_plot = edges.astype(float)\n\n        if enable_averaging:\n            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)\n            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)\n            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]\n            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]\n            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]\n            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')\n            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data\n\n        if curve_type == 'EDC':\n            indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)\n            x_values = energy_values\n            y_label = 'Intensity (arb. units)'\n            x_label = r'$E-E_f$ (eV)'\n            division_min, division_max = e_min, e_max\n        else:  # MDC\n            indices = np.linspace(0, len(valid_e_indices) - 1, n, dtype=int)\n            x_values = k_parallel\n            y_label = 'Intensity (arb. units)'\n            x_label = r'$k_\\parallel$ (Å⁻¹)'\n            division_min, division_max = k_min, k_max\n\n        # Ensure division_point is within the current bounds\n        division_point = max(division_min, min(division_max, division_point))\n\n        max_intensity = float('-inf')\n        min_intensity = float('inf')\n\n        for i, index in enumerate(indices):\n            if curve_type == 'EDC':\n                curve = data_to_plot[:, index]\n                actual_value = k_parallel[index]\n                label = fr'$k_\\parallel$ = {actual_value:.2f}'\n            else:  # MDC\n                curve = data_to_plot[index, :]\n                actual_value = energy_values[index]\n                label = fr'$E-E_f$ = {actual_value:.2f}'\n\n            if isinstance(curve, xr.DataArray):\n                curve = curve.values\n            if isinstance(x_values, xr.DataArray):\n                x_values = x_values.values\n\n            if not use_canny:\n                max_index = np.argmax(curve)\n                min_index = np.argmin(curve)\n                curve = curve / np.max(curve)\n\n            offset_curve = curve + i * vertical_offset\n            max_intensity = max(max_intensity, np.max(offset_curve))\n            min_intensity = min(min_intensity, np.min(offset_curve))\n\n            if show_curves:\n                if color_mode == 'Color':\n                    ax.plot(x_values, offset_curve, label=label)\n                else:  # Grayscale\n                    ax.plot(x_values, offset_curve, color='black')\n                \n                if marker_type != 'None' and not use_canny:\n                    if marker_type in ['Max', 'Dual Max']:\n                        ax.plot(x_values[max_index], offset_curve[max_index], 'bo', markersize=10, fillstyle='none')\n                    \n                    if marker_type in ['Min', 'Dual Min']:\n                        ax.plot(x_values[min_index], offset_curve[min_index], 'ro', markersize=10, fillstyle='none')\n                    \n                    if marker_type in ['Dual Max', 'Dual Min']:\n                        left_index = np.argmax(curve[x_values <= division_point]) if 'Max' in marker_type else np.argmin(curve[x_values <= division_point])\n                        right_index = len(x_values) - 1 - np.argmax(curve[x_values > division_point][::-1]) if 'Max' in marker_type else len(x_values) - 1 - np.argmin(curve[x_values > division_point][::-1])\n                        \n                        ax.plot(x_values[left_index], offset_curve[left_index], 'ro', markersize=10, fillstyle='none')\n                        ax.plot(x_values[right_index], offset_curve[right_index], 'bo', markersize=10, fillstyle='none')\n\n            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)\n\n            if show_fit:\n                # Filter data for fitting\n                fit_indices = np.where((x_values >= fit_e_min) & (x_values <= fit_e_max))[0]\n                fit_x_values = x_values[fit_indices]\n                fit_curve = curve[fit_indices]\n\n                if fit_type == 'Maxima':\n                    peaks, _ = find_peaks(fit_curve)\n                    peak_heights = fit_curve[peaks]\n                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]\n                    largest_peaks = peaks[sorted_indices]\n\n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(fit_curve))\n\n                    for j, peak in enumerate(largest_peaks):\n                        gaussian = GaussianModel(prefix=f'g{j+1}_')\n                        model += gaussian\n                        params.update(gaussian.make_params())\n                        params[f'g{j+1}_center'].set(value=fit_x_values[peak], min=fit_x_values.min(), max=fit_x_values.max())\n                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)\n\n                elif fit_type == 'Minima':\n                    valleys, _ = find_peaks(-fit_curve)\n                    valley_depths = np.max(fit_curve) - fit_curve[valleys]\n                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]\n                    largest_valleys = valleys[sorted_indices]\n\n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(fit_curve))\n\n                    for j, valley in enumerate(largest_valleys):\n                        gaussian = GaussianModel(prefix=f'g{j+1}_')\n                        model += gaussian\n                        params.update(gaussian.make_params())\n                        params[f'g{j+1}_center'].set(value=fit_x_values[valley], min=fit_x_values.min(), max=fit_x_values.max())\n                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])\n                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)\n\n                result = model.fit(fit_curve, params, x=fit_x_values)\n                fit = result.best_fit\n\n                # Interpolate the fit back to the full x range\n                full_fit = np.interp(x_values, fit_x_values, fit)\n                offset_fit = full_fit + i * vertical_offset\n\n                # Extract sigma values and their uncertainties for label\n                sigmas = []\n                sigma_errors = []\n                for j in range(num_peaks):\n                    sigma = abs(result.params[f'g{j+1}_sigma'].value)\n                    sigma_error = result.params[f'g{j+1}_sigma'].stderr\n                    sigmas.append(sigma)\n                    sigma_errors.append(sigma_error)\n\n                sigma_label = ', '.join([fr'$\\sigma_{j+1}$={sigma:.3f} $\\pm$ {error:.3f}' for j, (sigma, error) in enumerate(zip(sigmas, sigma_errors))])\n\n                if color_mode == 'Color':\n                    ax.plot(x_values, offset_fit, '--', label=fr'Fit {curve_type}={actual_value:.2f}, {sigma_label}', color=f'C{i}')\n                else:  # Grayscale\n                    ax.plot(x_values, offset_fit, '--', color='black')\n\n                if fit_type == 'Maxima':\n                    fit_peaks, _ = find_peaks(full_fit)\n                    ax.plot(x_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)\n\n                    for j, peak in enumerate(fit_peaks):\n                        peak_x = x_values[peak]\n                        peak_intensity = offset_fit[peak]\n                        sigma = sigmas[j]\n                        sigma_error = sigma_errors[j]\n                        left_sigma_x = peak_x - sigma\n                        right_sigma_x = peak_x + sigma\n                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)\n                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)\n\n                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)\n                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)\n                        ax.annotate(fr'$-\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')\n                        ax.annotate(fr'$+\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')\n\n                elif fit_type == 'Minima':\n                    fit_valleys, _ = find_peaks(-full_fit)\n                    ax.plot(x_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)\n\n                    for j, valley in enumerate(fit_valleys):\n                        valley_x = x_values[valley]\n                        valley_intensity = offset_fit[valley]\n                        sigma = sigmas[j]\n                        sigma_error = sigma_errors[j]\n                        left_sigma_x = valley_x - sigma\n                        right_sigma_x = valley_x + sigma\n                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)\n                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)\n\n                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)\n                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)\n                        ax.annotate(fr'$-\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')\n                        ax.annotate(fr'$+\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')\n\n        # Add a vertical line at the division point\n        if marker_type in ['Dual Max', 'Dual Min']:\n            ax.axvline(x=division_point, color='green', linestyle='--', alpha=0.5)\n\n        ax.set_xlabel(x_label, fontsize=12, fontweight='bold')\n        ax.set_ylabel(y_label, fontsize=12, fontweight='bold')\n        ax.set_title(f'{curve_type}s - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        \n        # Only show legend in Color mode\n        if color_mode == 'Color':\n            ax.legend()\n        \n        ax.tick_params(axis='both', which='major', labelsize=10)\n        ax.set_xlim(e_min if curve_type == 'EDC' else k_min, e_max if curve_type == 'EDC' else k_max)\n        y_range = max_intensity - min_intensity\n        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)\n\n        plt.tight_layout()\n        fig.canvas.draw_idle()\n\n        return max_intensity - min_intensity\n    def save_plot(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_curves': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'use_canny': interactive_plot.children[10].value,\n            'sigma': interactive_plot.children[11].value,\n            'low_threshold': interactive_plot.children[12].value,\n            'high_threshold': interactive_plot.children[13].value,\n            'enable_averaging': interactive_plot.children[14].value,\n            'averaging_kernel_size': interactive_plot.children[15].value,\n            'avg_e_min': interactive_plot.children[16].value,\n            'avg_e_max': interactive_plot.children[17].value,\n            'avg_k_min': interactive_plot.children[18].value,\n            'avg_k_max': interactive_plot.children[19].value,\n            'fit_type': interactive_plot.children[20].value,\n            'fit_e_min': interactive_plot.children[21].value,\n            'fit_e_max': interactive_plot.children[22].value,\n            'color_mode': interactive_plot.children[23].value,\n            'show_max_marker': interactive_plot.children[24].value,\n            'show_second_max_marker': interactive_plot.children[25].value,\n            'division_point': interactive_plot.children[26].value,\n            'curve_type': interactive_plot.children[27].value\n        }\n        plot_curves(**current_values)\n        filename = f\"{current_values['curve_type']}_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        print(f\"Plot saved as {filename}\")\n\n    def export_data(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_curves': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'use_canny': interactive_plot.children[10].value,\n            'sigma': interactive_plot.children[11].value,\n            'low_threshold': interactive_plot.children[12].value,\n            'high_threshold': interactive_plot.children[13].value,\n            'enable_averaging': interactive_plot.children[14].value,\n            'averaging_kernel_size': interactive_plot.children[15].value,\n            'avg_e_min': interactive_plot.children[16].value,\n            'avg_e_max': interactive_plot.children[17].value,\n            'avg_k_min': interactive_plot.children[18].value,\n            'avg_k_max': interactive_plot.children[19].value,\n            'fit_type': interactive_plot.children[20].value,\n            'fit_e_min': interactive_plot.children[21].value,\n            'fit_e_max': interactive_plot.children[22].value,\n            'color_mode': interactive_plot.children[23].value,\n            'show_max_marker': interactive_plot.children[24].value,\n            'show_second_max_marker': interactive_plot.children[25].value,\n            'division_point': interactive_plot.children[26].value,\n            'curve_type': interactive_plot.children[27].value\n        }\n        \n        plot_data = all_plots[current_values['scan_index'] - 1]\n        k_parallel = plot_data['k_parallel'][0]\n        energy_values = plot_data['energy_values']\n        data_values = plot_data['data_values']\n\n        # Convert xarray DataArrays to numpy arrays if necessary\n        if isinstance(k_parallel, xr.DataArray):\n            k_parallel = k_parallel.values\n        if isinstance(energy_values, xr.DataArray):\n            energy_values = energy_values.values\n        if isinstance(data_values, xr.DataArray):\n            data_values = data_values.values\n\n        # Apply filters only to the selected range\n        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]\n        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]\n        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]\n        k_parallel = k_parallel[valid_k_indices]\n        energy_values = energy_values[valid_e_indices]\n\n        if current_values['use_canny']:\n            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])\n            data_to_plot = edges.astype(float)\n\n        if current_values['enable_averaging']:\n            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)\n            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)\n            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')\n\n        if current_values['curve_type'] == 'EDC':\n            indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)\n            x_values = energy_values\n            y_label = 'Intensity (arb. units)'\n            x_label = 'Energy (eV)'\n        else:  # MDC\n            indices = np.linspace(0, len(valid_e_indices) - 1, current_values['n'], dtype=int)\n            x_values = k_parallel\n            y_label = 'Intensity (arb. units)'\n            x_label = rf'k_parallel ($\\AA^{-1}$)'\n\n        # Get the current axes\n        ax = plt.gca()\n\n        for i, index in enumerate(indices):\n            if current_values['curve_type'] == 'EDC':\n                curve = data_to_plot[:, index]\n                actual_value = k_parallel[index]\n            else:  # MDC\n                curve = data_to_plot[index, :]\n                actual_value = energy_values[index]\n\n            if isinstance(actual_value, np.ndarray):\n                actual_value = actual_value.item()\n\n            # Apply normalization if not using Canny filter\n            if not current_values['use_canny']:\n                curve = curve / np.max(curve)\n\n            # Export original/processed data\n            np.savetxt(f\"{current_values['curve_type']}_{actual_value:.2f}.dat\", np.column_stack((x_values, curve)), header=f\"{x_label}\\t{y_label}\")\n\n            if current_values['show_fit']:\n                # Find the fit line in the plot\n                fit_line = None\n                for line in ax.lines:\n                    if line.get_label().startswith(f\"Fit {current_values['curve_type']}={actual_value:.2f}\"):\n                        fit_line = line\n                        break\n\n                if fit_line is not None:\n                    # Get the x and y data of the fit line\n                    fit_x_data, fit_y_data = fit_line.get_data()\n                    # Remove the vertical offset\n                    fit_y_data -= i * current_values['vertical_offset']\n                    # Export the fit data\n                    np.savetxt(f\"gaussian_fit_{current_values['curve_type']}_{actual_value:.2f}.dat\", np.column_stack((fit_x_data, fit_y_data)), header=f\"{x_label}\\tFitted {y_label}\")\n                else:\n                    print(f\"No fit found for {current_values['curve_type']} = {actual_value:.2f}\")\n\n        print(\"Data export completed.\")\n\nclass DynamicFloatSlider(FloatSlider):\n    def __init__(self, *args, **kwargs):\n        super().__init__(*args, **kwargs)\n        self.observe(self._update_bounds, names=['value'])\n\n    def _update_bounds(self, change):\n        self.min = min(self.min, change['new'])\n        self.max = max(self.max, change['new'])\n\ndef update_division_point(*args):\n    curve_type = interactive_plot.children[-1].value\n    if curve_type == 'EDC':\n        e_min = interactive_plot.children[8].value\n        e_max = interactive_plot.children[9].value\n        interactive_plot.children[-2].min = e_min\n        interactive_plot.children[-2].max = e_max\n    else:  # MDC\n        k_min = interactive_plot.children[6].value\n        k_max = interactive_plot.children[7].value\n        interactive_plot.children[-2].min = k_min\n        interactive_plot.children[-2].max = k_max\n\n    interactive_plot = interactive(\n        plot_curves,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of Curves', continuous_update=True),\n        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),\n        show_curves=Checkbox(value=True, description='Show Curves'),\n        show_fit=Checkbox(value=False, description='Show Fits'),\n        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),\n        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),\n        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),\n        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),\n        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),\n        use_canny=Checkbox(value=False, description='Use Canny Filter'),\n        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),\n        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),\n        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),\n        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),\n        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),\n        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),\n        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),\n        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min (Å⁻¹)', continuous_update=True),\n        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max (Å⁻¹)', continuous_update=True),\n        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),\n        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),\n        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True),\n        color_mode=ToggleButtons(options=['Color', 'Grayscale'], description='Color Mode'),\n        marker_type=ToggleButtons(options=['None', 'Max', 'Min', 'Dual Max', 'Dual Min'], description='Marker Type'),\n        division_point=DynamicFloatSlider(value=(global_e_min + global_e_max) / 2, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Division Point', continuous_update=True),\n        curve_type=ToggleButtons(options=['EDC', 'MDC'], description='Curve Type')\n    )\n\n    # Observe changes in curve_type, e_min, e_max, k_min, and k_max\n    interactive_plot.children[-1].observe(update_division_point, names='value')\n    interactive_plot.children[6].observe(update_division_point, names='value')\n    interactive_plot.children[7].observe(update_division_point, names='value')\n    interactive_plot.children[8].observe(update_division_point, names='value')\n    interactive_plot.children[9].observe(update_division_point, names='value')\n\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    export_button = Button(description=\"Export Data\")\n    export_button.on_click(export_data)\n\n    output = VBox([interactive_plot, HBox([save_button, export_button])])\n    r\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n    interactive_plot_with_save = edc_plot(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")", 28185)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.5.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.5.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.6": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons\nfrom matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm\nfrom IPython.display import display, clear_output\nimport tkinter as tk\nfrom tkinter import filedialog\nimport warnings\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nfrom matplotlib import MatplotlibDeprecationWarning\nfrom lmfit.models import GaussianModel, LinearModel\nfrom scipy.signal import find_peaks\nfrom skimage.feature import canny\nfrom scipy.ndimage import convolve\nfrom ipywidgets import ToggleButtons\n\n%matplotlib widget\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",\n    \"axes.labelcolor\": \"black\",\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()\n    return [os.path.join(folder_path, f) for f in data_files]\n\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef edc_plot(data_files, work_function):\n    # Constants\n    hbar = 1.054571817e-34  # J*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    global_k_min = float('inf')\n    global_k_max = float('-inf')\n    global_e_min = float('inf')\n    global_e_max = float('-inf')\n    global_intensity_max = float('-inf')\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']\n\n        # Calculate kinetic energy and momentum\n        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19\n        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10\n\n        # Get the energy values\n        energy_values = -data.eV.values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        # Update global ranges\n        global_k_min = min(global_k_min, np.min(k_parallel))\n        global_k_max = max(global_k_max, np.max(k_parallel))\n        global_e_min = min(global_e_min, np.min(energy_values))\n        global_e_max = max(global_e_max, np.max(energy_values))\n        global_intensity_max = max(global_intensity_max, np.max(data.values))\n\n    global_k_range = global_k_max - global_k_min\n    global_e_range = global_e_max - global_e_min\n\n    fig, ax = plt.subplots(figsize=(10, 8))\n\n    def plot_curves(scan_index, n, vertical_offset, show_curves, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max, color_mode, marker_type, division_point, curve_type):\n        plot_data = all_plots[scan_index - 1]\n        ax.clear()\n\n        # Get the full data\n        data_to_plot = plot_data['data_values'].copy()\n        k_parallel = plot_data['k_parallel'][0]\n        energy_values = plot_data['energy_values']\n\n        # Apply filters only to the selected range\n        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]\n        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]\n        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]\n        k_parallel = k_parallel[valid_k_indices]\n        energy_values = energy_values[valid_e_indices]\n\n        if use_canny:\n            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)\n            data_to_plot = edges.astype(float)\n\n        if enable_averaging:\n            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)\n            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)\n            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]\n            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]\n            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]\n            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')\n            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data\n\n        if curve_type == 'EDC':\n            indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)\n            x_values = energy_values\n            y_label = 'Intensity (arb. units)'\n            x_label = r'$E-E_f$ (eV)'\n            division_min, division_max = e_min, e_max\n        else:  # MDC\n            indices = np.linspace(0, len(valid_e_indices) - 1, n, dtype=int)\n            x_values = k_parallel\n            y_label = 'Intensity (arb. units)'\n            x_label = r'$k_\\parallel$ (Å⁻¹)'\n            division_min, division_max = k_min, k_max\n\n        # Ensure division_point is within the current bounds\n        division_point = max(division_min, min(division_max, division_point))\n\n        max_intensity = float('-inf')\n        min_intensity = float('inf')\n\n        for i, index in enumerate(indices):\n            if curve_type == 'EDC':\n                curve = data_to_plot[:, index]\n                actual_value = k_parallel[index]\n                label = fr'$k_\\parallel$ = {actual_value:.2f}'\n            else:  # MDC\n                curve = data_to_plot[index, :]\n                actual_value = energy_values[index]\n                label = fr'$E-E_f$ = {actual_value:.2f}'\n\n            if isinstance(curve, xr.DataArray):\n                curve = curve.values\n            if isinstance(x_values, xr.DataArray):\n                x_values = x_values.values\n\n            if not use_canny:\n                max_index = np.argmax(curve)\n                min_index = np.argmin(curve)\n                curve = curve / np.max(curve)\n\n            offset_curve = curve + i * vertical_offset\n            max_intensity = max(max_intensity, np.max(offset_curve))\n            min_intensity = min(min_intensity, np.min(offset_curve))\n\n            if show_curves:\n                if color_mode == 'Color':\n                    ax.plot(x_values, offset_curve, label=label)\n                else:  # Grayscale\n                    ax.plot(x_values, offset_curve, color='black')\n                \n                if marker_type != 'None' and not use_canny:\n                    if marker_type in ['Max', 'Dual Max']:\n                        ax.plot(x_values[max_index], offset_curve[max_index], 'bo', markersize=10, fillstyle='none')\n                    \n                    if marker_type in ['Min', 'Dual Min']:\n                        ax.plot(x_values[min_index], offset_curve[min_index], 'ro', markersize=10, fillstyle='none')\n                    \n                    if marker_type in ['Dual Max', 'Dual Min']:\n                        left_index = np.argmax(curve[x_values <= division_point]) if 'Max' in marker_type else np.argmin(curve[x_values <= division_point])\n                        right_index = len(x_values) - 1 - np.argmax(curve[x_values > division_point][::-1]) if 'Max' in marker_type else len(x_values) - 1 - np.argmin(curve[x_values > division_point][::-1])\n                        \n                        ax.plot(x_values[left_index], offset_curve[left_index], 'ro', markersize=10, fillstyle='none')\n                        ax.plot(x_values[right_index], offset_curve[right_index], 'bo', markersize=10, fillstyle='none')\n\n            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)\n\n            if show_fit:\n                # Filter data for fitting\n                fit_indices = np.where((x_values >= fit_e_min) & (x_values <= fit_e_max))[0]\n                fit_x_values = x_values[fit_indices]\n                fit_curve = curve[fit_indices]\n\n                if fit_type == 'Maxima':\n                    peaks, _ = find_peaks(fit_curve)\n                    peak_heights = fit_curve[peaks]\n                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]\n                    largest_peaks = peaks[sorted_indices]\n\n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(fit_curve))\n\n                    for j, peak in enumerate(largest_peaks):\n                        gaussian = GaussianModel(prefix=f'g{j+1}_')\n                        model += gaussian\n                        params.update(gaussian.make_params())\n                        params[f'g{j+1}_center'].set(value=fit_x_values[peak], min=fit_x_values.min(), max=fit_x_values.max())\n                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)\n\n                elif fit_type == 'Minima':\n                    valleys, _ = find_peaks(-fit_curve)\n                    valley_depths = np.max(fit_curve) - fit_curve[valleys]\n                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]\n                    largest_valleys = valleys[sorted_indices]\n\n                    model = LinearModel(prefix='bkg_')\n                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(fit_curve))\n\n                    for j, valley in enumerate(largest_valleys):\n                        gaussian = GaussianModel(prefix=f'g{j+1}_')\n                        model += gaussian\n                        params.update(gaussian.make_params())\n                        params[f'g{j+1}_center'].set(value=fit_x_values[valley], min=fit_x_values.min(), max=fit_x_values.max())\n                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])\n                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)\n\n                result = model.fit(fit_curve, params, x=fit_x_values)\n                fit = result.best_fit\n\n                # Interpolate the fit back to the full x range\n                full_fit = np.interp(x_values, fit_x_values, fit)\n                offset_fit = full_fit + i * vertical_offset\n\n                # Extract sigma values and their uncertainties for label\n                sigmas = []\n                sigma_errors = []\n                for j in range(num_peaks):\n                    sigma = abs(result.params[f'g{j+1}_sigma'].value)\n                    sigma_error = result.params[f'g{j+1}_sigma'].stderr\n                    sigmas.append(sigma)\n                    sigma_errors.append(sigma_error)\n\n                sigma_label = ', '.join([fr'$\\sigma_{j+1}$={sigma:.3f} $\\pm$ {error:.3f}' for j, (sigma, error) in enumerate(zip(sigmas, sigma_errors))])\n\n                if color_mode == 'Color':\n                    ax.plot(x_values, offset_fit, '--', label=fr'Fit {curve_type}={actual_value:.2f}, {sigma_label}', color=f'C{i}')\n                else:  # Grayscale\n                    ax.plot(x_values, offset_fit, '--', color='black')\n\n                if fit_type == 'Maxima':\n                    fit_peaks, _ = find_peaks(full_fit)\n                    ax.plot(x_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)\n\n                    for j, peak in enumerate(fit_peaks):\n                        peak_x = x_values[peak]\n                        peak_intensity = offset_fit[peak]\n                        sigma = sigmas[j]\n                        sigma_error = sigma_errors[j]\n                        left_sigma_x = peak_x - sigma\n                        right_sigma_x = peak_x + sigma\n                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)\n                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)\n\n                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)\n                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)\n                        ax.annotate(fr'$-\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')\n                        ax.annotate(fr'$+\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')\n\n                elif fit_type == 'Minima':\n                    fit_valleys, _ = find_peaks(-full_fit)\n                    ax.plot(x_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)\n\n                    for j, valley in enumerate(fit_valleys):\n                        valley_x = x_values[valley]\n                        valley_intensity = offset_fit[valley]\n                        sigma = sigmas[j]\n                        sigma_error = sigma_errors[j]\n                        left_sigma_x = valley_x - sigma\n                        right_sigma_x = valley_x + sigma\n                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)\n                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)\n\n                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)\n                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)\n                        ax.annotate(fr'$-\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')\n                        ax.annotate(fr'$+\\sigma_{j+1}$={sigma:.3f}$\\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')\n\n        # Add a vertical line at the division point\n        if marker_type in ['Dual Max', 'Dual Min']:\n            ax.axvline(x=division_point, color='green', linestyle='--', alpha=0.5)\n\n        ax.set_xlabel(x_label, fontsize=12, fontweight='bold')\n        ax.set_ylabel(y_label, fontsize=12, fontweight='bold')\n        ax.set_title(f'{curve_type}s - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        \n        # Only show legend in Color mode\n        if color_mode == 'Color':\n            ax.legend()\n        \n        ax.tick_params(axis='both', which='major', labelsize=10)\n        ax.set_xlim(e_min if curve_type == 'EDC' else k_min, e_max if curve_type == 'EDC' else k_max)\n        y_range = max_intensity - min_intensity\n        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)\n\n        plt.tight_layout()\n        fig.canvas.draw_idle()\n\n        return max_intensity - min_intensity\n    def save_plot(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_curves': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'use_canny': interactive_plot.children[10].value,\n            'sigma': interactive_plot.children[11].value,\n            'low_threshold': interactive_plot.children[12].value,\n            'high_threshold': interactive_plot.children[13].value,\n            'enable_averaging': interactive_plot.children[14].value,\n            'averaging_kernel_size': interactive_plot.children[15].value,\n            'avg_e_min': interactive_plot.children[16].value,\n            'avg_e_max': interactive_plot.children[17].value,\n            'avg_k_min': interactive_plot.children[18].value,\n            'avg_k_max': interactive_plot.children[19].value,\n            'fit_type': interactive_plot.children[20].value,\n            'fit_e_min': interactive_plot.children[21].value,\n            'fit_e_max': interactive_plot.children[22].value,\n            'color_mode': interactive_plot.children[23].value,\n            'show_max_marker': interactive_plot.children[24].value,\n            'show_second_max_marker': interactive_plot.children[25].value,\n            'division_point': interactive_plot.children[26].value,\n            'curve_type': interactive_plot.children[27].value\n        }\n        plot_curves(**current_values)\n        filename = f\"{current_values['curve_type']}_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        print(f\"Plot saved as {filename}\")\n\n    def export_data(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_curves': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'use_canny': interactive_plot.children[10].value,\n            'sigma': interactive_plot.children[11].value,\n            'low_threshold': interactive_plot.children[12].value,\n            'high_threshold': interactive_plot.children[13].value,\n            'enable_averaging': interactive_plot.children[14].value,\n            'averaging_kernel_size': interactive_plot.children[15].value,\n            'avg_e_min': interactive_plot.children[16].value,\n            'avg_e_max': interactive_plot.children[17].value,\n            'avg_k_min': interactive_plot.children[18].value,\n            'avg_k_max': interactive_plot.children[19].value,\n            'fit_type': interactive_plot.children[20].value,\n            'fit_e_min': interactive_plot.children[21].value,\n            'fit_e_max': interactive_plot.children[22].value,\n            'color_mode': interactive_plot.children[23].value,\n            'show_max_marker': interactive_plot.children[24].value,\n            'show_second_max_marker': interactive_plot.children[25].value,\n            'division_point': interactive_plot.children[26].value,\n            'curve_type': interactive_plot.children[27].value\n        }\n        \n        plot_data = all_plots[current_values['scan_index'] - 1]\n        k_parallel = plot_data['k_parallel'][0]\n        energy_values = plot_data['energy_values']\n        data_values = plot_data['data_values']\n\n        # Convert xarray DataArrays to numpy arrays if necessary\n        if isinstance(k_parallel, xr.DataArray):\n            k_parallel = k_parallel.values\n        if isinstance(energy_values, xr.DataArray):\n            energy_values = energy_values.values\n        if isinstance(data_values, xr.DataArray):\n            data_values = data_values.values\n\n        # Apply filters only to the selected range\n        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]\n        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]\n        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]\n        k_parallel = k_parallel[valid_k_indices]\n        energy_values = energy_values[valid_e_indices]\n\n        if current_values['use_canny']:\n            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])\n            data_to_plot = edges.astype(float)\n\n        if current_values['enable_averaging']:\n            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)\n            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)\n            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')\n\n        if current_values['curve_type'] == 'EDC':\n            indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)\n            x_values = energy_values\n            y_label = 'Intensity (arb. units)'\n            x_label = 'Energy (eV)'\n        else:  # MDC\n            indices = np.linspace(0, len(valid_e_indices) - 1, current_values['n'], dtype=int)\n            x_values = k_parallel\n            y_label = 'Intensity (arb. units)'\n            x_label = rf'k_parallel ($\\AA^{-1}$)'\n\n        # Get the current axes\n        ax = plt.gca()\n\n        for i, index in enumerate(indices):\n            if current_values['curve_type'] == 'EDC':\n                curve = data_to_plot[:, index]\n                actual_value = k_parallel[index]\n            else:  # MDC\n                curve = data_to_plot[index, :]\n                actual_value = energy_values[index]\n\n            if isinstance(actual_value, np.ndarray):\n                actual_value = actual_value.item()\n\n            # Apply normalization if not using Canny filter\n            if not current_values['use_canny']:\n                curve = curve / np.max(curve)\n\n            # Export original/processed data\n            np.savetxt(f\"{current_values['curve_type']}_{actual_value:.2f}.dat\", np.column_stack((x_values, curve)), header=f\"{x_label}\\t{y_label}\")\n\n            if current_values['show_fit']:\n                # Find the fit line in the plot\n                fit_line = None\n                for line in ax.lines:\n                    if line.get_label().startswith(f\"Fit {current_values['curve_type']}={actual_value:.2f}\"):\n                        fit_line = line\n                        break\n\n                if fit_line is not None:\n                    # Get the x and y data of the fit line\n                    fit_x_data, fit_y_data = fit_line.get_data()\n                    # Remove the vertical offset\n                    fit_y_data -= i * current_values['vertical_offset']\n                    # Export the fit data\n                    np.savetxt(f\"gaussian_fit_{current_values['curve_type']}_{actual_value:.2f}.dat\", np.column_stack((fit_x_data, fit_y_data)), header=f\"{x_label}\\tFitted {y_label}\")\n                else:\n                    print(f\"No fit found for {current_values['curve_type']} = {actual_value:.2f}\")\n\n        print(\"Data export completed.\")\n\nclass DynamicFloatSlider(FloatSlider):\n    def __init__(self, *args, **kwargs):\n        super().__init__(*args, **kwargs)\n        self.observe(self._update_bounds, names=['value'])\n\n    def _update_bounds(self, change):\n        self.min = min(self.min, change['new'])\n        self.max = max(self.max, change['new'])\n\ndef update_division_point(*args):\n    curve_type = interactive_plot.children[-1].value\n    if curve_type == 'EDC':\n        e_min = interactive_plot.children[8].value\n        e_max = interactive_plot.children[9].value\n        interactive_plot.children[-2].min = e_min\n        interactive_plot.children[-2].max = e_max\n    else:  # MDC\n        k_min = interactive_plot.children[6].value\n        k_max = interactive_plot.children[7].value\n        interactive_plot.children[-2].min = k_min\n        interactive_plot.children[-2].max = k_max\n\n    interactive_plot = interactive(\n        plot_curves,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of Curves', continuous_update=True),\n        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),\n        show_curves=Checkbox(value=True, description='Show Curves'),\n        show_fit=Checkbox(value=False, description='Show Fits'),\n        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),\n        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),\n        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),\n        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),\n        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),\n        use_canny=Checkbox(value=False, description='Use Canny Filter'),\n        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),\n        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),\n        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),\n        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),\n        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),\n        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),\n        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),\n        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min (Å⁻¹)', continuous_update=True),\n        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max (Å⁻¹)', continuous_update=True),\n        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),\n        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),\n        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True),\n        color_mode=ToggleButtons(options=['Color', 'Grayscale'], description='Color Mode'),\n        marker_type=ToggleButtons(options=['None', 'Max', 'Min', 'Dual Max', 'Dual Min'], description='Marker Type'),\n        division_point=DynamicFloatSlider(value=(global_e_min + global_e_max) / 2, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Division Point', continuous_update=True),\n        curve_type=ToggleButtons(options=['EDC', 'MDC'], description='Curve Type')\n    )\n\n    # Observe changes in curve_type, e_min, e_max, k_min, and k_max\n    interactive_plot.children[-1].observe(update_division_point, names='value')\n    interactive_plot.children[6].observe(update_division_point, names='value')\n    interactive_plot.children[7].observe(update_division_point, names='value')\n    interactive_plot.children[8].observe(update_division_point, names='value')\n    interactive_plot.children[9].observe(update_division_point, names='value')\n\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    export_button = Button(description=\"Export Data\")\n    export_button.on_click(export_data)\n\n    output = VBox([interactive_plot, HBox([save_button, export_button])])\n    return o\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n    interactive_plot_with_save = edc_plot(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")", 28192)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.6.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.6.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import GaussianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
from ipywidgets import ToggleButtons

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_curves(scan_index, n, vertical_offset, show_curves, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max, color_mode, marker_type, division_point, curve_type):
        plot_data = all_plots[scan_index - 1]
        ax.clear()

        # Get the full data
        data_to_plot = plot_data['data_values'].copy()
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]
            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]
            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]
            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')
            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data

        if curve_type == 'EDC':
            indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)
            x_values = energy_values
            y_label = 'Intensity (arb. units)'
            x_label = r'$E-E_f$ (eV)'
            division_min, division_max = e_min, e_max
        else:  # MDC
            indices = np.linspace(0, len(valid_e_indices) - 1, n, dtype=int)
            x_values = k_parallel
            y_label = 'Intensity (arb. units)'
            x_label = r'$k_\parallel$ (Å⁻¹)'
            division_min, division_max = k_min, k_max

        # Ensure division_point is within the current bounds
        division_point = max(division_min, min(division_max, division_point))

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, index in enumerate(indices):
            if curve_type == 'EDC':
                curve = data_to_plot[:, index]
                actual_value = k_parallel[index]
                label = fr'$k_\parallel$ = {actual_value:.2f}'
            else:  # MDC
                curve = data_to_plot[index, :]
                actual_value = energy_values[index]
                label = fr'$E-E_f$ = {actual_value:.2f}'

            if isinstance(curve, xr.DataArray):
                curve = curve.values
            if isinstance(x_values, xr.DataArray):
                x_values = x_values.values

            if not use_canny:
                max_index = np.argmax(curve)
                min_index = np.argmin(curve)
                curve = curve / np.max(curve)

            offset_curve = curve + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_curve))
            min_intensity = min(min_intensity, np.min(offset_curve))

            if show_curves:
                if color_mode == 'Color':
                    ax.plot(x_values, offset_curve, label=label)
                else:  # Grayscale
                    ax.plot(x_values, offset_curve, color='black')
                
                if marker_type != 'None' and not use_canny:
                    if marker_type in ['Max', 'Dual Max']:
                        ax.plot(x_values[max_index], offset_curve[max_index], 'bo', markersize=10, fillstyle='none')
                    
                    if marker_type in ['Min', 'Dual Min']:
                        ax.plot(x_values[min_index], offset_curve[min_index], 'ro', markersize=10, fillstyle='none')
                    
                    if marker_type in ['Dual Max', 'Dual Min']:
                        left_index = np.argmax(curve[x_values <= division_point]) if 'Max' in marker_type else np.argmin(curve[x_values <= division_point])
                        right_index = len(x_values) - 1 - np.argmax(curve[x_values > division_point][::-1]) if 'Max' in marker_type else len(x_values) - 1 - np.argmin(curve[x_values > division_point][::-1])
                        
                        ax.plot(x_values[left_index], offset_curve[left_index], 'ro', markersize=10, fillstyle='none')
                        ax.plot(x_values[right_index], offset_curve[right_index], 'bo', markersize=10, fillstyle='none')

            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Filter data for fitting
                fit_indices = np.where((x_values >= fit_e_min) & (x_values <= fit_e_max))[0]
                fit_x_values = x_values[fit_indices]
                fit_curve = curve[fit_indices]

                if fit_type == 'Maxima':
                    peaks, _ = find_peaks(fit_curve)
                    peak_heights = fit_curve[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(fit_curve))

                    for j, peak in enumerate(largest_peaks):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_x_values[peak], min=fit_x_values.min(), max=fit_x_values.max())
                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                elif fit_type == 'Minima':
                    valleys, _ = find_peaks(-fit_curve)
                    valley_depths = np.max(fit_curve) - fit_curve[valleys]
                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]
                    largest_valleys = valleys[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(fit_curve))

                    for j, valley in enumerate(largest_valleys):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_x_values[valley], min=fit_x_values.min(), max=fit_x_values.max())
                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(fit_curve, params, x=fit_x_values)
                fit = result.best_fit

                # Interpolate the fit back to the full x range
                full_fit = np.interp(x_values, fit_x_values, fit)
                offset_fit = full_fit + i * vertical_offset

                # Extract sigma values and their uncertainties for label
                sigmas = []
                sigma_errors = []
                for j in range(num_peaks):
                    sigma = abs(result.params[f'g{j+1}_sigma'].value)
                    sigma_error = result.params[f'g{j+1}_sigma'].stderr
                    sigmas.append(sigma)
                    sigma_errors.append(sigma_error)

                sigma_label = ', '.join([fr'$\sigma_{j+1}$={sigma:.3f} $\pm$ {error:.3f}' for j, (sigma, error) in enumerate(zip(sigmas, sigma_errors))])

                if color_mode == 'Color':
                    ax.plot(x_values, offset_fit, '--', label=fr'Fit {curve_type}={actual_value:.2f}, {sigma_label}', color=f'C{i}')
                else:  # Grayscale
                    ax.plot(x_values, offset_fit, '--', color='black')

                if fit_type == 'Maxima':
                    fit_peaks, _ = find_peaks(full_fit)
                    ax.plot(x_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)

                    for j, peak in enumerate(fit_peaks):
                        peak_x = x_values[peak]
                        peak_intensity = offset_fit[peak]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_x = peak_x - sigma
                        right_sigma_x = peak_x + sigma
                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)

                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')

                elif fit_type == 'Minima':
                    fit_valleys, _ = find_peaks(-full_fit)
                    ax.plot(x_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

                    for j, valley in enumerate(fit_valleys):
                        valley_x = x_values[valley]
                        valley_intensity = offset_fit[valley]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_x = valley_x - sigma
                        right_sigma_x = valley_x + sigma
                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)

                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')

        # Add a vertical line at the division point
        if marker_type in ['Dual Max', 'Dual Min']:
            ax.axvline(x=division_point, color='green', linestyle='--', alpha=0.5)

        ax.set_xlabel(x_label, fontsize=12, fontweight='bold')
        ax.set_ylabel(y_label, fontsize=12, fontweight='bold')
        ax.set_title(f'{curve_type}s - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        
        # Only show legend in Color mode
        if color_mode == 'Color':
            ax.legend()
        
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_xlim(e_min if curve_type == 'EDC' else k_min, e_max if curve_type == 'EDC' else k_max)
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity
    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_curves': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'avg_e_min': interactive_plot.children[16].value,
            'avg_e_max': interactive_plot.children[17].value,
            'avg_k_min': interactive_plot.children[18].value,
            'avg_k_max': interactive_plot.children[19].value,
            'fit_type': interactive_plot.children[20].value,
            'fit_e_min': interactive_plot.children[21].value,
            'fit_e_max': interactive_plot.children[22].value,
            'color_mode': interactive_plot.children[23].value,
            'show_max_marker': interactive_plot.children[24].value,
            'show_second_max_marker': interactive_plot.children[25].value,
            'division_point': interactive_plot.children[26].value,
            'curve_type': interactive_plot.children[27].value
        }
        plot_curves(**current_values)
        filename = f"{current_values['curve_type']}_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def export_data(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_curves': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'avg_e_min': interactive_plot.children[16].value,
            'avg_e_max': interactive_plot.children[17].value,
            'avg_k_min': interactive_plot.children[18].value,
            'avg_k_max': interactive_plot.children[19].value,
            'fit_type': interactive_plot.children[20].value,
            'fit_e_min': interactive_plot.children[21].value,
            'fit_e_max': interactive_plot.children[22].value,
            'color_mode': interactive_plot.children[23].value,
            'show_max_marker': interactive_plot.children[24].value,
            'show_second_max_marker': interactive_plot.children[25].value,
            'division_point': interactive_plot.children[26].value,
            'curve_type': interactive_plot.children[27].value
        }
        
        plot_data = all_plots[current_values['scan_index'] - 1]
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']
        data_values = plot_data['data_values']

        # Convert xarray DataArrays to numpy arrays if necessary
        if isinstance(k_parallel, xr.DataArray):
            k_parallel = k_parallel.values
        if isinstance(energy_values, xr.DataArray):
            energy_values = energy_values.values
        if isinstance(data_values, xr.DataArray):
            data_values = data_values.values

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]
        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]
        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if current_values['use_canny']:
            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])
            data_to_plot = edges.astype(float)

        if current_values['enable_averaging']:
            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')

        if current_values['curve_type'] == 'EDC':
            indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)
            x_values = energy_values
            y_label = 'Intensity (arb. units)'
            x_label = 'Energy (eV)'
        else:  # MDC
            indices = np.linspace(0, len(valid_e_indices) - 1, current_values['n'], dtype=int)
            x_values = k_parallel
            y_label = 'Intensity (arb. units)'
            x_label = rf'k_parallel ($\AA^{-1}$)'

        # Get the current axes
        ax = plt.gca()

        for i, index in enumerate(indices):
            if current_values['curve_type'] == 'EDC':
                curve = data_to_plot[:, index]
                actual_value = k_parallel[index]
            else:  # MDC
                curve = data_to_plot[index, :]
                actual_value = energy_values[index]

            if isinstance(actual_value, np.ndarray):
                actual_value = actual_value.item()

            # Apply normalization if not using Canny filter
            if not current_values['use_canny']:
                curve = curve / np.max(curve)

            # Export original/processed data
            np.savetxt(f"{current_values['curve_type']}_{actual_value:.2f}.dat", np.column_stack((x_values, curve)), header=f"{x_label}\t{y_label}")

            if current_values['show_fit']:
                # Find the fit line in the plot
                fit_line = None
                for line in ax.lines:
                    if line.get_label().startswith(f"Fit {current_values['curve_type']}={actual_value:.2f}"):
                        fit_line = line
                        break

                if fit_line is not None:
                    # Get the x and y data of the fit line
                    fit_x_data, fit_y_data = fit_line.get_data()
                    # Remove the vertical offset
                    fit_y_data -= i * current_values['vertical_offset']
                    # Export the fit data
                    np.savetxt(f"gaussian_fit_{current_values['curve_type']}_{actual_value:.2f}.dat", np.column_stack((fit_x_data, fit_y_data)), header=f"{x_label}\tFitted {y_label}")
                else:
                    print(f"No fit found for {current_values['curve_type']} = {actual_value:.2f}")

        print("Data export completed.")

class DynamicFloatSlider(FloatSlider):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.observe(self._update_bounds, names=['value'])

    def _update_bounds(self, change):
        self.min = min(self.min, change['new'])
        self.max = max(self.max, change['new'])

def update_division_point(*args):
    curve_type = interactive_plot.children[-1].value
    if curve_type == 'EDC':
        e_min = interactive_plot.children[8].value
        e_max = interactive_plot.children[9].value
        interactive_plot.children[-2].min = e_min
        interactive_plot.children[-2].max = e_max
    else:  # MDC
        k_min = interactive_plot.children[6].value
        k_max = interactive_plot.children[7].value
        interactive_plot.children[-2].min = k_min
        interactive_plot.children[-2].max = k_max

    interactive_plot = interactive(
        plot_curves,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of Curves', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
        show_curves=Checkbox(value=True, description='Show Curves'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),
        use_canny=Checkbox(value=False, description='Use Canny Filter'),
        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),
        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),
        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),
        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min (Å⁻¹)', continuous_update=True),
        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max (Å⁻¹)', continuous_update=True),
        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),
        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),
        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True),
        color_mode=ToggleButtons(options=['Color', 'Grayscale'], description='Color Mode'),
        marker_type=ToggleButtons(options=['None', 'Max', 'Min', 'Dual Max', 'Dual Min'], description='Marker Type'),
        division_point=DynamicFloatSlider(value=(global_e_min + global_e_max) / 2, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Division Point', continuous_update=True),
        curve_type=ToggleButtons(options=['EDC', 'MDC'], description='Curve Type')
    )

    # Observe changes in curve_type, e_min, e_max, k_min, and k_max
    interactive_plot.children[-1].observe(update_division_point, names='value')
    interactive_plot.children[6].observe(update_division_point, names='value')
    interactive_plot.children[7].observe(update_division_point, names='value')
    interactive_plot.children[8].observe(update_division_point, names='value')
    interactive_plot.children[9].observe(update_division_point, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    export_button = Button(description="Export Data")
    export_button.on_click(export_data)

    output = VBox([interactive_plot, HBox([save_button, export_button])])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
