# IPython log file

from arpes.io import load_data
#from arpes.endstations.plugin import *
import numpy as np
#if not hasattr(np, 'complex'):
    #np.complex = np.complex128
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')
from arpes.io import load_data
#from arpes.endstations.plugin import *
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = np.complex128
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.116": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("l", 1)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.116.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.116.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.117": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("load_p", 6)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.117.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.117.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.118": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("load_px", 7)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.118.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.118.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.119": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("load_pxt", 8)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.119.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.119.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
load_pxt
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.120": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data, l\n#from arpes.endstations.plugin import *\nimport numpy as np\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')", 33)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.120.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.120.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.io import load_data, load_pxt
#from arpes.endstations.plugin import *
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = np.complex128
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')
from arpes.io import *
#from arpes.endstations.plugin import *
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = np.complex128
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.121": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("l", 1)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.121.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.121.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.122": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("lo", 2)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.122.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.122.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.123": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("l", 1)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.123.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.123.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.124": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("p", 1)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.124.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.124.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.125": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pxt", 3)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.125.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.125.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.126": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("p", 1)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.126.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.126.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.127": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pxt", 3)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.127.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.127.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
pxt
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.128": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pxt)r", 5)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.128.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.128.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.129": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pxt)r", 5)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.129.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.129.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.130": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pxt_", 4)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.130.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.130.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.131": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pxt_r", 5)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.131.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.131.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.132": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pxt_re", 6)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.132.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.132.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.133": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pxt_read", 8)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.133.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.133.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.134": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pxt_reade", 9)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.134.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.134.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.135": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pxt_reader", 10)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.135.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.135.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
pxt_reader
import numpy as np
import xarray as xr
import h5py
import os
from typing import Dict, Any

class MERLINPXTParser:
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.data = None
        self.metadata = {}

    def parse(self) -> xr.Dataset:
        """Parse the PXT file and return an xarray Dataset."""
        self._read_pxt_file()
        self._extract_metadata()
        self._create_xarray_dataset()
        return self.data

    def _read_pxt_file(self):
        """Read the PXT file using h5py."""
        with h5py.File(self.file_path, 'r') as f:
            self.raw_data = {key: f[key][:] for key in f.keys()}

    def _extract_metadata(self):
        """Extract metadata from the raw data."""
        self.metadata = {
            'scan_type': self.raw_data.get('scan_type', b'').decode('utf-8'),
            'analyzer_work_function': float(self.raw_data.get('analyzer_work_function', 0)),
            'photon_energy': float(self.raw_data.get('photon_energy', 0)),
            'temperature': float(self.raw_data.get('temperature', 0)),
            'pass_energy': float(self.raw_data.get('pass_energy', 0)),
            'slit_width': float(self.raw_data.get('slit_width', 0)),
        }

    def _create_xarray_dataset(self):
        """Create an xarray Dataset from the raw data and metadata."""
        intensity = self.raw_data['intensity']
        energy = self.raw_data['energy']
        angle = self.raw_data['angle']

        coords = {
            'energy': ('energy', energy),
            'angle': ('angle', angle),
        }

        if self.metadata['scan_type'] == 'map':
            x = self.raw_data['x']
            y = self.raw_data['y']
            coords['x'] = ('x', x)
            coords['y'] = ('y', y)
            data_vars = {'intensity': (['y', 'x', 'angle', 'energy'], intensity)}
        else:
            data_vars = {'intensity': (['angle', 'energy'], intensity)}

        self.data = xr.Dataset(
            data_vars=data_vars,
            coords=coords,
            attrs=self.metadata
        )

def load_merlin_pxt(file_path: str) -> xr.Dataset:
    """Load a MERLIN PXT file and return an xarray Dataset."""
    parser = MERLINPXTParser(file_path)
    return parser.parse()

# Example usage
#if __name__ == "__main__":
   # file_path = "path/to/your/merlin_data.pxt"
   # data = load_merlin_pxt(file_path)
    #print(data)
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.136": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("loa", 2)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.136.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.136.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.137": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("data = l('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt')", 8)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.137.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.137.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
data = load_merlin_pxt('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.138": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import *\nf\nimport numpy as np\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')", 24)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.138.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.138.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.139": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import *\nfrom arpes.endstations.plugin.merlin i\nimport numpy as np\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')", 61)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.139.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.139.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.140": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes.io import *\nfrom arpes.endimport ", 44, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.140.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.140.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.io import *
from arpes.endstations.plugin.merlin import *
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = np.complex128
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.141": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import *\nfrom arpes.endstations.plugin.merlin import *\nimport numpy as np\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL403ARPESEndstation')", 278)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.141.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.141.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.142": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes.io import *\nfrom arpes.endstations.plugin.merlin import *\nimport numpy as np\nif not hasattr(np, 'complex'):\n    np.coAny", 131, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.142.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.142.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.io import *
from arpes.endstations.plugin.merlin import *
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = np.complex128
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL403ARPESEndstation')
from arpes.io import *
from arpes.endstations.plugin.merlin import *
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = np.complex128
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL403')
from arpes.io import *
from arpes.endstations.plugin.merlin import *
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = np.complex128
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL403')
