# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import GaussianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve

# Set up matplotlib and suppress warnings
get_ipython().run_line_magic('matplotlib', 'widget')
if not hasattr(np, 'complex'):
    np.complex = np.complex128
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana'] 
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black", 
    "axes.labelcolor": "black",
})
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

# Modules
class ARPESModule:
    def __init__(self):
        self.enabled = True
        
    def enable(self):
        self.enabled = True
        
    def disable(self):
        self.enabled = False
        
    def plot(self, ax, data):
        if self.enabled:
            self._plot(ax, data)
            
    def _plot(self, ax, data):
        raise NotImplementedError("Subclasses must implement _plot method")

        
class ColorMapModule(ARPESModule):
    def __init__(self):
        super().__init__()
        self.cmap = 'rainbow_light'
        self.vmin = 0
        self.vmax = None
        
    def set_cmap(self, cmap):
        self.cmap = cmap
        
    def set_clim(self, vmin, vmax):
        self.vmin = vmin
        self.vmax = vmax
        
    def _plot(self, ax, data):
        im = ax.pcolormesh(data['k_parallel'], data['energy_values'], data['data_values'], 
                           shading='auto', cmap=self.cmap, 
                           norm=Normalize(vmin=self.vmin, vmax=self.vmax))
        return im
        

class EDCModule(ARPESModule):
    def __init__(self):
        super().__init__()
        self.n = 5
        self.vertical_offset = 0.5
        self.show_edc = True
        self.show_fit = False
        self.num_peaks = 1
        self.fit_type = 'Maxima'
        self.fit_e_min = None
        self.fit_e_max = None
        
    def set_params(self, n=5, vertical_offset=0.5, show_edc=True, show_fit=False, 
                   num_peaks=1, fit_type='Maxima', fit_e_min=None, fit_e_max=None):
        self.n = n
        self.vertical_offset = vertical_offset
        self.show_edc = show_edc
        self.show_fit = show_fit
        self.num_peaks = num_peaks
        self.fit_type = fit_type
        self.fit_e_min = fit_e_min
        self.fit_e_max = fit_e_max
        
    def _plot(self, ax, data):
        k_indices = np.linspace(0, len(data['k_parallel']) - 1, self.n, dtype=int)
        for i, k_index in enumerate(k_indices):
            actual_k = data['k_parallel'][k_index]
            kdc = data['data_values'][:, k_index]
            if self.show_edc:
                offset_kdc = kdc + i * self.vertical_offset
                ax.plot(data['energy_values'], offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')
                ax.axhline(y=i * self.vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)
            if self.show_fit:
                self._plot_fit(ax, data['energy_values'], kdc, actual_k, i)
                
    def _plot_fit(self, ax, energy_values, kdc, actual_k, i):
        # Fit data and plot the fit line
        # (implementation similar to the original code)
        pass

        
class FilterModule(ARPESModule):
    def __init__(self):
        super().__init__()
        self.use_canny = False
        self.sigma = 1.0
        self.low_threshold = 0.1
        self.high_threshold = 0.2
        self.enable_averaging = False
        self.averaging_kernel_size = 2
        
    def set_params(self, use_canny=False, sigma=1.0, low_threshold=0.1, high_threshold=0.2,
                   enable_averaging=False, averaging_kernel_size=2):
        self.use_canny = use_canny
        self.sigma = sigma
        self.low_threshold = low_threshold
        self.high_threshold = high_threshold
        self.enable_averaging = enable_averaging
        self.averaging_kernel_size = averaging_kernel_size
        
    def _plot(self, ax, data):
        data_to_plot = data['data_values'].copy()
        if self.use_canny:
            data_to_plot = canny(data_to_plot, sigma=self.sigma, 
                                 low_threshold=self.low_threshold, 
                                 high_threshold=self.high_threshold)
        if self.enable_averaging:
            kernel_size = max(3, self.averaging_kernel_size // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')
        data['data_values'] = data_to_plot
        
        
# Main plotting function
def plot_arpes(data_files, work_function, modules):
    all_plots = []
    global_ranges = {'k_min': float('inf'), 'k_max': float('-inf'), 
                     'e_min': float('inf'), 'e_max': float('-inf'),
                     'intensity_max': float('-inf')}
    
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']
        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
        k_parallel = (np.sqrt(-2 * 9.1093837015e-31 * E_b) * np.sin(np.radians(data.phi)) / 1.054571817e-34) / 10**10
        energy_values = -data.eV.values
        
        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })
        
        global_ranges['k_min'] = min(global_ranges['k_min'], np.min(k_parallel))
        global_ranges['k_max'] = max(global_ranges['k_max'], np.max(k_parallel))
        global_ranges['e_min'] = min(global_ranges['e_min'], np.min(energy_values))
        global_ranges['e_max'] = max(global_ranges['e_max'], np.max(energy_values))
        global_ranges['intensity_max'] = max(global_ranges['intensity_max'], np.max(data.values))
        
    fig, ax = plt.subplots(figsize=(10, 8))
    
    def update_plot(scan_index, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]
        ax.clear()
        
        valid_k_indices = np.where((plot_data['k_parallel'] >= k_min) & (plot_data['k_parallel'] <= k_max))[0]
        valid_e_indices = np.where((plot_data['energy_values'] >= e_min) & (plot_data['energy_values'] <= e_max))[0]
        data_to_plot = {
            'k_parallel': plot_data['k_parallel'][valid_k_indices],
            'energy_values': plot_data['energy_values'][valid_e_indices],
            'data_values': plot_data['data_values'][np.ix_(valid_e_indices, valid_k_indices)]
        }
        
        for module in modules:
            module.plot(ax, data_to_plot)
            
        ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_xlim(data_to_plot['k_parallel'].min(), data_to_plot['k_parallel'].max())
        ax.set_ylim(data_to_plot['energy_values'].min(), data_to_plot['energy_values'].max())
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()
        
    interactive_plot = interactive(
        update_plot,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        k_min=FloatSlider(value=global_ranges['k_min'], min=global_ranges['k_min'], max=global_ranges['k_max'], 
                          step=(global_ranges['k_max']-global_ranges['k_min'])/1000, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_ranges['k_max'], min=global_ranges['k_min'], max=global_ranges['k_max'], 
                          step=(global_ranges['k_max']-global_ranges['k_min'])/1000, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_ranges['e_min'], min=global_ranges['e_min'], max=global_ranges['e_max'], 
                          step=(global_ranges['e_max']-global_ranges['e_min'])/1000, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_ranges['e_max'], min=global_ranges['e_min'], max=global_ranges['e_max'], 
                          step=(global_ranges['e_max']-global_ranges['e_min'])/1000, description='E_max (eV)', continuous_update=True)
    )
    
    return interactive_plot

# Usage
if __name__ == '__main__':
    root = tk.Tk()
    root.withdraw()
    folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
    root.destroy()

    if folder_path:
        data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
        data_files.sort()
        work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
        
        # Create module instances
        color_map_module = ColorMapModule()
        edc_module = EDCModule()
        filter_module = FilterModule()
        
        # Configure modules (optional)
        color_map_module.set_cmap('rainbow_light')
        color_map_module.set_clim(0, None)
        edc_module.set_params(n=5, vertical_offset=0.5, show_edc=True, show_fit=False, num_peaks=1, 
                              fit_type='Maxima', fit_e_min=None, fit_e_max=None)
        filter_module.set_params(use_canny=False, sigma=1.0, low_threshold=0.1, high_threshold=0.2,
                                 enable_averaging=False, averaging_kernel_size=2)
        
        # Create a list of enabled modules
        modules = [color_map_module, edc_module, filter_module]
        
        interactive_plot = plot_arpes(data_files, work_function, modules)
        display(interactive_plot)
    else:
        print("No folder selected.")
