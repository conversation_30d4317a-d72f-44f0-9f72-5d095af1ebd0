# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_energy_min = float('inf')
    global_energy_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global energy range
        global_energy_min = min(global_energy_min, np.min(energy_values))
        global_energy_max = max(global_energy_max, np.max(energy_values))

    def plot_mdc(scan_index, mdc_energy):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 6))

        # Find the closest energy value to mdc_energy
        energy_index = np.argmin(np.abs(plot_data['energy_values'] - mdc_energy))
        actual_energy = plot_data['energy_values'][energy_index]

        # Extract MDC
        mdc = plot_data['data_values'][energy_index, :]
        k_parallel = plot_data['k_parallel'][energy_index, :]

        # Plot MDC
        ax.plot(k_parallel, mdc)
        ax.set_xlabel(r'$k_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDC at E = {actual_energy:.2f} eV - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        return fig

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'mdc_energy': interactive_plot.children[1].value
        }

        # Generate the plot with current values
        fig = plot_mdc(**current_values)

        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_energy_{current_values['mdc_energy']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   mdc_energy=FloatSlider(value=(global_energy_min + global_energy_max) / 2,
                                                          min=global_energy_min,
                                                          max=global_energy_max,
                                                          step=(global_energy_max - global_energy_min) / 100,
                                                          description='MDC Energy (eV)',
                                                          continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = mdc_plot(data_files, work_function)
display(interactive_plot_with_save)
