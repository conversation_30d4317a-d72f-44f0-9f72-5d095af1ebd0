# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar
        energy_values = -data.eV.values
        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': file_path
        })
        max_data_value = max(max_data_value, np.max(data.values))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    plot_data = all_plots[0]
    norm = Normalize(vmin=0, vmax=max_data_value)
    cmap = plt.cm.jet
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()

    def update_plot(scan_index, vmin, vmax, scale, 
                    vertical_enabled, horizontal_enabled,
                    vertical_magnitude, horizontal_magnitude, kernel_size):
        plot_data = all_plots[scan_index - 1]
        data_to_plot = plot_data['data_values'].copy()
        
        # Apply convolutions if enabled
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1],
                                            [-2, 0, 2],
                                            [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1],
                                              [0, 0, 0],
                                              [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)
            
            # Use TwoSlopeNorm for edge detection
            vmax_abs = max(abs(vmin), abs(vmax))
            norm = TwoSlopeNorm(vmin=-vmax_abs, vcenter=0, vmax=vmax_abs)
            cmap = plt.cm.RdBu_r
        else:
            # Use regular Normalize for original data
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = plt.cm.jet
        
        im.set_array(data_to_plot.ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
        fig.canvas.draw_idle()

    interactive_plot = interactive(
        update_plot,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
        vmin=FloatSlider(value=0, min=-max_data_value, max=max_data_value, step=max_data_value/100, description='Min Value', continuous_update=False),
        vmax=FloatSlider(value=max_data_value, min=0, max=max_data_value*2, step=max_data_value/100, description='Max Value', continuous_update=False),
        scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=9, step=2, description='Kernel Size', continuous_update=False)
    )

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: save_plot(b, interactive_plot, all_plots))

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(lambda b: make_video(b, interactive_plot, all_plots, data_files))

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])
    return output

def save_plot(b, interactive_plot, all_plots):
    current_values = {
        'scan_index': interactive_plot.children[0].value,
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'vertical_enabled': interactive_plot.children[4].value,
        'horizontal_enabled': interactive_plot.children[5].value,
        'vertical_magnitude': interactive_plot.children[6].value,
        'horizontal_magnitude': interactive_plot.children[7].value,
        'kernel_size': interactive_plot.children[8].value
    }
    fig = plot_arpes(current_values, all_plots)
    current_file = all_plots[current_values['scan_index'] - 1]['file_name']
    save_folder = os.path.dirname(current_file)
    filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
    fig.savefig(filename, dpi=2000, bbox_inches='tight')
    plt.close(fig)
    print(f"Plot saved as {filename}")

def plot_arpes(current_values, all_plots):
    plot_data = all_plots[current_values['scan_index'] - 1]
    fig, ax = plt.subplots(figsize=(10, 8))
    
    data_to_plot = plot_data['data_values'].copy()
    
    if current_values['vertical_enabled'] or current_values['horizontal_enabled']:
        if current_values['vertical_enabled']:
            vertical_kernel = np.array([[-1, 0, 1],
                                        [-2, 0, 2],
                                        [-1, 0, 1]]) * current_values['vertical_magnitude']
            if current_values['kernel_size'] > 3:
                vertical_kernel = np.repeat(np.repeat(vertical_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)
            data_to_plot = convolve2d(data_to_plot, vertical_kernel)
        
        if current_values['horizontal_enabled']:
            horizontal_kernel = np.array([[-1, -2, -1],
                                          [0, 0, 0],
                                          [1, 2, 1]]) * current_values['horizontal_magnitude']
            if current_values['kernel_size'] > 3:
                horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)
            data_to_plot = convolve2d(data_to_plot, horizontal_kernel)
        
        vmax_abs = max(abs(current_values['vmin']), abs(current_values['vmax']))
        norm = TwoSlopeNorm(vmin=-vmax_abs, vcenter=0, vmax=vmax_abs)
        cmap = plt.cm.RdBu_r
    else:
        norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
        cmap = plt.cm.jet
    
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], data_to_plot, shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()
    return fig

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    scan_types = determine_scan_type(data_files)
    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar
        energy_values = -data.eV.values
        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': file_path
        })
        max_data_value = max(max_data_value, np.max(data.values))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    plot_data = all_plots[0]
    norm = Normalize(vmin=0, vmax=max_data_value)
    cmap = plt.cm.jet
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()

    def update_plot(scan_index, vmin, vmax, scale, 
                    vertical_enabled, horizontal_enabled,
                    vertical_magnitude, horizontal_magnitude, kernel_size):
        plot_data = all_plots[scan_index - 1]
        data_to_plot = plot_data['data_values'].copy()
        
        # Apply convolutions if enabled
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1],
                                            [-2, 0, 2],
                                            [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1],
                                              [0, 0, 0],
                                              [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)
            
            # Use TwoSlopeNorm for edge detection
            vmax_abs = max(abs(vmin), abs(vmax))
            norm = TwoSlopeNorm(vmin=-vmax_abs, vcenter=0, vmax=vmax_abs)
            cmap = plt.cm.RdBu_r
        else:
            # Use regular Normalize for original data
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = plt.cm.jet
        
        im.set_array(data_to_plot.ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
        fig.canvas.draw_idle()

    interactive_plot = interactive(
        update_plot,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
        vmin=FloatSlider(value=0, min=-max_data_value, max=max_data_value, step=max_data_value/100, description='Min Value', continuous_update=False),
        vmax=FloatSlider(value=max_data_value, min=0, max=max_data_value*2, step=max_data_value/100, description='Max Value', continuous_update=False),
        scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False)
    )

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: save_plot(b, interactive_plot, all_plots))

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(lambda b: make_video(b, interactive_plot, all_plots, data_files))

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])
    return output

def save_plot(b, interactive_plot, all_plots):
    current_values = {
        'scan_index': interactive_plot.children[0].value,
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'vertical_enabled': interactive_plot.children[4].value,
        'horizontal_enabled': interactive_plot.children[5].value,
        'vertical_magnitude': interactive_plot.children[6].value,
        'horizontal_magnitude': interactive_plot.children[7].value,
        'kernel_size': interactive_plot.children[8].value
    }
    fig = plot_arpes(current_values, all_plots)
    current_file = all_plots[current_values['scan_index'] - 1]['file_name']
    save_folder = os.path.dirname(current_file)
    filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
    fig.savefig(filename, dpi=2000, bbox_inches='tight')
    plt.close(fig)
    print(f"Plot saved as {filename}")

def plot_arpes(current_values, all_plots):
    plot_data = all_plots[current_values['scan_index'] - 1]
    fig, ax = plt.subplots(figsize=(10, 8))
    
    data_to_plot = plot_data['data_values'].copy()
    
    if current_values['vertical_enabled'] or current_values['horizontal_enabled']:
        if current_values['vertical_enabled']:
            vertical_kernel = np.array([[-1, 0, 1],
                                        [-2, 0, 2],
                                        [-1, 0, 1]]) * current_values['vertical_magnitude']
            if current_values['kernel_size'] > 3:
                vertical_kernel = np.repeat(np.repeat(vertical_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)
            data_to_plot = convolve2d(data_to_plot, vertical_kernel)
        
        if current_values['horizontal_enabled']:
            horizontal_kernel = np.array([[-1, -2, -1],
                                          [0, 0, 0],
                                          [1, 2, 1]]) * current_values['horizontal_magnitude']
            if current_values['kernel_size'] > 3:
                horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)
            data_to_plot = convolve2d(data_to_plot, horizontal_kernel)
        
        vmax_abs = max(abs(current_values['vmin']), abs(current_values['vmax']))
        norm = TwoSlopeNorm(vmin=-vmax_abs, vcenter=0, vmax=vmax_abs)
        cmap = plt.cm.RdBu_r
    else:
        norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
        cmap = plt.cm.jet
    
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], data_to_plot, shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()
    return fig

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    scan_types = determine_scan_type(data_files)
    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog
get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def moving_average(data, window_size):
    kernel = np.ones((window_size, window_size)) / (window_size * window_size)
    return convolve2d(data, kernel)

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar
        energy_values = -data.eV.values
        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': file_path
        })
        max_data_value = max(max_data_value, np.max(data.values))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    plot_data = all_plots[0]
    norm = Normalize(vmin=0, vmax=max_data_value)
    cmap = plt.cm.jet
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()

    def update_plot(scan_index, vmin, vmax, scale, vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size, moving_average_enabled, moving_average_size):
        plot_data = all_plots[scan_index - 1]
        data_to_plot = plot_data['data_values'].copy()

        # Apply convolutions if enabled
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

        # Apply moving average if enabled
        if moving_average_enabled:
            data_to_plot = moving_average(data_to_plot, moving_average_size)

        # Use TwoSlopeNorm for edge detection
        if vertical_enabled or horizontal_enabled:
            vmax_abs = max(abs(vmin), abs(vmax))
            norm = TwoSlopeNorm(vmin=-vmax_abs, vcenter=0, vmax=vmax_abs)
            cmap = plt.cm.RdBu_r
        else:
            # Use regular Normalize for original data
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = plt.cm.jet

        im.set_array(data_to_plot.ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
        fig.canvas.draw_idle()

    interactive_plot = interactive(
        update_plot,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
        vmin=FloatSlider(value=0, min=-max_data_value, max=max_data_value, step=max_data_value/100, description='Min Value', continuous_update=False),
        vmax=FloatSlider(value=max_data_value, min=0, max=max_data_value*2, step=max_data_value/100, description='Max Value', continuous_update=False),
        scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),
        moving_average_enabled=Checkbox(value=False, description='Moving Average'),
        moving_average_size=IntSlider(value=3, min=3, max=20, step=2, description='Moving Average Size', continuous_update=False)
    )

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: save_plot(b, interactive_plot, all_plots))

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(lambda b: make_video(b, interactive_plot, all_plots, data_files))

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])
    return output

def save_plot(b, interactive_plot, all_plots):
    current_values = {
        'scan_index': interactive_plot.children[0].value,
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'vertical_enabled': interactive_plot.children[4].value,
        'horizontal_enabled': interactive_plot.children[5].value,
        'vertical_magnitude': interactive_plot.children[6].value,
        'horizontal_magnitude': interactive_plot.children[7].value,
        'kernel_size': interactive_plot.children[8].value,
        'moving_average_enabled': interactive_plot.children[9].value,
        'moving_average_size': interactive_plot.children[10].value
    }
    fig = plot_arpes(current_values, all_plots)
    current_file = all_plots[current_values['scan_index'] - 1]['file_name']
    save_folder = os.path.dirname(current_file)
    filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
    fig.savefig(filename, dpi=2000, bbox_inches='tight')
    plt.close(fig)
    print(f"Plot saved as {filename}")

def plot_arpes(current_values, all_plots):
    plot_data = all_plots[current_values['scan_index'] - 1]
    fig, ax = plt.subplots(figsize=(10, 8))
    data_to_plot = plot_data['data_values'].copy()

    if current_values['vertical_enabled'] or current_values['horizontal_enabled']:
        if current_values['vertical_enabled']:
            vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * current_values['vertical_magnitude']
            if current_values['kernel_size'] > 3:
                vertical_kernel = np.repeat(np.repeat(vertical_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)
            data_to_plot = convolve2d(data_to_plot, vertical_kernel)
        if current_values['horizontal_enabled']:
            horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * current_values['horizontal_magnitude']
            if current_values['kernel_size'] > 3:
                horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)
            data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

    if current_values['moving_average_enabled']:
        data_to_plot = moving_average(data_to_plot, current_values['moving_average_size'])

    if current_values['vertical_enabled'] or current_values['horizontal_enabled']:
        vmax_abs = max(abs(current_values['vmin']), abs(current_values['vmax']))
        norm = TwoSlopeNorm(vmin=-vmax_abs, vcenter=0, vmax=vmax_abs)
        cmap = plt.cm.RdBu_r
    else:
        norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
        cmap = plt.cm.jet

    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], data_to_plot, shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()
    return fig

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    scan_types = determine_scan_type(data_files)
    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
