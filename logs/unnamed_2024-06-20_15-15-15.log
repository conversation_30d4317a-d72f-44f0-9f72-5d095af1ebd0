# IPython log file

from arpes.io import load_data
#from arpes.endstations.plugin import *
import numpy as np
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.113": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\n#from arpes.endstations.plugin import *\nimport numpy as np\nif not hasattr(np, 'complex'):\n    np.complex = n\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')", 139)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.113.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.113.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.114": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\n#from arpes.endstations.plugin import *\nimport numpy as np\nif not hasattr(np, 'complex'):\n    np.complex = np.\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')", 141)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.114.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.114.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.115": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\n#from arpes.endstations.plugin import *\nimport numpy as np\nif not hasattr(np, 'complex'):\n    np.complex = np.c\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')", 142)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.115.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.115.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.io import load_data
#from arpes.endstations.plugin import *
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = np.complex128
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')
from arpes.io import load_data
#from arpes.endstations.plugin import *
import numpy as np
#if not hasattr(np, 'complex'):
    #np.complex = np.complex128
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')
