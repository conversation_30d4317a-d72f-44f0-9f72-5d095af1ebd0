# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Deja<PERSON><PERSON> Sans', 'Helve<PERSON>', 'Aria<PERSON>', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_energy_min = float('inf')
    global_energy_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global energy range
        global_energy_min = min(global_energy_min, np.min(energy_values))
        global_energy_max = max(global_energy_max, np.max(energy_values))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced energy indices
        energy_indices = np.linspace(0, len(plot_data['energy_values']) - 1, n, dtype=int)
        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, energy_index in enumerate(energy_indices):
            actual_energy = plot_data['energy_values'][energy_index]
            mdc = plot_data['data_values'][energy_index, :]
            k_parallel = plot_data['k_parallel'][energy_index, :]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(k_parallel, mdc + i * vertical_offset, label=f'E = {actual_energy:.2f} eV')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Split data into left and right sides
                left_mask = k_parallel < 0
                right_mask = k_parallel > 0

                # Fit left side
                if np.any(left_mask):
                    left_k = k_parallel[left_mask]
                    left_mdc = mdc[left_mask]
                    peaks, _ = find_peaks(left_mdc)
                    peak_heights = left_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(left_mdc))

                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'l{j+1}_center'].set(value=left_k[peak], min=left_k.min(), max=left_k.max())
                        params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                    result = model.fit(left_mdc, params, x=left_k)
                    fit = result.best_fit
                    ax.plot(left_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (left)', color=f'C{i}')

                # Fit right side
                if np.any(right_mask):
                    right_k = k_parallel[right_mask]
                    right_mdc = mdc[right_mask]
                    peaks, _ = find_peaks(right_mdc)
                    peak_heights = right_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(right_mdc))

                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'r{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'r{j+1}_center'].set(value=right_k[peak], min=right_k.min(), max=right_k.max())
                        params[f'r{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'r{j+1}_sigma'].set(value=0.1, min=0)

                    result = model.fit(right_mdc, params, x=right_k)
                    fit = result.best_fit
                    ax.plot(right_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (right)', color=f'C{i}')

        ax.set_xlabel(r'$k_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_mdc(**current_values)

        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_mdc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_energy_min = float('inf')
    global_energy_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global energy range
        global_energy_min = min(global_energy_min, np.min(energy_values))
        global_energy_max = max(global_energy_max, np.max(energy_values))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced energy indices
        energy_indices = np.linspace(0, len(plot_data['energy_values']) - 1, n, dtype=int)
        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, energy_index in enumerate(energy_indices):
            actual_energy = plot_data['energy_values'][energy_index]
            mdc = plot_data['data_values'][energy_index, :]
            k_parallel = plot_data['k_parallel'][energy_index, :]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(k_parallel, mdc + i * vertical_offset, label=f'E = {actual_energy:.2f} eV')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Split data into left and right sides
                left_mask = k_parallel < 0
                right_mask = k_parallel > 0

                # Fit left side
                if np.any(left_mask):
                    left_k = k_parallel[left_mask]
                    left_mdc = mdc[left_mask]
                    peaks, _ = find_peaks(left_mdc)
                    peak_heights = left_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(left_mdc))

                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'l{j+1}_center'].set(value=left_k[peak], min=left_k.min(), max=left_k.max())
                        params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                    result = model.fit(left_mdc, params, x=left_k)
                    fit = result.best_fit
                    ax.plot(left_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (left)', color=f'C{i}')

                # Fit right side
                if np.any(right_mask):
                    right_k = k_parallel[right_mask]
                    right_mdc = mdc[right_mask]
                    peaks, _ = find_peaks(right_mdc)
                    peak_heights = right_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(right_mdc))

                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'r{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'r{j+1}_center'].set(value=right_k[peak], min=right_k.min(), max=right_k.max())
                        params[f'r{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'r{j+1}_sigma'].set(value=0.1, min=0)

                    result = model.fit(right_mdc, params, x=right_k)
                    fit = result.best_fit
                    ax.plot(right_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (right)', color=f'C{i}')

        ax.set_xlabel(r'$k_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_mdc(**current_values)

        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_mdc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_energy_min = float('inf')
    global_energy_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global energy range
        global_energy_min = min(global_energy_min, np.min(energy_values))
        global_energy_max = max(global_energy_max, np.max(energy_values))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced energy indices
        energy_indices = np.linspace(0, len(plot_data['energy_values']) - 1, n, dtype=int)
        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, energy_index in enumerate(energy_indices):
            actual_energy = plot_data['energy_values'][energy_index]
            mdc = plot_data['data_values'][energy_index, :]
            k_parallel = plot_data['k_parallel'][energy_index, :]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(k_parallel, mdc + i * vertical_offset, label=f'E = {actual_energy:.2f} eV')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Split data into left and right sides
                left_mask = k_parallel < 0
                right_mask = k_parallel > 0

                # Fit left side
                if np.any(left_mask):
                    left_k = k_parallel[left_mask]
                    left_mdc = mdc[left_mask]
                    peaks, _ = find_peaks(left_mdc)
                    peak_heights = left_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(left_mdc))

                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'l{j+1}_center'].set(value=left_k[peak], min=left_k.min(), max=left_k.max())
                        params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                    result = model.fit(left_mdc, params, x=left_k)
                    fit = result.best_fit
                    ax.plot(left_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (left)', color=f'C{i}')

                # Fit right side
                if np.any(right_mask):
                    right_k = k_parallel[right_mask]
                    right_mdc = mdc[right_mask]
                    peaks, _ = find_peaks(right_mdc)
                    peak_heights = right_mdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(right_mdc))

                    for j, peak in enumerate(largest_peaks):
                        lorentzian = LorentzianModel(prefix=f'r{j+1}_')
                        model += lorentzian
                        params.update(lorentzian.make_params())
                        params[f'r{j+1}_center'].set(value=right_k[peak], min=right_k.min(), max=right_k.max())
                        params[f'r{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'r{j+1}_sigma'].set(value=0.1, min=0)

                    result = model.fit(right_mdc, params, x=right_k)
                    fit = result.best_fit
                    ax.plot(right_k, fit + i * vertical_offset, '--', label=f'Fit E = {actual_energy:.2f} eV (right)', color=f'C{i}')

        ax.set_xlabel(r'$k_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_mdc(**current_values)

        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_mdc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
