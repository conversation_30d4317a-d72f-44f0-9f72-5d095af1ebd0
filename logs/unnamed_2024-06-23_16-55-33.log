# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    plot_data = all_plots[0]
    norm = Normalize(vmin=0, vmax=max_data_value)
    cmap_array = plt.cm.jet(np.linspace(0, 1, 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()

    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
        norm = Normalize(vmin=vmin, vmax=vmax / scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
        im.set_array(plot_data['data_values'].ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        fig.canvas.draw_idle()

    interactive_plot = interactive(update_plot,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(make_video)

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])

    return output

def save_plot(b):
    # Get current slider values directly from the widgets
    current_values = {
        'scan_index': interactive_plot.children[0].value,
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Generate the plot with current values
    fig = plot_arpes(**current_values)

    # Save the plot
    filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
    fig.savefig(filename, dpi=2000, bbox_inches='tight')
    plt.close(fig)
    print(f"Plot saved as {filename}")

def make_video(b):
    # Get current slider values directly from the widgets
    current_values = {
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Determine scan types
    scan_types = determine_scan_type(data_files)

    # Create the video
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    plot_data = all_plots[0]
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')
    ims = []

    for i, (plot_data, (scan_type, scan_value)) in enumerate(zip(all_plots, scan_types)):
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],
                           shading='auto', cmap=cmap, norm=norm)
        if scan_type != 'unknown':
            text = ax.text(0.5, -0.1, f'{scan_type}: {scan_value:.2f}',  # Position text below the plot
                           transform=ax.transAxes, color='black',  # Set text color to black
                           horizontalalignment='center', verticalalignment='top',
                           fontsize=10, fontweight='bold')
            ims.append([im, text])
        else:
            ims.append([im])

    ani = animation.ArtistAnimation(fig, ims, interval=500, blit=True)
    ani.save('ARPES_video.mp4')
    plt.close(fig)
    print("Video saved as ARPES_video.mp4")

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    scan_types = determine_scan_type(data_files)
    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
