# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib.cbook import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'vmin': interactive_plot.children[1].value,
            'vmax': interactive_plot.children[2].value,
            'scale': interactive_plot.children[3].value,
            'cmap_start': interactive_plot.children[4].value,
            'cmap_end': interactive_plot.children[5].value
        }
        
        # Generate the plot with current values
        fig = plot_arpes(**current_values)
        
        # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")
    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output
# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = kspace(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'vmin': interactive_plot.children[1].value,
            'vmax': interactive_plot.children[2].value,
            'scale': interactive_plot.children[3].value,
            'cmap_start': interactive_plot.children[4].value,
            'cmap_end': interactive_plot.children[5].value
        }
        
        # Generate the plot with current values
        fig = plot_arpes(**current_values)
        
        # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")
    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output
# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = kspace(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
def kspace_and_mdc(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def plot_arpes_and_mdc(scan_index, vmin, vmax, scale, cmap_start, cmap_end, mdc_energy):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8), gridspec_kw={'width_ratios': [2, 1]})
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        # ARPES plot
        im = ax1.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                            shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax1)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax1.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax1.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax1.set_title(f'ARPES Data - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax1.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        
        # MDC plot
        energy_index = np.argmin(np.abs(plot_data['energy_values'] - mdc_energy))
        mdc = plot_data['data_values'][energy_index, :]
        ax2.plot(mdc, plot_data['k_parallel'])
        ax2.set_xlabel('Intensity', fontsize=12, fontweight='bold')
        ax2.set_ylabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax2.set_title(f'MDC at E = {mdc_energy:.2f} eV', fontsize=14, fontweight='bold')
        
        # Adjust tick label font size
        ax1.tick_params(axis='both', which='major', labelsize=10)
        ax2.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'vmin': interactive_plot.children[1].value,
            'vmax': interactive_plot.children[2].value,
            'scale': interactive_plot.children[3].value,
            'cmap_start': interactive_plot.children[4].value,
            'cmap_end': interactive_plot.children[5].value,
            'mdc_energy': interactive_plot.children[6].value
        }
        
        # Generate the plot with current values
        fig = plot_arpes_and_mdc(**current_values)
        
        # Save the plot
        filename = f"ARPES_MDC_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes_and_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True),
                                   mdc_energy=FloatSlider(value=0.0, min=-10, max=10, step=0.1, description='MDC Energy (eV)', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = kspace_and_mdc(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
def kspace_and_mdc(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def mdc_plot(data_files, work_function):
        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg

        # Pre-calculate all plots
        all_plots = []

        for file_path in data_files:
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']  # Photon energy from the attributes

            # Calculate kinetic energy and momentum
            E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
            k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

            # Get the energy values
            energy_values = -data.eV.values  # Negative energy values

            all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': os.path.basename(file_path)
            })

        def plot_mdc(scan_index, mdc_energy):
            plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

            fig, ax = plt.subplots(figsize=(10, 8))
        
            # Find the closest energy value to mdc_energy
            energy_index = np.argmin(np.abs(plot_data['energy_values'] - mdc_energy))
            mdc = plot_data['data_values'][:, energy_index]
            
            # Plot MDC
            ax.plot(plot_data['k_parallel'], mdc)
            ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
            ax.set_ylabel('Intensity', fontsize=12, fontweight='bold')
            ax.set_title(f'MDC at E = {mdc_energy:.2f} eV - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
            
            # Adjust tick label font size
            ax.tick_params(axis='both', which='major', labelsize=10)
            
            plt.tight_layout()
            return fig

        def save_plot(b):
            # Get current slider values directly from the widgets
            current_values = {
                'scan_index': interactive_plot.children[0].value,
                'mdc_energy': interactive_plot.children[1].value
            }
            
            # Generate the plot with current values
            fig = plot_mdc(**current_values)
            
            # Save the plot
            filename = f"MDC_plot_scan_{current_values['scan_index']}_energy_{current_values['mdc_energy']:.2f}.png"
            fig.savefig(filename, dpi=2000, bbox_inches='tight')
            plt.close(fig)
            print(f"Plot saved as {filename}")

        # Create the interactive widget
        interactive_plot = interactive(plot_mdc,
                                    scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                    mdc_energy=FloatSlider(value=0.0, min=-10, max=10, step=0.1, description='MDC Energy (eV)', continuous_update=True))

        # Create a save button
        save_button = Button(description="Save Plot")
        save_button.on_click(save_plot)

        # Combine the interactive plot and the save button
        output = VBox([interactive_plot, save_button])
        return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = mdc_plot(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

    def plot_mdc(scan_index, mdc_energy):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Find the closest energy value to mdc_energy
        energy_index = np.argmin(np.abs(plot_data['energy_values'] - mdc_energy))
        mdc = plot_data['data_values'][:, energy_index]
        
        # Plot MDC
        ax.plot(plot_data['k_parallel'], mdc)
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity', fontsize=12, fontweight='bold')
        ax.set_title(f'MDC at E = {mdc_energy:.2f} eV - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'mdc_energy': interactive_plot.children[1].value
        }
        
        # Generate the plot with current values
        fig = plot_mdc(**current_values)
        
        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_energy_{current_values['mdc_energy']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   mdc_energy=FloatSlider(value=0.0, min=-10, max=10, step=0.1, description='MDC Energy (eV)', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = mdc_plot(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

    def plot_mdc(scan_index, mdc_energy):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))

        # Print shapes for debugging
        print(f"energy_values shape: {plot_data['energy_values'].shape}")
        print(f"k_parallel shape: {plot_data['k_parallel'].shape}")
        print(f"data_values shape: {plot_data['data_values'].shape}")

        # Find the closest energy value to mdc_energy
        energy_index = np.argmin(np.abs(plot_data['energy_values'] - mdc_energy))
        
        # Adjust indexing based on the shape of data_values
        if plot_data['data_values'].shape[0] == len(plot_data['energy_values']):
            mdc = plot_data['data_values'][energy_index, :]
        else:
            mdc = plot_data['data_values'][:, energy_index]

        # Plot MDC
        ax.plot(plot_data['k_parallel'], mdc)
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity', fontsize=12, fontweight='bold')
        ax.set_title(f'MDC at E = {mdc_energy:.2f} eV - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'mdc_energy': interactive_plot.children[1].value
        }
        
        # Generate the plot with current values
        fig = plot_mdc(**current_values)
        
        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_energy_{current_values['mdc_energy']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   mdc_energy=FloatSlider(value=0.0, min=-10, max=10, step=0.1, description='MDC Energy (eV)', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = mdc_plot(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

    def plot_mdc(scan_index, mdc_energy):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))

        # Print shapes for debugging
        print(f"energy_values shape: {plot_data['energy_values'].shape}")
        print(f"k_parallel shape: {plot_data['k_parallel'].shape}")
        print(f"data_values shape: {plot_data['data_values'].shape}")

        # Find the closest energy value to mdc_energy
        energy_index = np.argmin(np.abs(plot_data['energy_values'] - mdc_energy))
        
        # Extract MDC
        mdc = plot_data['data_values'][:, energy_index]

        # Extract k_parallel for the specific energy
        k_parallel = plot_data['k_parallel'][:, energy_index]

        # Plot MDC
        ax.plot(k_parallel, mdc)
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity', fontsize=12, fontweight='bold')
        ax.set_title(f'MDC at E = {mdc_energy:.2f} eV - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig
    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'mdc_energy': interactive_plot.children[1].value
        }
        
        # Generate the plot with current values
        fig = plot_mdc(**current_values)
        
        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_energy_{current_values['mdc_energy']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   mdc_energy=FloatSlider(value=0.0, min=-10, max=10, step=0.1, description='MDC Energy (eV)', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = mdc_plot(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

    def plot_mdc(scan_index, mdc_energy):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))

        # Print shapes and ranges for debugging
        print(f"energy_values shape: {plot_data['energy_values'].shape}")
        print(f"energy_values range: {plot_data['energy_values'].min():.2f} to {plot_data['energy_values'].max():.2f}")
        print(f"k_parallel shape: {plot_data['k_parallel'].shape}")
        print(f"data_values shape: {plot_data['data_values'].shape}")
        print(f"Requested MDC energy: {mdc_energy:.2f}")

        # Find the closest energy value to mdc_energy
        energy_index = np.argmin(np.abs(plot_data['energy_values'] - mdc_energy))
        print(f"Closest energy index: {energy_index}")
        print(f"Closest energy value: {plot_data['energy_values'][energy_index]:.2f}")

        # Extract MDC
        if plot_data['data_values'].shape[1] == len(plot_data['energy_values']):
            mdc = plot_data['data_values'][:, energy_index]
        else:
            mdc = plot_data['data_values'][energy_index, :]

        # Extract k_parallel
        if plot_data['k_parallel'].ndim == 2:
            k_parallel = plot_data['k_parallel'][:, 0]  # Assuming k_parallel is the same for all energies
        else:
            k_parallel = plot_data['k_parallel']

        # Plot MDC
        ax.plot(k_parallel, mdc)
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity', fontsize=12, fontweight='bold')
        ax.set_title(f'MDC at E = {plot_data["energy_values"][energy_index]:.2f} eV - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig
    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'mdc_energy': interactive_plot.children[1].value
        }
        
        # Generate the plot with current values
        fig = plot_mdc(**current_values)
        
        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_energy_{current_values['mdc_energy']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   mdc_energy=FloatSlider(value=0.0, min=-10, max=10, step=0.1, description='MDC Energy (eV)', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = mdc_plot(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'vmin': interactive_plot.children[1].value,
            'vmax': interactive_plot.children[2].value,
            'scale': interactive_plot.children[3].value,
            'cmap_start': interactive_plot.children[4].value,
            'cmap_end': interactive_plot.children[5].value
        }
        
        # Generate the plot with current values
        fig = plot_arpes(**current_values)
        
        # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")
    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output
# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = kspace(data_files, work_function)
display(interactive_plot_with_save)
mport os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

    def plot_mdc(scan_index, mdc_energy):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 6))

        # Find the closest energy value to mdc_energy
        energy_index = np.argmin(np.abs(plot_data['energy_values'] - mdc_energy))
        actual_energy = plot_data['energy_values'][energy_index]

        # Extract MDC
        mdc = plot_data['data_values'][energy_index, :]
        k_parallel = plot_data['k_parallel'][energy_index, :]

        # Plot MDC
        ax.plot(k_parallel, mdc)
        ax.set_xlabel(r'$k_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDC at E = {actual_energy:.2f} eV - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        return fig

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'mdc_energy': interactive_plot.children[1].value
        }

        # Generate the plot with current values
        fig = plot_mdc(**current_values)

        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_energy_{current_values['mdc_energy']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   mdc_energy=FloatSlider(value=0.0, min=-10, max=10, step=0.1, description='MDC Energy (eV)', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = mdc_plot(data_files, work_function)
display(interactive_plot_with_save)
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.0": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\n# Suppress specific warnings\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef mdc_plot(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']  # Photon energy from the attributes\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n    def plot_mdc(scan_index, mdc_energy):\n        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n\n        fig, ax = plt.subplots(figsize=(10, 6))\n\n        # Find the closest energy value to mdc_energy\n        energy_index = np.argmin(np.abs(plot_data['energy_values'] - mdc_energy))\n        actual_energy = plot_data['energy_values'][energy_index]\n\n        # Extract MDC\n        mdc = plot_data['data_values'][energy_index, :]\n        k_parallel = plot_data['k_parallel'][energy_index, :]\n\n        # Plot MDC\n        ax.plot(k_parallel, mdc)\n        ax.set_xlabel(r'$k_\\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')\n        ax.set_title(f'MDC at E = {actual_energy:.2f} eV - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n\n        # Adjust tick label font size\n        ax.tick_params(axis='both', which='major', labelsize=10)\n\n        plt.tight_layout()\n        return fig\n\n    def save_plot(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'mdc_energy': interactive_plot.children[1].value\n        }\n\n        # Generate the plot with current values\n        fig = plot_mdc(**current_values)\n\n        # Save the plot\n        filename = f\"MDC_plot_scan_{current_values['scan_index']}_energy_{current_values['mdc_energy']:.2f}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        plt.close(fig)\n        print(f\"Plot saved as {filename}\")\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_mdc,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),\n                                   mdc_energy=FloatSlider(value=0.0, min=-10, max=10, step=0.1, description='MDC Energy (eV)', continuous_update=True))\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    # Combine the interactive plot and the save button\n    output = VBox([interactive_plot, save_button])\n    return output\n\n# Usage\nfolder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\ndata_files = load_data_files(folder_path)\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\ninteractive_plot_with_save = mdc_plot(data_files, work_function)\ndisplay(interactive_plot_with_save)", 1)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.0.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.0.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

    def plot_mdc(scan_index, mdc_energy):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 6))

        # Find the closest energy value to mdc_energy
        energy_index = np.argmin(np.abs(plot_data['energy_values'] - mdc_energy))
        actual_energy = plot_data['energy_values'][energy_index]

        # Extract MDC
        mdc = plot_data['data_values'][energy_index, :]
        k_parallel = plot_data['k_parallel'][energy_index, :]

        # Plot MDC
        ax.plot(k_parallel, mdc)
        ax.set_xlabel(r'$k_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDC at E = {actual_energy:.2f} eV - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        return fig

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'mdc_energy': interactive_plot.children[1].value
        }

        # Generate the plot with current values
        fig = plot_mdc(**current_values)

        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_energy_{current_values['mdc_energy']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   mdc_energy=FloatSlider(value=0.0, min=-10, max=10, step=0.1, description='MDC Energy (eV)', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = mdc_plot(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_energy_min = float('inf')
    global_energy_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global energy range
        global_energy_min = min(global_energy_min, np.min(energy_values))
        global_energy_max = max(global_energy_max, np.max(energy_values))

    def plot_mdc(scan_index, mdc_energy):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 6))

        # Find the closest energy value to mdc_energy
        energy_index = np.argmin(np.abs(plot_data['energy_values'] - mdc_energy))
        actual_energy = plot_data['energy_values'][energy_index]

        # Extract MDC
        mdc = plot_data['data_values'][energy_index, :]
        k_parallel = plot_data['k_parallel'][energy_index, :]

        # Plot MDC
        ax.plot(k_parallel, mdc)
        ax.set_xlabel(r'$k_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDC at E = {actual_energy:.2f} eV - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        return fig

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'mdc_energy': interactive_plot.children[1].value
        }

        # Generate the plot with current values
        fig = plot_mdc(**current_values)

        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_energy_{current_values['mdc_energy']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   mdc_energy=FloatSlider(value=(global_energy_min + global_energy_max) / 2,
                                                          min=global_energy_min,
                                                          max=global_energy_max,
                                                          step=(global_energy_max - global_energy_min) / 100,
                                                          description='MDC Energy (eV)',
                                                          continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = mdc_plot(data_files, work_function)
display(interactive_plot_with_save)
