# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None

    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))

    return scan_types

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))

        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)

        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)

        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)

        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        return fig

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'vmin': interactive_plot.children[1].value,
            'vmax': interactive_plot.children[2].value,
            'scale': interactive_plot.children[3].value,
            'cmap_start': interactive_plot.children[4].value,
            'cmap_end': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        fig = plot_arpes(**current_values)

        # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    def make_video(b):
        # Get current slider values directly from the widgets
        current_values = {
            'vmin': interactive_plot.children[1].value,
            'vmax': interactive_plot.children[2].value,
            'scale': interactive_plot.children[3].value,
            'cmap_start': interactive_plot.children[4].value,
            'cmap_end': interactive_plot.children[5].value
        }

        # Determine scan types
        scan_types = determine_scan_type(data_files)

        # Create the video
        fig, ax = plt.subplots(figsize=(10, 8))

        # Create a custom normalization
        norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax']/current_values['scale'])

        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
        cmap = ListedColormap(cmap_array)

        # Initialize the plot with the first dataset
        plot_data = all_plots[0]
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)

        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')

        ims = []

        for i, (plot_data, (scan_type, scan_value)) in enumerate(zip(all_plots, scan_types)):
            im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],
                               shading='auto', cmap=cmap, norm=norm)

            # Add text for scan type and value
            if scan_type != 'unknown':
                text = ax.text(0.5, -0.1, f'{scan_type}: {scan_value:.2f}',  # Position text below the plot
                               transform=ax.transAxes, color='black',  # Set text color to black
                               horizontalalignment='center', verticalalignment='top',
                               fontsize=10, fontweight='bold')
                ims.append([im, text])
            else:
                ims.append([im])

        ani = animation.ArtistAnimation(fig, ims, interval=500, blit=True)
        ani.save('ARPES_video.mp4')
        plt.close(fig)
        print("Video saved as ARPES_video.mp4")

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(make_video)

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

    # Determine scan types
    scan_types = determine_scan_type(data_files)

    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.0": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display\nimport matplotlib.animation as animation\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\nimport tkinter as tk\nfrom tkinter import filedialog\n%\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",  # Set default text color to black\n    \"axes.labelcolor\": \"black\",  # Set default axes label color to black\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\n\n# Suppress specific warnings\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef determine_scan_type(data_files):\n    scan_types = []\n    previous_polar = None\n    previous_hv = None\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        if 'polar' in data.attrs and 'hv' in data.attrs:\n            current_polar = data.attrs['polar']\n            current_hv = data.attrs['hv']\n            if previous_polar is not None and previous_hv is not None:\n                if current_polar != previous_polar:\n                    scan_types.append(('polar', current_polar))\n                elif current_hv != previous_hv:\n                    scan_types.append(('hv', current_hv))\n                else:\n                    scan_types.append(('unknown', None))\n            else:\n                scan_types.append(('unknown', None))\n            previous_polar = current_polar\n            previous_hv = current_hv\n        else:\n            scan_types.append(('unknown', None))\n\n    return scan_types\n\ndef kspace(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    max_data_value = 0\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']  # Photon energy from the attributes\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        max_data_value = max(max_data_value, np.max(data.values))\n\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n\n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n\n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n\n        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                           shading='auto', cmap=cmap, norm=norm)\n        cbar = fig.colorbar(im, ax=ax)\n        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n        cbar.ax.tick_params(labelsize=10)\n\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits\n\n        # Adjust tick label font size\n        ax.tick_params(axis='both', which='major', labelsize=10)\n\n        plt.tight_layout()\n        return fig\n\n    def save_plot(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'vmin': interactive_plot.children[1].value,\n            'vmax': interactive_plot.children[2].value,\n            'scale': interactive_plot.children[3].value,\n            'cmap_start': interactive_plot.children[4].value,\n            'cmap_end': interactive_plot.children[5].value\n        }\n\n        # Generate the plot with current values\n        fig = plot_arpes(**current_values)\n\n        # Save the plot\n        filename = f\"ARPES_plot_scan_{current_values['scan_index']}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        plt.close(fig)\n        print(f\"Plot saved as {filename}\")\n\n    def make_video(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'vmin': interactive_plot.children[1].value,\n            'vmax': interactive_plot.children[2].value,\n            'scale': interactive_plot.children[3].value,\n            'cmap_start': interactive_plot.children[4].value,\n            'cmap_end': interactive_plot.children[5].value\n        }\n\n        # Determine scan types\n        scan_types = determine_scan_type(data_files)\n\n        # Create the video\n        fig, ax = plt.subplots(figsize=(10, 8))\n\n        # Create a custom normalization\n        norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax']/current_values['scale'])\n\n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))\n        cmap = ListedColormap(cmap_array)\n\n        # Initialize the plot with the first dataset\n        plot_data = all_plots[0]\n        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                           shading='auto', cmap=cmap, norm=norm)\n        cbar = fig.colorbar(im, ax=ax)\n        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n        cbar.ax.tick_params(labelsize=10)\n\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n        ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')\n\n        ims = []\n\n        for i, (plot_data, (scan_type, scan_value)) in enumerate(zip(all_plots, scan_types)):\n            im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                               shading='auto', cmap=cmap, norm=norm)\n\n            # Add text for scan type and value\n            if scan_type != 'unknown':\n                text = ax.text(0.5, -0.1, f'{scan_type}: {scan_value:.2f}',  # Position text below the plot\n                               transform=ax.transAxes, color='black',  # Set text color to black\n                               horizontalalignment='center', verticalalignment='top',\n                               fontsize=10, fontweight='bold')\n                ims.append([im, text])\n            else:\n                ims.append([im])\n\n        ani = animation.ArtistAnimation(fig, ims, interval=500, blit=True)\n        ani.save('ARPES_video.mp4')\n        plt.close(fig)\n        print(\"Video saved as ARPES_video.mp4\")\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),\n                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    # Create a video button\n    video_button = Button(description=\"Make Video\")\n    video_button.on_click(make_video)\n\n    # Combine the interactive plot, save button, and video button\n    output = VBox([interactive_plot, save_button, video_button])\n\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()  # Hide the root window\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()  # Destroy the root window\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n    # Determine scan types\n    scan_types = determine_scan_type(data_files)\n\n    interactive_plot_with_save = kspace(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")", 525)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.0.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.0.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.1": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display\nimport matplotlib.animation as animation\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\nimport tkinter as tk\nfrom tkinter import filedialog\n%matplotlib t\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",  # Set default text color to black\n    \"axes.labelcolor\": \"black\",  # Set default axes label color to black\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\n\n# Suppress specific warnings\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef determine_scan_type(data_files):\n    scan_types = []\n    previous_polar = None\n    previous_hv = None\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        if 'polar' in data.attrs and 'hv' in data.attrs:\n            current_polar = data.attrs['polar']\n            current_hv = data.attrs['hv']\n            if previous_polar is not None and previous_hv is not None:\n                if current_polar != previous_polar:\n                    scan_types.append(('polar', current_polar))\n                elif current_hv != previous_hv:\n                    scan_types.append(('hv', current_hv))\n                else:\n                    scan_types.append(('unknown', None))\n            else:\n                scan_types.append(('unknown', None))\n            previous_polar = current_polar\n            previous_hv = current_hv\n        else:\n            scan_types.append(('unknown', None))\n\n    return scan_types\n\ndef kspace(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    max_data_value = 0\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']  # Photon energy from the attributes\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        max_data_value = max(max_data_value, np.max(data.values))\n\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n\n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n\n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n\n        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                           shading='auto', cmap=cmap, norm=norm)\n        cbar = fig.colorbar(im, ax=ax)\n        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n        cbar.ax.tick_params(labelsize=10)\n\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits\n\n        # Adjust tick label font size\n        ax.tick_params(axis='both', which='major', labelsize=10)\n\n        plt.tight_layout()\n        return fig\n\n    def save_plot(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'vmin': interactive_plot.children[1].value,\n            'vmax': interactive_plot.children[2].value,\n            'scale': interactive_plot.children[3].value,\n            'cmap_start': interactive_plot.children[4].value,\n            'cmap_end': interactive_plot.children[5].value\n        }\n\n        # Generate the plot with current values\n        fig = plot_arpes(**current_values)\n\n        # Save the plot\n        filename = f\"ARPES_plot_scan_{current_values['scan_index']}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        plt.close(fig)\n        print(f\"Plot saved as {filename}\")\n\n    def make_video(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'vmin': interactive_plot.children[1].value,\n            'vmax': interactive_plot.children[2].value,\n            'scale': interactive_plot.children[3].value,\n            'cmap_start': interactive_plot.children[4].value,\n            'cmap_end': interactive_plot.children[5].value\n        }\n\n        # Determine scan types\n        scan_types = determine_scan_type(data_files)\n\n        # Create the video\n        fig, ax = plt.subplots(figsize=(10, 8))\n\n        # Create a custom normalization\n        norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax']/current_values['scale'])\n\n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))\n        cmap = ListedColormap(cmap_array)\n\n        # Initialize the plot with the first dataset\n        plot_data = all_plots[0]\n        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                           shading='auto', cmap=cmap, norm=norm)\n        cbar = fig.colorbar(im, ax=ax)\n        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n        cbar.ax.tick_params(labelsize=10)\n\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n        ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')\n\n        ims = []\n\n        for i, (plot_data, (scan_type, scan_value)) in enumerate(zip(all_plots, scan_types)):\n            im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                               shading='auto', cmap=cmap, norm=norm)\n\n            # Add text for scan type and value\n            if scan_type != 'unknown':\n                text = ax.text(0.5, -0.1, f'{scan_type}: {scan_value:.2f}',  # Position text below the plot\n                               transform=ax.transAxes, color='black',  # Set text color to black\n                               horizontalalignment='center', verticalalignment='top',\n                               fontsize=10, fontweight='bold')\n                ims.append([im, text])\n            else:\n                ims.append([im])\n\n        ani = animation.ArtistAnimation(fig, ims, interval=500, blit=True)\n        ani.save('ARPES_video.mp4')\n        plt.close(fig)\n        print(\"Video saved as ARPES_video.mp4\")\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),\n                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    # Create a video button\n    video_button = Button(description=\"Make Video\")\n    video_button.on_click(make_video)\n\n    # Combine the interactive plot, save button, and video button\n    output = VBox([interactive_plot, save_button, video_button])\n\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()  # Hide the root window\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()  # Destroy the root window\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n    # Determine scan types\n    scan_types = determine_scan_type(data_files)\n\n    interactive_plot_with_save = kspace(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")", 537)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.1.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.1.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.2": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("import os\nimtk", 14, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.2.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.2.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.3": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display\nimport matplotlib.animation as animation\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\nimport tkinter as tk\nfrom tkinter import filedialog\n%matplotlib w\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",  # Set default text color to black\n    \"axes.labelcolor\": \"black\",  # Set default axes label color to black\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\n\n# Suppress specific warnings\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef determine_scan_type(data_files):\n    scan_types = []\n    previous_polar = None\n    previous_hv = None\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        if 'polar' in data.attrs and 'hv' in data.attrs:\n            current_polar = data.attrs['polar']\n            current_hv = data.attrs['hv']\n            if previous_polar is not None and previous_hv is not None:\n                if current_polar != previous_polar:\n                    scan_types.append(('polar', current_polar))\n                elif current_hv != previous_hv:\n                    scan_types.append(('hv', current_hv))\n                else:\n                    scan_types.append(('unknown', None))\n            else:\n                scan_types.append(('unknown', None))\n            previous_polar = current_polar\n            previous_hv = current_hv\n        else:\n            scan_types.append(('unknown', None))\n\n    return scan_types\n\ndef kspace(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    max_data_value = 0\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']  # Photon energy from the attributes\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        max_data_value = max(max_data_value, np.max(data.values))\n\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n\n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n\n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n\n        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                           shading='auto', cmap=cmap, norm=norm)\n        cbar = fig.colorbar(im, ax=ax)\n        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n        cbar.ax.tick_params(labelsize=10)\n\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits\n\n        # Adjust tick label font size\n        ax.tick_params(axis='both', which='major', labelsize=10)\n\n        plt.tight_layout()\n        return fig\n\n    def save_plot(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'vmin': interactive_plot.children[1].value,\n            'vmax': interactive_plot.children[2].value,\n            'scale': interactive_plot.children[3].value,\n            'cmap_start': interactive_plot.children[4].value,\n            'cmap_end': interactive_plot.children[5].value\n        }\n\n        # Generate the plot with current values\n        fig = plot_arpes(**current_values)\n\n        # Save the plot\n        filename = f\"ARPES_plot_scan_{current_values['scan_index']}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        plt.close(fig)\n        print(f\"Plot saved as {filename}\")\n\n    def make_video(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'vmin': interactive_plot.children[1].value,\n            'vmax': interactive_plot.children[2].value,\n            'scale': interactive_plot.children[3].value,\n            'cmap_start': interactive_plot.children[4].value,\n            'cmap_end': interactive_plot.children[5].value\n        }\n\n        # Determine scan types\n        scan_types = determine_scan_type(data_files)\n\n        # Create the video\n        fig, ax = plt.subplots(figsize=(10, 8))\n\n        # Create a custom normalization\n        norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax']/current_values['scale'])\n\n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))\n        cmap = ListedColormap(cmap_array)\n\n        # Initialize the plot with the first dataset\n        plot_data = all_plots[0]\n        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                           shading='auto', cmap=cmap, norm=norm)\n        cbar = fig.colorbar(im, ax=ax)\n        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n        cbar.ax.tick_params(labelsize=10)\n\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n        ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')\n\n        ims = []\n\n        for i, (plot_data, (scan_type, scan_value)) in enumerate(zip(all_plots, scan_types)):\n            im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                               shading='auto', cmap=cmap, norm=norm)\n\n            # Add text for scan type and value\n            if scan_type != 'unknown':\n                text = ax.text(0.5, -0.1, f'{scan_type}: {scan_value:.2f}',  # Position text below the plot\n                               transform=ax.transAxes, color='black',  # Set text color to black\n                               horizontalalignment='center', verticalalignment='top',\n                               fontsize=10, fontweight='bold')\n                ims.append([im, text])\n            else:\n                ims.append([im])\n\n        ani = animation.ArtistAnimation(fig, ims, interval=500, blit=True)\n        ani.save('ARPES_video.mp4')\n        plt.close(fig)\n        print(\"Video saved as ARPES_video.mp4\")\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),\n                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    # Create a video button\n    video_button = Button(description=\"Make Video\")\n    video_button.on_click(make_video)\n\n    # Combine the interactive plot, save button, and video button\n    output = VBox([interactive_plot, save_button, video_button])\n\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()  # Hide the root window\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()  # Destroy the root window\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n    # Determine scan types\n    scan_types = determine_scan_type(data_files)\n\n    interactive_plot_with_save = kspace(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")", 537)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.3.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.3.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.4": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("import os\nimwarnings", 20, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.4.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.4.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.5": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("import os\nimwith", 16, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.5.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.5.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.6": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display\nimport matplotlib.animation as animation\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\nimport tkinter as tk\nfrom tkinter import filedialog\n%matplotlib wid\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",  # Set default text color to black\n    \"axes.labelcolor\": \"black\",  # Set default axes label color to black\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\n\n# Suppress specific warnings\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef determine_scan_type(data_files):\n    scan_types = []\n    previous_polar = None\n    previous_hv = None\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        if 'polar' in data.attrs and 'hv' in data.attrs:\n            current_polar = data.attrs['polar']\n            current_hv = data.attrs['hv']\n            if previous_polar is not None and previous_hv is not None:\n                if current_polar != previous_polar:\n                    scan_types.append(('polar', current_polar))\n                elif current_hv != previous_hv:\n                    scan_types.append(('hv', current_hv))\n                else:\n                    scan_types.append(('unknown', None))\n            else:\n                scan_types.append(('unknown', None))\n            previous_polar = current_polar\n            previous_hv = current_hv\n        else:\n            scan_types.append(('unknown', None))\n\n    return scan_types\n\ndef kspace(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    max_data_value = 0\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']  # Photon energy from the attributes\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        max_data_value = max(max_data_value, np.max(data.values))\n\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n\n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n\n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n\n        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                           shading='auto', cmap=cmap, norm=norm)\n        cbar = fig.colorbar(im, ax=ax)\n        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n        cbar.ax.tick_params(labelsize=10)\n\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits\n\n        # Adjust tick label font size\n        ax.tick_params(axis='both', which='major', labelsize=10)\n\n        plt.tight_layout()\n        return fig\n\n    def save_plot(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'vmin': interactive_plot.children[1].value,\n            'vmax': interactive_plot.children[2].value,\n            'scale': interactive_plot.children[3].value,\n            'cmap_start': interactive_plot.children[4].value,\n            'cmap_end': interactive_plot.children[5].value\n        }\n\n        # Generate the plot with current values\n        fig = plot_arpes(**current_values)\n\n        # Save the plot\n        filename = f\"ARPES_plot_scan_{current_values['scan_index']}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        plt.close(fig)\n        print(f\"Plot saved as {filename}\")\n\n    def make_video(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'vmin': interactive_plot.children[1].value,\n            'vmax': interactive_plot.children[2].value,\n            'scale': interactive_plot.children[3].value,\n            'cmap_start': interactive_plot.children[4].value,\n            'cmap_end': interactive_plot.children[5].value\n        }\n\n        # Determine scan types\n        scan_types = determine_scan_type(data_files)\n\n        # Create the video\n        fig, ax = plt.subplots(figsize=(10, 8))\n\n        # Create a custom normalization\n        norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax']/current_values['scale'])\n\n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))\n        cmap = ListedColormap(cmap_array)\n\n        # Initialize the plot with the first dataset\n        plot_data = all_plots[0]\n        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                           shading='auto', cmap=cmap, norm=norm)\n        cbar = fig.colorbar(im, ax=ax)\n        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n        cbar.ax.tick_params(labelsize=10)\n\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n        ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')\n\n        ims = []\n\n        for i, (plot_data, (scan_type, scan_value)) in enumerate(zip(all_plots, scan_types)):\n            im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                               shading='auto', cmap=cmap, norm=norm)\n\n            # Add text for scan type and value\n            if scan_type != 'unknown':\n                text = ax.text(0.5, -0.1, f'{scan_type}: {scan_value:.2f}',  # Position text below the plot\n                               transform=ax.transAxes, color='black',  # Set text color to black\n                               horizontalalignment='center', verticalalignment='top',\n                               fontsize=10, fontweight='bold')\n                ims.append([im, text])\n            else:\n                ims.append([im])\n\n        ani = animation.ArtistAnimation(fig, ims, interval=500, blit=True)\n        ani.save('ARPES_video.mp4')\n        plt.close(fig)\n        print(\"Video saved as ARPES_video.mp4\")\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),\n                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    # Create a video button\n    video_button = Button(description=\"Make Video\")\n    video_button.on_click(make_video)\n\n    # Combine the interactive plot, save button, and video button\n    output = VBox([interactive_plot, save_button, video_button])\n\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()  # Hide the root window\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()  # Destroy the root window\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n    # Determine scan types\n    scan_types = determine_scan_type(data_files)\n\n    interactive_plot_with_save = kspace(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")", 539)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.6.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.6.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.7": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display\nimport matplotlib.animation as animation\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\nimport tkinter as tk\nfrom tkinter import filedialog\n%matplotlib widg\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",  # Set default text color to black\n    \"axes.labelcolor\": \"black\",  # Set default axes label color to black\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\n\n# Suppress specific warnings\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef determine_scan_type(data_files):\n    scan_types = []\n    previous_polar = None\n    previous_hv = None\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        if 'polar' in data.attrs and 'hv' in data.attrs:\n            current_polar = data.attrs['polar']\n            current_hv = data.attrs['hv']\n            if previous_polar is not None and previous_hv is not None:\n                if current_polar != previous_polar:\n                    scan_types.append(('polar', current_polar))\n                elif current_hv != previous_hv:\n                    scan_types.append(('hv', current_hv))\n                else:\n                    scan_types.append(('unknown', None))\n            else:\n                scan_types.append(('unknown', None))\n            previous_polar = current_polar\n            previous_hv = current_hv\n        else:\n            scan_types.append(('unknown', None))\n\n    return scan_types\n\ndef kspace(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    max_data_value = 0\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']  # Photon energy from the attributes\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        max_data_value = max(max_data_value, np.max(data.values))\n\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n\n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n\n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n\n        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                           shading='auto', cmap=cmap, norm=norm)\n        cbar = fig.colorbar(im, ax=ax)\n        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n        cbar.ax.tick_params(labelsize=10)\n\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits\n\n        # Adjust tick label font size\n        ax.tick_params(axis='both', which='major', labelsize=10)\n\n        plt.tight_layout()\n        return fig\n\n    def save_plot(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'vmin': interactive_plot.children[1].value,\n            'vmax': interactive_plot.children[2].value,\n            'scale': interactive_plot.children[3].value,\n            'cmap_start': interactive_plot.children[4].value,\n            'cmap_end': interactive_plot.children[5].value\n        }\n\n        # Generate the plot with current values\n        fig = plot_arpes(**current_values)\n\n        # Save the plot\n        filename = f\"ARPES_plot_scan_{current_values['scan_index']}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        plt.close(fig)\n        print(f\"Plot saved as {filename}\")\n\n    def make_video(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'vmin': interactive_plot.children[1].value,\n            'vmax': interactive_plot.children[2].value,\n            'scale': interactive_plot.children[3].value,\n            'cmap_start': interactive_plot.children[4].value,\n            'cmap_end': interactive_plot.children[5].value\n        }\n\n        # Determine scan types\n        scan_types = determine_scan_type(data_files)\n\n        # Create the video\n        fig, ax = plt.subplots(figsize=(10, 8))\n\n        # Create a custom normalization\n        norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax']/current_values['scale'])\n\n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))\n        cmap = ListedColormap(cmap_array)\n\n        # Initialize the plot with the first dataset\n        plot_data = all_plots[0]\n        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                           shading='auto', cmap=cmap, norm=norm)\n        cbar = fig.colorbar(im, ax=ax)\n        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n        cbar.ax.tick_params(labelsize=10)\n\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n        ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')\n\n        ims = []\n\n        for i, (plot_data, (scan_type, scan_value)) in enumerate(zip(all_plots, scan_types)):\n            im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                               shading='auto', cmap=cmap, norm=norm)\n\n            # Add text for scan type and value\n            if scan_type != 'unknown':\n                text = ax.text(0.5, -0.1, f'{scan_type}: {scan_value:.2f}',  # Position text below the plot\n                               transform=ax.transAxes, color='black',  # Set text color to black\n                               horizontalalignment='center', verticalalignment='top',\n                               fontsize=10, fontweight='bold')\n                ims.append([im, text])\n            else:\n                ims.append([im])\n\n        ani = animation.ArtistAnimation(fig, ims, interval=500, blit=True)\n        ani.save('ARPES_video.mp4')\n        plt.close(fig)\n        print(\"Video saved as ARPES_video.mp4\")\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),\n                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    # Create a video button\n    video_button = Button(description=\"Make Video\")\n    video_button.on_click(make_video)\n\n    # Combine the interactive plot, save button, and video button\n    output = VBox([interactive_plot, save_button, video_button])\n\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()  # Hide the root window\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()  # Destroy the root window\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n    # Determine scan types\n    scan_types = determine_scan_type(data_files)\n\n    interactive_plot_with_save = kspace(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")", 540)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.7.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.7.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.8": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display\nimport matplotlib.animation as animation\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\nimport tkinter as tk\nfrom tkinter import filedialog\n%matplotlib widget\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",  # Set default text color to black\n    \"axes.labelcolor\": \"black\",  # Set default axes label color to black\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\n\n# Suppress specific warnings\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef determine_scan_type(data_files):\n    scan_types = []\n    previous_polar = None\n    previous_hv = None\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        if 'polar' in data.attrs and 'hv' in data.attrs:\n            current_polar = data.attrs['polar']\n            current_hv = data.attrs['hv']\n            if previous_polar is not None and previous_hv is not None:\n                if current_polar != previous_polar:\n                    scan_types.append(('polar', current_polar))\n                elif current_hv != previous_hv:\n                    scan_types.append(('hv', current_hv))\n                else:\n                    scan_types.append(('unknown', None))\n            else:\n                scan_types.append(('unknown', None))\n            previous_polar = current_polar\n            previous_hv = current_hv\n        else:\n            scan_types.append(('unknown', None))\n\n    return scan_types\n\ndef kspace(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    max_data_value = 0\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']  # Photon energy from the attributes\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        max_data_value = max(max_data_value, np.max(data.values))\n\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n\n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n\n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n\n        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                           shading='auto', cmap=cmap, norm=norm)\n        cbar = fig.colorbar(im, ax=ax)\n        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n        cbar.ax.tick_params(labelsize=10)\n\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits\n\n        # Adjust tick label font size\n        ax.tick_params(axis='both', which='major', labelsize=10)\n\n        plt.tight_layout()\n        return fig\n\n    def save_plot(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'vmin': interactive_plot.children[1].value,\n            'vmax': interactive_plot.children[2].value,\n            'scale': interactive_plot.children[3].value,\n            'cmap_start': interactive_plot.children[4].value,\n            'cmap_end': interactive_plot.children[5].value\n        }\n\n        # Generate the plot with current values\n        fig = plot_arpes(**current_values)\n\n        # Save the plot\n        filename = f\"ARPES_plot_scan_{current_values['scan_index']}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        plt.close(fig)\n        print(f\"Plot saved as {filename}\")\n\n    def make_video(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'vmin': interactive_plot.children[1].value,\n            'vmax': interactive_plot.children[2].value,\n            'scale': interactive_plot.children[3].value,\n            'cmap_start': interactive_plot.children[4].value,\n            'cmap_end': interactive_plot.children[5].value\n        }\n\n        # Determine scan types\n        scan_types = determine_scan_type(data_files)\n\n        # Create the video\n        fig, ax = plt.subplots(figsize=(10, 8))\n\n        # Create a custom normalization\n        norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax']/current_values['scale'])\n\n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))\n        cmap = ListedColormap(cmap_array)\n\n        # Initialize the plot with the first dataset\n        plot_data = all_plots[0]\n        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                           shading='auto', cmap=cmap, norm=norm)\n        cbar = fig.colorbar(im, ax=ax)\n        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n        cbar.ax.tick_params(labelsize=10)\n\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n        ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')\n\n        ims = []\n\n        for i, (plot_data, (scan_type, scan_value)) in enumerate(zip(all_plots, scan_types)):\n            im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],\n                               shading='auto', cmap=cmap, norm=norm)\n\n            # Add text for scan type and value\n            if scan_type != 'unknown':\n                text = ax.text(0.5, -0.1, f'{scan_type}: {scan_value:.2f}',  # Position text below the plot\n                               transform=ax.transAxes, color='black',  # Set text color to black\n                               horizontalalignment='center', verticalalignment='top',\n                               fontsize=10, fontweight='bold')\n                ims.append([im, text])\n            else:\n                ims.append([im])\n\n        ani = animation.ArtistAnimation(fig, ims, interval=500, blit=True)\n        ani.save('ARPES_video.mp4')\n        plt.close(fig)\n        print(\"Video saved as ARPES_video.mp4\")\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),\n                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    # Create a video button\n    video_button = Button(description=\"Make Video\")\n    video_button.on_click(make_video)\n\n    # Combine the interactive plot, save button, and video button\n    output = VBox([interactive_plot, save_button, video_button])\n\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()  # Hide the root window\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()  # Destroy the root window\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n    # Determine scan types\n    scan_types = determine_scan_type(data_files)\n\n    interactive_plot_with_save = kspace(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")", 542)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.8.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.8.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog
get_ipython().run_line_magic('matplotlib', 'widget')
if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None

    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))

    return scan_types

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))

        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)

        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)

        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)

        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        return fig

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'vmin': interactive_plot.children[1].value,
            'vmax': interactive_plot.children[2].value,
            'scale': interactive_plot.children[3].value,
            'cmap_start': interactive_plot.children[4].value,
            'cmap_end': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        fig = plot_arpes(**current_values)

        # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    def make_video(b):
        # Get current slider values directly from the widgets
        current_values = {
            'vmin': interactive_plot.children[1].value,
            'vmax': interactive_plot.children[2].value,
            'scale': interactive_plot.children[3].value,
            'cmap_start': interactive_plot.children[4].value,
            'cmap_end': interactive_plot.children[5].value
        }

        # Determine scan types
        scan_types = determine_scan_type(data_files)

        # Create the video
        fig, ax = plt.subplots(figsize=(10, 8))

        # Create a custom normalization
        norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax']/current_values['scale'])

        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
        cmap = ListedColormap(cmap_array)

        # Initialize the plot with the first dataset
        plot_data = all_plots[0]
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)

        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')

        ims = []

        for i, (plot_data, (scan_type, scan_value)) in enumerate(zip(all_plots, scan_types)):
            im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],
                               shading='auto', cmap=cmap, norm=norm)

            # Add text for scan type and value
            if scan_type != 'unknown':
                text = ax.text(0.5, -0.1, f'{scan_type}: {scan_value:.2f}',  # Position text below the plot
                               transform=ax.transAxes, color='black',  # Set text color to black
                               horizontalalignment='center', verticalalignment='top',
                               fontsize=10, fontweight='bold')
                ims.append([im, text])
            else:
                ims.append([im])

        ani = animation.ArtistAnimation(fig, ims, interval=500, blit=True)
        ani.save('ARPES_video.mp4')
        plt.close(fig)
        print("Video saved as ARPES_video.mp4")

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(make_video)

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

    # Determine scan types
    scan_types = determine_scan_type(data_files)

    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    plot_data = all_plots[0]
    norm = Normalize(vmin=0, vmax=max_data_value)
    cmap_array = plt.cm.jet(np.linspace(0, 1, 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()

    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
        norm = Normalize(vmin=vmin, vmax=vmax / scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
        im.set_array(plot_data['data_values'].ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        fig.canvas.draw_idle()

    interactive_plot = interactive(update_plot,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(make_video)

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])

    return output

def save_plot(b):
    # Get current slider values directly from the widgets
    current_values = {
        'scan_index': interactive_plot.children[0].value,
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Generate the plot with current values
    fig = plot_arpes(**current_values)

    # Save the plot
    filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
    fig.savefig(filename, dpi=2000, bbox_inches='tight')
    plt.close(fig)
    print(f"Plot saved as {filename}")

def make_video(b):
    # Get current slider values directly from the widgets
    current_values = {
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Determine scan types
    scan_types = determine_scan_type(data_files)

    # Create the video
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    plot_data = all_plots[0]
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')
    ims = []

    for i, (plot_data, (scan_type, scan_value)) in enumerate(zip(all_plots, scan_types)):
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],
                           shading='auto', cmap=cmap, norm=norm)
        if scan_type != 'unknown':
            text = ax.text(0.5, -0.1, f'{scan_type}: {scan_value:.2f}',  # Position text below the plot
                           transform=ax.transAxes, color='black',  # Set text color to black
                           horizontalalignment='center', verticalalignment='top',
                           fontsize=10, fontweight='bold')
            ims.append([im, text])
        else:
            ims.append([im])

    ani = animation.ArtistAnimation(fig, ims, interval=500, blit=True)
    ani.save('ARPES_video.mp4')
    plt.close(fig)
    print("Video saved as ARPES_video.mp4")

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    scan_types = determine_scan_type(data_files)
    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
