# IPython log file

from arpes.io import load_data
#data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='ALS-BL7')
get_ipython().run_line_magic('pinfo2', 'load_data')
from arpes.io import load_data
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='ALS-BL7')
get_ipython().run_line_magic('pinfo2', 'load_data')
from arpes.io import load_data
#data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='ALS-BL7')
get_ipython().run_line_magic('pinfo2', 'load_data')
from arpes.io import load_data, IgorEndstation
#data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='ALS-BL7')
get_ipython().run_line_magic('pinfo2', 'load_data')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.54": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\n#data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='ALS-BL7')\nload_data??", 30)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.54.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.54.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.endstations.plugin.igor_plugin import IgorEndstation

from arpes.io import load_data
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='ALS-BL7')
get_ipython().run_line_magic('pinfo2', 'load_data')
from arpes.io import load_data
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt')
get_ipython().run_line_magic('pinfo2', 'load_data')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.55": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', l)\nload_data??", 190)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.55.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.55.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.56": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location=\"\")\nload_data??", 199)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.56.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.56.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.57": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\ndata = lofile=", 109, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.57.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.57.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.58": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\ndata = loid", 106, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.58.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.58.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.59": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\ndata = loIgorEndstation", 118, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.59.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.59.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.io import load_data
from arpes.endstations.plugin.igor_plugin import IgorEndstation
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location="IgorEndstation")
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.60": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location=\"IgorEndstation\")\nl", 217)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.60.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.60.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.io import load_data
from arpes.endstations.plugin.igor_plugin import IgorEndstation
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location="IgorEndstation")
load_data
from arpes.io import load_data
from arpes.endstations.plugin.igor_plugin import IgorEndstation
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', "IgorEndstation")
from arpes.io import load_data
from arpes.endstations.plugin.igor_plugin import IgorEndstation
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', "Igor")
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.61": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', l\"Igor\")", 190)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.61.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.61.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.62": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', locati0\"Igor\")", 196)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.62.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.62.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.63": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', locati0n\"Igor\")", 197)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.63.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.63.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.64": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location\"Igor\")", 196)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.64.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.64.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.io import load_data
from arpes.endstations.plugin.igor_plugin import IgorEndstation
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location="Igor")
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.65": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("p", 1)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.65.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.65.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.66": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip i", 5)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.66.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.66.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.67": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip instas", 10)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.67.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.67.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.68": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip instasl", 11)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.68.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.68.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.69": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip instasll", 12)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.69.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.69.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.70": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip instal", 10)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.70.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.70.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.71": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install", 11)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.71.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.71.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.72": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install i", 13)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.72.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.72.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
get_ipython().run_line_magic('pip', 'install igor')
