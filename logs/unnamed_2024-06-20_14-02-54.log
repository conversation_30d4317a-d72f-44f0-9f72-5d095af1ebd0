# IPython log file

from arpes.io import load_data
from arpes.endstations.plugin.igor_plugin import IgorEndstation
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location="Igor")
from arpes.io import load_data
from arpes.endstations.plugin.igor_plugin import IgorEndstation
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.73": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', l)", 190)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.73.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.73.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.74": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location-'')", 199)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.74.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.74.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.75": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='')", 199)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.75.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.75.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.76": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\ndata = loIOError", 111, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.76.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.76.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.77": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\ndata = loIgorEndstation", 118, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.77.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.77.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.io import load_data
from arpes.endstations.plugin.igor_plugin import IgorEndstation
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='IgorEndstation')
from arpes.io import load_data
from arpes.endstations.plugin.igor_plugin import IgorEndstation
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='Igor')
from arpes.io import load_data
from arpes.endstations.plugin.igor_plugin import IgorEndstation
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S134.pxt', location='Igor')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.78": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install igorp", 17)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.78.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.78.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.79": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install igorpy", 18)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.79.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.79.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
get_ipython().run_line_magic('pip', 'install igorpy')
