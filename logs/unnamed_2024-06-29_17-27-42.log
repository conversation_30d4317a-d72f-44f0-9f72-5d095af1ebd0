# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Deja<PERSON><PERSON> Sans', 'Helve<PERSON>', 'Aria<PERSON>', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_energy_min = float('inf')
    global_energy_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the momentum values
        momentum_values = data.phi.values  # Momentum values

        all_plots.append({
            'k_parallel': k_parallel,
            'momentum_values': momentum_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global energy range
        global_energy_min = min(global_energy_min, np.min(data.eV.values))
        global_energy_max = max(global_energy_max, np.max(data.eV.values))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced momentum indices
        momentum_indices = np.linspace(0, len(plot_data['momentum_values']) - 1, n, dtype=int)

        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, momentum_index in enumerate(momentum_indices):
            actual_momentum = plot_data['momentum_values'][momentum_index]
            edc = plot_data['data_values'][:, momentum_index]
            energy_values = -plot_data['k_parallel'][:, momentum_index]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(edc))

            # Plot EDC with vertical offset
            if show_edc:
                ax.plot(energy_values, edc + i * vertical_offset, label=f'k = {actual_momentum:.2f} Å$^{-1}$')

            # Fit EDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit
                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit k = {actual_momentum:.2f} Å$^{-1}$', color=f'C{i}')

        ax.set_xlabel(r'$E$ (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_edc(**current_values)

        # Save the plot
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_edc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_edc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_edc=Checkbox(value=True, description='Show EDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of EDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k range
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_indices = np.linspace(0, len(plot_data['k_parallel']) - 1, n, dtype=int)

        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(edc))

            # Plot EDC with vertical offset
            if show_edc:
                ax.plot(energy_values, edc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k:.2f} Å$^{{-1}}$')

            # Fit EDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Fit the entire EDC
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_edc(**current_values)

        # Save the plot
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_edc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_edc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_edc=Checkbox(value=True, description='Show EDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of EDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k range
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_indices = np.linspace(0, len(plot_data['k_parallel']) - 1, n, dtype=int)

        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(edc))

            # Plot EDC with vertical offset
            if show_edc:
                ax.plot(energy_values, edc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k:.2f} Å$^{{-1}}$')

            # Fit EDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Fit the entire EDC
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_edc(**current_values)

        # Save the plot
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_edc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_edc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_edc=Checkbox(value=True, description='Show EDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of EDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k range
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_indices = np.linspace(0, len(plot_data['k_parallel']) - 1, n, dtype=int)

        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.to_numpy()

            # Update max intensity
            max_intensity = max(max_intensity, np.max(edc))

            # Plot EDC with vertical offset
            if show_edc:
                ax.plot(energy_values, edc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k:.2f} Å$^{{-1}}$')

            # Fit EDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Fit the entire EDC
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_edc(**current_values)

        # Save the plot
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_edc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_edc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_edc=Checkbox(value=True, description='Show EDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of EDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k range
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_indices = np.linspace(0, len(plot_data['k_parallel']) - 1, n, dtype=int)

        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(actual_k, xr.DataArray):
                actual_k = actual_k.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(edc))

            # Plot EDC with vertical offset
            if show_edc:
                ax.plot(energy_values, edc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k:.2f} Å$^{{-1}}$')

            # Fit EDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Fit the entire EDC
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_edc(**current_values)

        # Save the plot
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_edc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_edc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_edc=Checkbox(value=True, description='Show EDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of EDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k range
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_indices = np.linspace(0, len(plot_data['k_parallel']) - 1, n, dtype=int)

        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(actual_k, xr.DataArray):
                actual_k = actual_k.values.item()  # Convert to scalar

            # Update max intensity
            max_intensity = max(max_intensity, np.max(edc))

            # Plot EDC with vertical offset
            if show_edc:
                ax.plot(energy_values, edc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k:.2f} Å$^{{-1}}$')

            # Fit EDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Fit the entire EDC
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_edc(**current_values)

        # Save the plot
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_edc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_edc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_edc=Checkbox(value=True, description='Show EDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of EDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k range
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_indices = np.linspace(0, len(plot_data['k_parallel']) - 1, n, dtype=int)

        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(actual_k, xr.DataArray):
                actual_k = actual_k.item()  # Convert to scalar

            # Update max intensity
            max_intensity = max(max_intensity, np.max(edc))

            # Plot EDC with vertical offset
            if show_edc:
                ax.plot(energy_values, edc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k:.2f} Å$^{{-1}}$')

            # Fit EDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Fit the entire EDC
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_edc(**current_values)

        # Save the plot
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_edc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_edc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_edc=Checkbox(value=True, description='Show EDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of EDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_parallel_min = float('inf')
    global_k_parallel_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel range
        global_k_parallel_min = min(global_k_parallel_min, np.min(k_parallel))
        global_k_parallel_max = max(global_k_parallel_max, np.max(k_parallel))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_parallel_indices = np.linspace(0, len(plot_data['k_parallel']) - 1, n, dtype=int)

        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, k_parallel_index in enumerate(k_parallel_indices):
            actual_k_parallel = plot_data['k_parallel'][k_parallel_index]
            mdc = plot_data['data_values'][:, k_parallel_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(energy_values, mdc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Fit the data
                peaks, _ = find_peaks(mdc)
                peak_heights = mdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(mdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(mdc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_mdc(**current_values)
        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_mdc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_parallel_min = float('inf')
    global_k_parallel_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel range
        global_k_parallel_min = min(global_k_parallel_min, np.min(k_parallel))
        global_k_parallel_max = max(global_k_parallel_max, np.max(k_parallel))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_parallel_indices = np.linspace(0, len(plot_data['k_parallel']) - 1, n, dtype=int)

        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, k_parallel_index in enumerate(k_parallel_indices):
            actual_k_parallel = plot_data['k_parallel'][k_parallel_index]
            mdc = plot_data['data_values'][:, k_parallel_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(energy_values, mdc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Fit the data
                peaks, _ = find_peaks(mdc)
                peak_heights = mdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(mdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(mdc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_mdc(**current_values)
        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_mdc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_parallel_min = float('inf')
    global_k_parallel_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel.values if isinstance(k_parallel, xr.DataArray) else k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel range
        global_k_parallel_min = min(global_k_parallel_min, np.min(k_parallel))
        global_k_parallel_max = max(global_k_parallel_max, np.max(k_parallel))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_parallel_indices = np.linspace(0, len(plot_data['k_parallel']) - 1, n, dtype=int)

        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, k_parallel_index in enumerate(k_parallel_indices):
            actual_k_parallel = plot_data['k_parallel'][k_parallel_index]
            mdc = plot_data['data_values'][:, k_parallel_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(energy_values, mdc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Fit the data
                peaks, _ = find_peaks(mdc)
                peak_heights = mdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(mdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(mdc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_mdc(**current_values)
        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_mdc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_parallel_min = float('inf')
    global_k_parallel_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel.values if isinstance(k_parallel, xr.DataArray) else k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel range
        global_k_parallel_min = min(global_k_parallel_min, np.min(k_parallel))
        global_k_parallel_max = max(global_k_parallel_max, np.max(k_parallel))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_parallel_indices = np.linspace(0, len(plot_data['k_parallel']) - 1, n, dtype=int)

        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, k_parallel_index in enumerate(k_parallel_indices):
            actual_k_parallel = float(plot_data['k_parallel'][k_parallel_index])
            mdc = plot_data['data_values'][:, k_parallel_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(energy_values, mdc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Fit the data
                peaks, _ = find_peaks(mdc)
                peak_heights = mdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(mdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(mdc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_mdc(**current_values)
        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_mdc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_parallel_min = float('inf')
    global_k_parallel_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel.values if isinstance(k_parallel, xr.DataArray) else k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel range
        global_k_parallel_min = min(global_k_parallel_min, np.min(k_parallel))
        global_k_parallel_max = max(global_k_parallel_max, np.max(k_parallel))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_parallel_indices = np.linspace(0, len(plot_data['k_parallel']) - 1, n, dtype=int)

        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, k_parallel_index in enumerate(k_parallel_indices):
            actual_k_parallel = plot_data['k_parallel'][k_parallel_index].item()  # Ensure it's a scalar
            mdc = plot_data['data_values'][:, k_parallel_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(energy_values, mdc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Fit the data
                peaks, _ = find_peaks(mdc)
                peak_heights = mdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(mdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(mdc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_mdc(**current_values)
        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_mdc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, VBox, Checkbox, Output
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import read_single_pxt
from scipy.signal import find_peaks
from lmfit.models import LorentzianModel, LinearModel

get_ipython().run_line_magic('matplotlib', 'widget')

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Binding energy
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel.values if isinstance(k_parallel, xr.DataArray) else k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_parallel_indices = np.linspace(0, len(plot_data['k_parallel']) - 1, n, dtype=int)

        for i, k_parallel_index in enumerate(k_parallel_indices):
            actual_k_parallel = plot_data['k_parallel'][k_parallel_index].item()  # Ensure it's a scalar
            mdc = plot_data['data_values'][:, k_parallel_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(energy_values, mdc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                peaks, _ = find_peaks(mdc)
                peak_heights = mdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(mdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(mdc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_mdc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Combine the interactive plot and output widget
    output = VBox([interactive_plot, out])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel range
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_indices = np.linspace(0, plot_data['k_parallel'].shape[1] - 1, n, dtype=int)

        max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            max_intensity = max(max_intensity, np.max(edc))

            if show_edc:
                ax.plot(energy_values, edc + i * vertical_offset, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_edc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel range
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_indices = np.linspace(0, plot_data['k_parallel'].shape[1] - 1, n, dtype=int)

        max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            max_intensity = max(max_intensity, np.max(edc))

            if show_edc:
                ax.plot(energy_values, edc + i * vertical_offset, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_edc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, VBox, Checkbox, Output
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import read_single_pxt
from scipy.signal import find_peaks
from lmfit.models import LorentzianModel, LinearModel

get_ipython().run_line_magic('matplotlib', 'widget')

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_parallel_min = float('inf')
    global_k_parallel_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Binding energy
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel.values if isinstance(k_parallel, xr.DataArray) else k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel range
        global_k_parallel_min = min(global_k_parallel_min, np.min(k_parallel))
        global_k_parallel_max = max(global_k_parallel_max, np.max(k_parallel))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks, k_min, k_max):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Filter k_parallel values within the specified range
        k_parallel_mask = (plot_data['k_parallel'] >= k_min) & (plot_data['k_parallel'] <= k_max)
        k_parallel_indices = np.where(k_parallel_mask)[0]

        # Calculate equally spaced k_parallel indices
        k_parallel_indices = np.linspace(k_parallel_indices[0], k_parallel_indices[-1], n, dtype=int)

        for i, k_parallel_index in enumerate(k_parallel_indices):
            actual_k_parallel = plot_data['k_parallel'][k_parallel_index].item()  # Ensure it's a scalar
            mdc = plot_data['data_values'][:, k_parallel_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(energy_values, mdc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                peaks, _ = find_peaks(mdc)
                peak_heights = mdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(mdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(mdc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
                                   k_min=FloatSlider(value=global_k_parallel_min, min=global_k_parallel_min, max=global_k_parallel_max, step=0.01, description='k_min', continuous_update=True),
                                   k_max=FloatSlider(value=global_k_parallel_max, min=global_k_parallel_min, max=global_k_parallel_max, step=0.01, description='k_max', continuous_update=True))

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_mdc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Combine the interactive plot and output widget
    output = VBox([interactive_plot, out])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, VBox, Checkbox, Output
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import read_single_pxt
from scipy.signal import find_peaks
from lmfit.models import LorentzianModel, LinearModel

get_ipython().run_line_magic('matplotlib', 'widget')

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_parallel_min = float('inf')
    global_k_parallel_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Binding energy
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel.values if isinstance(k_parallel, xr.DataArray) else k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel range
        global_k_parallel_min = min(global_k_parallel_min, np.min(k_parallel))
        global_k_parallel_max = max(global_k_parallel_max, np.max(k_parallel))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks, k_min, k_max):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Filter k_parallel values within the specified range
        k_parallel_mask = (plot_data['k_parallel'] >= k_min) & (plot_data['k_parallel'] <= k_max)
        k_parallel_indices = np.where(k_parallel_mask)[0]

        if len(k_parallel_indices) == 0:
            print(f"No k_parallel values in the range {k_min} to {k_max}")
            return

        # Calculate equally spaced k_parallel indices
        k_parallel_indices = np.linspace(k_parallel_indices[0], k_parallel_indices[-1], n, dtype=int)

        for i, k_parallel_index in enumerate(k_parallel_indices):
            actual_k_parallel = plot_data['k_parallel'][k_parallel_index]  # Ensure it's a scalar
            mdc = plot_data['data_values'][:, k_parallel_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(energy_values, mdc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                peaks, _ = find_peaks(mdc)
                peak_heights = mdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(mdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(mdc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
                                   k_min=FloatSlider(value=global_k_parallel_min, min=global_k_parallel_min, max=global_k_parallel_max, step=0.01, description='k_min', continuous_update=True),
                                   k_max=FloatSlider(value=global_k_parallel_max, min=global_k_parallel_min, max=global_k_parallel_max, step=0.01, description='k_max', continuous_update=True))

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_mdc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Combine the interactive plot and output widget
    output = VBox([interactive_plot, out])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, VBox, Checkbox, Output
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import read_single_pxt
from scipy.signal import find_peaks
from lmfit.models import LorentzianModel, LinearModel

get_ipython().run_line_magic('matplotlib', 'widget')

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Binding energy
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel.values if isinstance(k_parallel, xr.DataArray) else k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_parallel_indices = np.linspace(0, len(plot_data['k_parallel']) - 1, n, dtype=int)

        for i, k_parallel_index in enumerate(k_parallel_indices):
            actual_k_parallel = plot_data['k_parallel'][k_parallel_index].item()  # Ensure it's a scalar
            mdc = plot_data['data_values'][:, k_parallel_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(energy_values, mdc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                peaks, _ = find_peaks(mdc)
                peak_heights = mdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(mdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(mdc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_mdc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Combine the interactive plot and output widget
    output = VBox([interactive_plot, out])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_parallel_min = float('inf')
    global_k_parallel_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel.values if isinstance(k_parallel, xr.DataArray) else k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel range
        global_k_parallel_min = min(global_k_parallel_min, np.min(k_parallel))
        global_k_parallel_max = max(global_k_parallel_max, np.max(k_parallel))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_parallel_indices = np.linspace(0, len(plot_data['k_parallel']) - 1, n, dtype=int)

        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, k_parallel_index in enumerate(k_parallel_indices):
            actual_k_parallel = plot_data['k_parallel'][k_parallel_index].item()  # Ensure it's a scalar
            mdc = plot_data['data_values'][:, k_parallel_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(energy_values, mdc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Fit the data
                peaks, _ = find_peaks(mdc)
                peak_heights = mdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(mdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(mdc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_mdc(**current_values)
        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_mdc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def mdc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_parallel_min = float('inf')
    global_k_parallel_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel.values if isinstance(k_parallel, xr.DataArray) else k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel range
        global_k_parallel_min = min(global_k_parallel_min, np.min(k_parallel))
        global_k_parallel_max = max(global_k_parallel_max, np.max(k_parallel))

    def plot_mdc(scan_index, n, vertical_offset, show_mdc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_parallel_indices = np.linspace(0, len(plot_data['k_parallel']) - 1, n, dtype=int)

        max_intensity = 0  # Initialize max intensity for the displayed curves

        for i, k_parallel_index in enumerate(k_parallel_indices):
            actual_k_parallel = float(plot_data['k_parallel'][k_parallel_index])
            mdc = plot_data['data_values'][:, k_parallel_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(mdc, xr.DataArray):
                mdc = mdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Update max intensity
            max_intensity = max(max_intensity, np.max(mdc))

            # Plot MDC with vertical offset
            if show_mdc:
                ax.plot(energy_values, mdc + i * vertical_offset, label=f'$k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$')

            # Fit MDC with multiple Lorentzian peaks + Linear background
            if show_fit:
                # Fit the data
                peaks, _ = find_peaks(mdc)
                peak_heights = mdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]  # Indices of the largest peaks
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(mdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(mdc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit $k_\\parallel$ = {actual_k_parallel:.2f} Å$^{-1}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'MDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()

        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)

        plt.tight_layout()
        fig.canvas.draw_idle()  # Update the figure

        return max_intensity

    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        # Generate the plot with current values
        plot_mdc(**current_values)
        # Save the plot
        filename = f"MDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_mdc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_mdc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    # Create the interactive widget
    interactive_plot = interactive(plot_mdc,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   n=IntSlider(value=5, min=1, max=20, step=1, description='Number of MDCs', continuous_update=True),
                                   vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
                                   show_mdc=Checkbox(value=True, description='Show MDCs'),
                                   show_fit=Checkbox(value=False, description='Show Fits'),
                                   num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    # Update the vertical offset range when the number of MDCs or scan index changes
    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    # Create an output widget to capture the plot
    out = Output()

    # Function to display the plot in the output widget
    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_mdc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    # Observe changes in the interactive plot and update the output widget
    interactive_plot.observe(display_plot, names='value')

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot, output widget, and the save button
    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))  # Create the figure and axis once
    interactive_plot_with_save = mdc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel range
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices
        k_indices = np.linspace(0, plot_data['k_parallel'].shape[1] - 1, n, dtype=int)

        max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            max_intensity = max(max_intensity, np.max(edc))

            if show_edc:
                ax.plot(energy_values, edc + i * vertical_offset, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value
        }

        max_intensity = plot_edc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True))

    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel range
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))

    global_k_range = global_k_max - global_k_min
    k_min_initial = global_k_min
    k_max_initial = global_k_max

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            max_intensity = max(max_intensity, np.max(edc))

            if show_edc:
                ax.plot(energy_values, edc + i * vertical_offset, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value
        }

        max_intensity = plot_edc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=k_min_initial, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=k_max_initial, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True)
    )

    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel range
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))

    global_k_range = global_k_max - global_k_min
    k_min_initial = global_k_min
    k_max_initial = global_k_max

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            max_intensity = max(max_intensity, np.max(edc))

            if show_edc:
                ax.plot(energy_values, edc + i * vertical_offset, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value
        }

        max_intensity = plot_edc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=k_min_initial, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=k_max_initial, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True)
    )

    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            max_intensity = max(max_intensity, np.max(edc))

            if show_edc:
                ax.plot(energy_values, edc + i * vertical_offset, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)
        ax.set_ylim(0, max_intensity * n * vertical_offset)
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        max_intensity = plot_edc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            max_intensity = max(max_intensity, np.max(edc))

            if show_edc:
                ax.plot(energy_values, edc + i * vertical_offset, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)
        ax.set_ylim(0, max_intensity * n * vertical_offset)
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        max_intensity = plot_edc(**current_values)
        interactive_plot.children[2].max = current_values['n'] * max_intensity

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    interactive_plot.children[0].observe(update_vertical_offset_range, names='value')
    interactive_plot.children[1].observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_energy_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            edc = edc[valid_energy_indices]
            energy_values = energy_values[valid_energy_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            max_intensity = max(max_intensity, np.max(edc))

            if show_edc:
                ax.plot(energy_values, edc + i * vertical_offset, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit + i * vertical_offset, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)
        ax.set_ylim(0, max_intensity * n * vertical_offset)
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        max_intensity = plot_edc(**current_values)
        
        # Update vertical offset slider bounds
        k_range = current_values['k_max'] - current_values['k_min']
        e_range = current_values['e_max'] - current_values['e_min']
        scale_factor = (k_range / global_k_range) * (e_range / global_e_range)
        
        new_max_offset = scale_factor * max_intensity * current_values['n']
        interactive_plot.children[2].max = new_max_offset

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_energy_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            edc = edc[valid_energy_indices]
            energy_values = energy_values[valid_energy_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            max_intensity = max(max_intensity, np.max(edc))

            if show_edc:
                # Apply sequential vertical offset
                offset_edc = edc + i * vertical_offset
                ax.plot(energy_values, offset_edc, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                # Apply sequential vertical offset to the fit
                offset_fit = fit + i * vertical_offset
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)
        ax.set_ylim(0, max_intensity + (n - 1) * vertical_offset)
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        max_intensity = plot_edc(**current_values)
        
        # Update vertical offset slider bounds
        k_range = current_values['k_max'] - current_values['k_min']
        e_range = current_values['e_max'] - current_values['e_min']
        scale_factor = (k_range / global_k_range) * (e_range / global_e_range)
        
        new_max_offset = scale_factor * max_intensity * current_values['n']
        interactive_plot.children[2].max = new_max_offset

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = 0
        first_curve_max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_energy_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            edc = edc[valid_energy_indices]
            energy_values = energy_values[valid_energy_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            max_intensity = max(max_intensity, np.max(edc))
            if i == 0:
                first_curve_max_intensity = np.max(edc)

            if show_edc:
                # Apply sequential vertical offset
                offset_edc = edc + i
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = 0
        first_curve_max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_energy_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            edc = edc[valid_energy_indices]
            energy_values = energy_values[valid_energy_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            max_intensity = max(max_intensity, np.max(edc))
            if i == 0:
                first_curve_max_intensity = np.max(edc)

            if show_edc:
                # Apply sequential vertical offset
                offset_edc = edc + i * vertical_offset
                ax.plot(energy_values, offset_edc, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                # Apply sequential vertical offset to the fit
                offset_fit = fit + i * vertical_offset
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)
        ax.set_ylim(0, max_intensity + (n - 1) * vertical_offset)
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity, first_curve_max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        max_intensity, first_curve_max_intensity = plot_edc(**current_values)
        
        # Update vertical offset slider bounds
        n = current_values['n']
        new_max_offset = n * first_curve_max_intensity
        interactive_plot.children[2].max = new_max_offset
        
        # Adjust the current value if it's out of the new range
        if interactive_plot.children[2].value > new_max_offset:
            interactive_plot.children[2].value = new_max_offset

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    # Call update_vertical_offset_range once to set initial bounds
    update_vertical_offset_range()

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_energy_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            edc = edc[valid_energy_indices]
            energy_values = energy_values[valid_energy_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Normalize the curve to its maximum intensity
            edc_max = np.max(edc)
            edc_normalized = edc / edc_max if edc_max > 0 else edc

            max_intensity = max(max_intensity, np.max(edc_normalized))

            if show_edc:
                # Apply sequential vertical offset (ranging from 0 to n-1)
                offset_edc = edc_normalized + i * vertical_offset
                ax.plot(energy_values, offset_edc, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc_normalized)
                peak_heights = edc_normalized[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc_normalized))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc_normalized, params, x=energy_values)
                fit = result.best_fit

                # Apply sequential vertical offset to the fit
                offset_fit = fit + i * vertical_offset
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)
        ax.set_ylim(0, 1 + (n - 1) * vertical_offset)
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        
        # Update vertical offset slider bounds
        n = current_values['n']
        interactive_plot.children[2].max = n - 1
        
        # Adjust the current value if it's out of the new range
        if interactive_plot.children[2].value > n - 1:
            interactive_plot.children[2].value = n - 1

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=4.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
