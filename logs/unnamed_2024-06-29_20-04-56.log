# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['<PERSON>ja<PERSON><PERSON> Sans', 'Helve<PERSON>', 'Aria<PERSON>', 'Luc<PERSON> Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = 0
        offset_curves = []

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_energy_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            edc = edc[valid_energy_indices]
            energy_values = energy_values[valid_energy_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Apply vertical offset
            offset_edc = edc + i * vertical_offset
            offset_curves.append((energy_values, offset_edc, actual_k))

        # Renormalize curves after applying vertical offset
        for i, (energy_values, offset_edc, actual_k) in enumerate(offset_curves):
            # Normalize the curve to its maximum intensity
            edc_max = np.max(offset_edc)
            edc_normalized = offset_edc / edc_max if edc_max > 0 else offset_edc

            max_intensity = max(max_intensity, np.max(edc_normalized))

            if show_edc:
                ax.plot(energy_values, edc_normalized, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc_normalized)
                peak_heights = edc_normalized[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc_normalized))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc_normalized, params, x=energy_values)
                fit = result.best_fit

                ax.plot(energy_values, fit, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)
        ax.set_ylim(0, 1.1)  # Set y-axis limit to slightly above 1 for better visibility
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        
        # Update vertical offset slider bounds
        n = current_values['n']
        interactive_plot.children[2].max = n - 1
        
        # Adjust the current value if it's out of the new range
        if interactive_plot.children[2].value > n - 1:
            interactive_plot.children[2].value = n - 1

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=4.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_energy_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            edc = edc[valid_energy_indices]
            energy_values = energy_values[valid_energy_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Apply vertical offset
            offset_edc = edc + i * vertical_offset

            max_intensity = max(max_intensity, np.max(offset_edc))

            if show_edc:
                ax.plot(energy_values, offset_edc, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                # Apply vertical offset to the fit
                offset_fit = fit + i * vertical_offset
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)
        ax.set_ylim(0, max_intensity * 1.1)  # Add 10% margin at the top
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        max_intensity = plot_edc(**current_values)
        
        # Update vertical offset slider bounds
        n = current_values['n']
        new_max_offset = max_intensity / (n - 1) if n > 1 else max_intensity
        interactive_plot.children[2].max = new_max_offset
        
        # Adjust the current value if it's out of the new range
        if interactive_plot.children[2].value > new_max_offset:
            interactive_plot.children[2].value = new_max_offset

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_energy_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            edc = edc[valid_energy_indices]
            energy_values = energy_values[valid_energy_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Apply vertical offset
            offset_edc = edc + i * vertical_offset

            # Normalize the curve so its maximum is 1 unit above its offset "zero point"
            edc_max = np.max(offset_edc)
            edc_min = np.min(offset_edc)
            normalized_edc = (offset_edc - edc_min) / (edc_max - edc_min) + i * vertical_offset

            max_intensity = max(max_intensity, np.max(normalized_edc))

            if show_edc:
                ax.plot(energy_values, normalized_edc, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc)
                peak_heights = edc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc, params, x=energy_values)
                fit = result.best_fit

                # Normalize and offset the fit
                fit_max = np.max(fit)
                fit_min = np.min(fit)
                normalized_fit = (fit - fit_min) / (fit_max - fit_min) + i * vertical_offset
                ax.plot(energy_values, normalized_fit, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)
        ax.set_ylim(0, max_intensity * 1.1)  # Add 10% margin at the top
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        max_intensity = plot_edc(**current_values)
        
        # Update vertical offset slider bounds
        n = current_values['n']
        new_max_offset = max_intensity / n if n > 1 else max_intensity
        interactive_plot.children[2].max = new_max_offset
        
        # Adjust the current value if it's out of the new range
        if interactive_plot.children[2].value > new_max_offset:
            interactive_plot.children[2].value = new_max_offset

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_energy_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            edc = edc[valid_energy_indices]
            energy_values = energy_values[valid_energy_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Normalize the curve to its maximum intensity
            edc_max = np.max(edc)
            edc_normalized = edc / edc_max if edc_max > 0 else edc

            # Apply vertical offset (ranging from 0 to 1.05)
            offset_edc = edc_normalized + i * (vertical_offset / (n - 1)) * 1.05

            max_intensity = max(max_intensity, np.max(offset_edc))

            if show_edc:
                ax.plot(energy_values, offset_edc, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc_normalized)
                peak_heights = edc_normalized[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc_normalized))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc_normalized, params, x=energy_values)
                fit = result.best_fit

                # Apply vertical offset to the fit
                offset_fit = fit + i * (vertical_offset / (n - 1)) * 1.05
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)
        ax.set_ylim(0, 1.05)  # Set y-axis limit to 1.05
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=1.05, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = 0
        min_energy = float('inf')
        max_energy = float('-inf')

        curves = []

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_energy_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            edc = edc[valid_energy_indices]
            energy_values = energy_values[valid_energy_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Normalize the curve to its maximum intensity
            edc_max = np.max(edc)
            edc_normalized = edc / edc_max if edc_max > 0 else edc

            curves.append((energy_values, edc_normalized, actual_k))

            max_intensity = max(max_intensity, np.max(edc_normalized))
            min_energy = min(min_energy, np.min(energy_values))
            max_energy = max(max_energy, np.max(energy_values))

        # Calculate vertical offset scale
        vertical_scale = vertical_offset / (n - 1) if n > 1 else 0

        for i, (energy_values, edc_normalized, actual_k) in enumerate(curves):
            offset_edc = edc_normalized + i * vertical_scale

            if show_edc:
                ax.plot(energy_values, offset_edc, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc_normalized)
                peak_heights = edc_normalized[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc_normalized))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc_normalized, params, x=energy_values)
                fit = result.best_fit

                offset_fit = fit + i * vertical_scale
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on data and vertical offset
        ax.set_xlim(min_energy, max_energy)
        ax.set_ylim(0, 1 + vertical_offset)
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=1.05, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_energy_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            edc = edc[valid_energy_indices]
            energy_values = energy_values[valid_energy_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Normalize the curve to its maximum intensity
            edc_max = np.max(edc)
            edc_normalized = edc / edc_max if edc_max > 0 else edc

            # Apply vertical offset (n times the offset for nth curve)
            offset_edc = edc_normalized + i * vertical_offset

            max_intensity = max(max_intensity, np.max(offset_edc))

            if show_edc:
                ax.plot(energy_values, offset_edc, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc_normalized)
                peak_heights = edc_normalized[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc_normalized))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc_normalized, params, x=energy_values)
                fit = result.best_fit

                # Apply vertical offset to the fit
                offset_fit = fit + i * vertical_offset
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on data and vertical offset
        ax.set_xlim(e_min, e_max)
        ax.set_ylim(0, 1 + (n - 1) * vertical_offset)
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=1.05, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced energy indices within the specified range
        energy_values = plot_data['energy_values']
        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        e_indices = valid_indices[e_indices]

        max_intensity = 0

        for i, e_index in enumerate(e_indices):
            actual_e = energy_values[e_index]
            edc = plot_data['data_values'][e_index, :]
            k_parallel = plot_data['k_parallel'][0, :]

            # Filter k_parallel values within the specified range
            valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
            edc = edc[valid_k_indices]
            k_parallel = k_parallel[valid_k_indices]

            # Normalize the curve to its maximum intensity
            edc_max = np.max(edc)
            edc_normalized = edc / edc_max if edc_max > 0 else edc

            # Apply vertical offset
            offset_edc = edc_normalized + i * vertical_offset

            max_intensity = max(max_intensity, np.max(offset_edc))

            if show_edc:
                ax.plot(k_parallel, offset_edc, label=f'E = {actual_e:.2f} eV')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc_normalized)
                peak_heights = edc_normalized[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc_normalized))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=k_parallel[peak], min=k_parallel.min(), max=k_parallel.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc_normalized, params, x=k_parallel)
                fit = result.best_fit

                # Apply vertical offset to the fit
                offset_fit = fit + i * vertical_offset
                ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')

        ax.set_xlabel('k_parallel (Å⁻¹)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on data and vertical offset
        ax.set_xlim(k_min, k_max)
        ax.set_ylim(0, 1 + (n - 1) * vertical_offset)
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=1.05, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced energy indices within the specified range
        energy_values = plot_data['energy_values']
        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        e_indices = valid_indices[e_indices]

        max_intensity = 0

        for i, e_index in enumerate(e_indices):
            actual_e = energy_values[e_index]
            kdc = plot_data['data_values'][e_index, :]
            k_parallel = plot_data['k_parallel'][0]

            # Filter k_parallel values within the specified range
            valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
            kdc = kdc[valid_k_indices]
            k_parallel = k_parallel[valid_k_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Normalize the curve to its maximum intensity
            kdc_max = np.max(kdc)
            kdc_normalized = kdc / kdc_max if kdc_max > 0 else kdc

            # Apply vertical offset
            offset_kdc = kdc_normalized + i * vertical_offset

            max_intensity = max(max_intensity, np.max(offset_kdc))

            if show_edc:
                ax.plot(k_parallel, offset_kdc, label=f'E = {actual_e:.2f} eV')

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc_normalized)
                peak_heights = kdc_normalized[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc_normalized))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=k_parallel[peak], min=k_parallel.min(), max=k_parallel.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc_normalized, params, x=k_parallel)
                fit = result.best_fit

                offset_fit = fit + i * vertical_offset
                ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')

        ax.set_xlabel('k$_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'KDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on sliders
        ax.set_xlim(k_min, k_max)
        ax.set_ylim(0, 1 + (n - 1) * vertical_offset)
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"KDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of KDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=1.05, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show KDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced energy indices within the specified range
        energy_values = plot_data['energy_values']
        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        e_indices = valid_indices[e_indices]

        max_intensity = 0

        for i, e_index in enumerate(e_indices):
            actual_e = energy_values[e_index]
            kdc = plot_data['data_values'][e_index, :]
            k_parallel = plot_data['k_parallel'][0]

            # Filter k_parallel values within the specified range
            valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
            kdc = kdc[valid_k_indices]
            k_parallel = k_parallel[valid_k_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Normalize the curve to its maximum intensity
            kdc_max = np.max(kdc)
            kdc_normalized = kdc / kdc_max if kdc_max > 0 else kdc

            # Apply vertical offset
            offset_kdc = kdc_normalized + i * vertical_offset

            max_intensity = max(max_intensity, np.max(offset_kdc))

            if show_edc:
                ax.plot(k_parallel, offset_kdc, label=f'E = {actual_e:.2f} eV')

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc_normalized)
                peak_heights = kdc_normalized[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc_normalized))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=k_parallel[peak], min=k_parallel.min(), max=k_parallel.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc_normalized, params, x=k_parallel)
                fit = result.best_fit

                offset_fit = fit + i * vertical_offset
                ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')

        ax.set_xlabel('k$_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'KDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on sliders
        ax.set_xlim(k_min, k_max)
        ax.set_ylim(0, 1 + (n - 1) * vertical_offset)
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"KDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of KDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=1.05, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show KDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
from scipy.optimize import minimize_scalar

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced energy indices within the specified range
        energy_values = plot_data['energy_values']
        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        e_indices = valid_indices[e_indices]

        max_intensity = 0
        curves = []

        for i, e_index in enumerate(e_indices):
            actual_e = energy_values[e_index]
            kdc = plot_data['data_values'][e_index, :]
            k_parallel = plot_data['k_parallel'][0]

            # Filter k_parallel values within the specified range
            valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
            kdc = kdc[valid_k_indices]
            k_parallel = k_parallel[valid_k_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Normalize the curve to its maximum intensity
            kdc_max = np.max(kdc)
            kdc_normalized = kdc / kdc_max if kdc_max > 0 else kdc

            # Apply vertical offset
            offset_kdc = kdc_normalized + i * vertical_offset

            max_intensity = max(max_intensity, np.max(offset_kdc))

            if show_edc:
                line, = ax.plot(k_parallel, offset_kdc, label=f'E = {actual_e:.2f} eV')
                curves.append((line, k_parallel, offset_kdc))

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc_normalized)
                peak_heights = kdc_normalized[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc_normalized))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=k_parallel[peak], min=k_parallel.min(), max=k_parallel.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc_normalized, params, x=k_parallel)
                fit = result.best_fit

                offset_fit = fit + i * vertical_offset
                line, = ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')
                curves.append((line, k_parallel, offset_fit))

        ax.set_xlabel('k$_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'KDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on sliders
        ax.set_xlim(k_min, k_max)
        ax.set_ylim(0, 1 + (n - 1) * vertical_offset)
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity, curves

    def find_local_extremum(x, y, x_click, find_max=True):
        # Find the closest point to the click
        idx = np.argmin(np.abs(x - x_click))
        
        # Define the objective function (negative for finding maximum)
        def objective(idx):
            return -y[int(idx)] if find_max else y[int(idx)]
        
        # Use scipy's minimize_scalar to find the local extremum
        result = minimize_scalar(objective, bounds=(max(0, idx-50), min(len(x)-1, idx+50)), method='bounded')
        
        extremum_idx = int(result.x)
        return x[extremum_idx], y[extremum_idx]

    def on_click(event):
        if event.inaxes != ax:
            return

        for line, x, y in curves:
            if line.contains(event)[0]:
                if event.button == 1:  # Left click
                    x_max, y_max = find_local_extremum(x, y, event.xdata, find_max=True)
                    ax.plot(x_max, y_max, 'r^', markersize=10)
                    ax.annotate(f'Local Max: ({x_max:.2f}, {y_max:.2f})', (x_max, y_max), 
                                xytext=(5, 5), textcoords='offset points')
                elif event.button == 3:  # Right click
                    x_min, y_min = find_local_extremum(x, y, event.xdata, find_max=False)
                    ax.plot(x_min, y_min, 'gv', markersize=10)
                    ax.annotate(f'Local Min: ({x_min:.2f}, {y_min:.2f})', (x_min, y_min), 
                                xytext=(5, -15), textcoords='offset points')
                fig.canvas.draw_idle()
                break

    def update_plot(*args):
        nonlocal curves
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        _, curves = plot_edc(**current_values)

    interactive_plot = interactive(update_plot,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of KDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=1.05, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show KDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    fig, ax = plt.subplots(figsize=(10, 6))
    curves = []

    fig.canvas.mpl_connect('button_press_event', on_click)

    out = Output()

    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: fig.savefig(f"KDC_plot_scan_{interactive_plot.children[0].value}.png", dpi=300, bbox_inches='tight'))

    output = VBox([interactive_plot, out, save_button])

    display(output)
    plt.show()

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
from scipy.optimize import minimize_scalar

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced energy indices within the specified range
        energy_values = plot_data['energy_values']
        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        e_indices = valid_indices[e_indices]

        max_intensity = 0
        curves = []

        for i, e_index in enumerate(e_indices):
            actual_e = energy_values[e_index]
            kdc = plot_data['data_values'][e_index, :]
            k_parallel = plot_data['k_parallel'][0]

            # Filter k_parallel values within the specified range
            valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
            kdc = kdc[valid_k_indices]
            k_parallel = k_parallel[valid_k_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Normalize the curve to its maximum intensity
            kdc_max = np.max(kdc)
            kdc_normalized = kdc / kdc_max if kdc_max > 0 else kdc

            # Apply vertical offset
            offset_kdc = kdc_normalized + i * vertical_offset

            max_intensity = max(max_intensity, np.max(offset_kdc))

            if show_edc:
                line, = ax.plot(k_parallel, offset_kdc, label=f'E = {actual_e:.2f} eV')
                curves.append((line, k_parallel, offset_kdc))

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc_normalized)
                peak_heights = kdc_normalized[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc_normalized))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=k_parallel[peak], min=k_parallel.min(), max=k_parallel.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc_normalized, params, x=k_parallel)
                fit = result.best_fit

                offset_fit = fit + i * vertical_offset
                line, = ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')
                curves.append((line, k_parallel, offset_fit))

        ax.set_xlabel('k$_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'KDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on sliders
        ax.set_xlim(k_min, k_max)
        ax.set_ylim(0, 1 + (n - 1) * vertical_offset)
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity, curves

    def find_local_extremum(x, y, x_click, find_max=True):
        # Find the closest point to the click
        idx = np.argmin(np.abs(x - x_click))
        
        # Define the objective function (negative for finding maximum)
        def objective(idx):
            return -y[int(idx)] if find_max else y[int(idx)]
        
        # Use scipy's minimize_scalar to find the local extremum
        result = minimize_scalar(objective, bounds=(max(0, idx-50), min(len(x)-1, idx+50)), method='bounded')
        
        extremum_idx = int(result.x)
        return x[extremum_idx], y[extremum_idx]

    def on_click(event):
        if event.inaxes != ax:
            return

        for line, x, y in curves:
            if line.contains(event)[0]:
                if event.button == 1:  # Left click
                    x_max, y_max = find_local_extremum(x, y, event.xdata, find_max=True)
                    ax.plot(x_max, y_max, 'r^', markersize=10)
                    ax.annotate(f'Local Max: ({x_max:.2f}, {y_max:.2f})', (x_max, y_max), 
                                xytext=(5, 5), textcoords='offset points')
                elif event.button == 3:  # Right click
                    x_min, y_min = find_local_extremum(x, y, event.xdata, find_max=False)
                    ax.plot(x_min, y_min, 'gv', markersize=10)
                    ax.annotate(f'Local Min: ({x_min:.2f}, {y_min:.2f})', (x_min, y_min), 
                                xytext=(5, -15), textcoords='offset points')
                fig.canvas.draw_idle()
                break

    def update_plot(*args):
        nonlocal curves
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        _, curves = plot_edc(**current_values)

    interactive_plot = interactive(update_plot,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of KDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=1.05, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show KDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    fig, ax = plt.subplots(figsize=(10, 6))
    curves = []

    fig.canvas.mpl_connect('button_press_event', on_click)

    out = Output()

    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: fig.savefig(f"KDC_plot_scan_{interactive_plot.children[0].value}.png", dpi=300, bbox_inches='tight'))

    output = VBox([interactive_plot, out, save_button])

    display(output)
    plt.show()

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks, argrelextrema

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": False,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 6))
    plt.close(fig)  # Close the figure to prevent it from being displayed immediately

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced energy indices within the specified range
        energy_values = plot_data['energy_values']
        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        e_indices = valid_indices[e_indices]

        max_intensity = 0
        curves = []

        for i, e_index in enumerate(e_indices):
            actual_e = energy_values[e_index]
            kdc = plot_data['data_values'][e_index, :]
            k_parallel = plot_data['k_parallel'][0]

            # Filter k_parallel values within the specified range
            valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
            kdc = kdc[valid_k_indices]
            k_parallel = k_parallel[valid_k_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Normalize the curve to its maximum intensity
            kdc_max = np.max(kdc)
            kdc_normalized = kdc / kdc_max if kdc_max > 0 else kdc

            # Apply vertical offset
            offset_kdc = kdc_normalized + i * vertical_offset

            max_intensity = max(max_intensity, np.max(offset_kdc))

            if show_edc:
                line, = ax.plot(k_parallel, offset_kdc, label=f'E = {actual_e:.2f} eV')
                curves.append((line, k_parallel, offset_kdc))

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc_normalized)
                peak_heights = kdc_normalized[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc_normalized))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=k_parallel[peak], min=k_parallel.min(), max=k_parallel.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc_normalized, params, x=k_parallel)
                fit = result.best_fit

                offset_fit = fit + i * vertical_offset
                line, = ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')
                curves.append((line, k_parallel, offset_fit))

        ax.set_xlabel('k_parallel (Å^-1)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'KDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on sliders
        ax.set_xlim(k_min, k_max)
        ax.set_ylim(0, 1 + (n - 1) * vertical_offset)
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity, curves

    def find_local_extrema(x, y, x_click, find_max=True):
        # Find the closest point to the click
        idx = np.argmin(np.abs(x - x_click))
        
        # Use scipy's argrelextrema to find local extrema
        if find_max:
            extrema = argrelextrema(y, np.greater)[0]
        else:
            extrema = argrelextrema(y, np.less)[0]
        
        # Find the closest extremum to the click point
        if len(extrema) > 0:
            closest_extremum = extrema[np.argmin(np.abs(extrema - idx))]
            return x[closest_extremum], y[closest_extremum]
        else:
            return None, None

    def on_click(event):
        if event.inaxes != ax:
            return

        for line, x, y in curves:
            if line.contains(event)[0]:
                if event.button == 1:  # Left click
                    x_max, y_max = find_local_extrema(x, y, event.xdata, find_max=True)
                    if x_max is not None:
                        ax.plot(x_max, y_max, 'r^', markersize=10)
                        ax.annotate(f'Local Max: ({x_max:.2f}, {y_max:.2f})', (x_max, y_max), 
                                    xytext=(5, 5), textcoords='offset points')
                elif event.button == 3:  # Right click
                    x_min, y_min = find_local_extrema(x, y, event.xdata, find_max=False)
                    if x_min is not None:
                        ax.plot(x_min, y_min, 'gv', markersize=10)
                        ax.annotate(f'Local Min: ({x_min:.2f}, {y_min:.2f})', (x_min, y_min), 
                                    xytext=(5, -15), textcoords='offset points')
                fig.canvas.draw_idle()
                break

    fig.canvas.mpl_connect('button_press_event', on_click)

    def update_plot(*args):
        nonlocal curves
        current_values = {
            'scan_index': widgets['scan_index'].value,
            'n': widgets['n'].value,
            'vertical_offset': widgets['vertical_offset'].value,
            'show_edc': widgets['show_edc'].value,
            'show_fit': widgets['show_fit'].value,
            'num_peaks': widgets['num_peaks'].value,
            'k_min': widgets['k_min'].value,
            'k_max': widgets['k_max'].value,
            'e_min': widgets['e_min'].value,
            'e_max': widgets['e_max'].value
        }

        _, curves = plot_edc(**current_values)

    widgets = {
        'scan_index': IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        'n': IntSlider(value=5, min=1, max=20, step=1, description='Number of KDCs', continuous_update=True),
        'vertical_offset': FloatSlider(value=0.5, min=0.0, max=1.05, step=0.01, description='Vertical Offset', continuous_update=True),
        'show_edc': Checkbox(value=True, description='Show KDCs'),
        'show_fit': Checkbox(value=False, description='Show Fits'),
        'num_peaks': IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        'k_min': FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        'k_max': FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        'e_min': FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        'e_max': FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    }

    interactive_plot = interactive(update_plot, **widgets)

    curves = []

    out = Output()
    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: fig.savefig(f"KDC_plot_scan_{widgets['scan_index'].value}.png", dpi=300, bbox_inches='tight'))

    output = VBox([interactive_plot, out, save_button])

    display(output)
    display(fig.canvas)

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
from scipy.optimize import curve_fit

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def model_function(x, a, b, c, d):
    return d * np.log(1 + (np.exp(x - a) / (b * c)))

def optimize_curve_fit(x_data, y_data, initial_params):
    popt, _ = curve_fit(model_function, x_data, y_data, p0=initial_params)
    return popt

def calculate_error(y_true, y_pred):
    return np.mean((y_true - y_pred)**2)  # Mean squared error

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced energy indices within the specified range
        energy_values = plot_data['energy_values']
        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        e_indices = valid_indices[e_indices]

        max_intensity = 0

        for i, e_index in enumerate(e_indices):
            actual_e = energy_values[e_index]
            kdc = plot_data['data_values'][e_index, :]
            k_parallel = plot_data['k_parallel'][0]

            # Filter k_parallel values within the specified range
            valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
            kdc = kdc[valid_k_indices]
            k_parallel = k_parallel[valid_k_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Normalize the curve to its maximum intensity
            kdc_max = np.max(kdc)
            kdc_normalized = kdc / kdc_max if kdc_max > 0 else kdc

            # Apply vertical offset
            offset_kdc = kdc_normalized + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))

            if show_edc:
                ax.plot(k_parallel, offset_kdc, label=f'E = {actual_e:.2f} eV')

            if show_fit:
                # Fit KDC with optimized curve fitting
                initial_params = [np.mean(k_parallel), 1, 1, 1]  # Initial guess for [a, b, c, d]
                optimized_params = optimize_curve_fit(k_parallel, kdc_normalized, initial_params)
                fit = model_function(k_parallel, *optimized_params)
                offset_fit = fit + i * vertical_offset
                ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')

                # Calculate and print error
                error = calculate_error(kdc_normalized, fit)
                print(f"Fitting error for E = {actual_e:.2f} eV: {error:.4f}")

        ax.set_xlabel('k$_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'KDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(k_min, k_max)
        ax.set_ylim(0, 1 + (n - 1) * vertical_offset)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"KDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of KDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=1.05, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show KDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = 0

        for i, k_index in enumerate(k_indices):
            actual_k = plot_data['k_parallel'][0, k_index]
            edc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_energy_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            edc = edc[valid_energy_indices]
            energy_values = energy_values[valid_energy_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(edc, xr.DataArray):
                edc = edc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Normalize the curve to its maximum intensity
            edc_max = np.max(edc)
            edc_normalized = edc / edc_max if edc_max > 0 else edc

            # Apply vertical offset (n times the offset for nth curve)
            offset_edc = edc_normalized + i * vertical_offset

            max_intensity = max(max_intensity, np.max(offset_edc))

            if show_edc:
                ax.plot(energy_values, offset_edc, label=f'k = {actual_k:.2f} Å$^{{-1}}$')

            if show_fit:
                # Fit EDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(edc_normalized)
                peak_heights = edc_normalized[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(edc_normalized))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(edc_normalized, params, x=energy_values)
                fit = result.best_fit

                # Apply vertical offset to the fit
                offset_fit = fit + i * vertical_offset
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k = {actual_k:.2f} Å$^{{-1}}$', color=f'C{i}')

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        # Set axis limits based on data and vertical offset
        ax.set_xlim(e_min, e_max)
        ax.set_ylim(0, 1 + (n - 1) * vertical_offset)
        
        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=1.05, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])

    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced energy indices within the specified range
        energy_values = plot_data['energy_values']
        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        e_indices = valid_indices[e_indices]

        max_intensity = 0

        for i, e_index in enumerate(e_indices):
            actual_e = energy_values[e_index]
            kdc = plot_data['data_values'][e_index, :]
            k_parallel = plot_data['k_parallel'][0]

            # Filter k_parallel values within the specified range
            valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
            kdc = kdc[valid_k_indices]
            k_parallel = k_parallel[valid_k_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Normalize the curve to its maximum intensity
            kdc_max = np.max(kdc)
            kdc_normalized = kdc / kdc_max if kdc_max > 0 else kdc

            # Apply vertical offset
            offset_kdc = kdc_normalized + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))

            if show_edc:
                ax.plot(k_parallel, offset_kdc, label=f'E = {actual_e:.2f} eV')

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc_normalized)
                peak_heights = kdc_normalized[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc_normalized))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=k_parallel[peak], min=k_parallel.min(), max=k_parallel.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc_normalized, params, x=k_parallel)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')

        ax.set_xlabel('k$_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'KDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(k_min, k_max)
        ax.set_ylim(0, 1 + (n - 1) * vertical_offset)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"KDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of KDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=1.05, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show KDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced energy indices within the specified range
        energy_values = plot_data['energy_values']
        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        e_indices = valid_indices[e_indices]

        max_intensity = 0

        for i, e_index in enumerate(e_indices):
            actual_e = energy_values[e_index]
            kdc = plot_data['data_values'][e_index, :]
            k_parallel = plot_data['k_parallel'][0]

            # Filter k_parallel values within the specified range
            valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
            kdc = kdc[valid_k_indices]
            k_parallel = k_parallel[valid_k_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Normalize the curve to its maximum intensity
            kdc_max = np.max(kdc)
            kdc_normalized = kdc / kdc_max if kdc_max > 0 else kdc

            # Apply vertical offset
            offset_kdc = kdc_normalized + i * vertical_offset

            max_intensity = max(max_intensity, np.max(offset_kdc))

            if show_edc:
                ax.plot(k_parallel, offset_kdc, label=f'E = {actual_e:.2f} eV')

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc_normalized)
                peak_heights = kdc_normalized[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc_normalized))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=k_parallel[peak], min=k_parallel.min(), max=k_parallel.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc_normalized, params, x=k_parallel)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset

                ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(k_parallel[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(k_parallel[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('k$_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'KDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(k_min, k_max)
        ax.set_ylim(0, 1 + (n - 1) * vertical_offset)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"KDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of KDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=1.05, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show KDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16 # eV*s
    m_e = 9.1093837015e-31 # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global k_parallel and energy ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Calculate equally spaced energy indices within the specified range
        energy_values = plot_data['energy_values']
        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        e_indices = valid_indices[e_indices]

        max_intensity = 0

        for i, e_index in enumerate(e_indices):
            actual_e = energy_values[e_index]
            kdc = plot_data['data_values'][e_index, :]
            k_parallel = plot_data['k_parallel'][0]

            # Filter k_parallel values within the specified range
            valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
            kdc = kdc[valid_k_indices]
            k_parallel = k_parallel[valid_k_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Normalize the curve to its maximum intensity
            kdc_max = np.max(kdc)
            kdc_normalized = kdc / kdc_max if kdc_max > 0 else kdc

            # Apply vertical offset
            offset_kdc = kdc_normalized + i * vertical_offset

            max_intensity = max(max_intensity, np.max(offset_kdc))

            if show_edc:
                ax.plot(k_parallel, offset_kdc, label=f'E = {actual_e:.2f} eV')

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc_normalized)
                peak_heights = kdc_normalized[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc_normalized))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=k_parallel[peak], min=k_parallel.min(), max=k_parallel.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc_normalized, params, x=k_parallel)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset

                ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(k_parallel[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(k_parallel[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('k$_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'KDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(k_min, k_max)
        ax.set_ylim(0, 1 + (n - 1) * vertical_offset)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)
        filename = f"KDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def update_vertical_offset_range(*args):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value
        }

        plot_edc(**current_values)

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of KDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=1.05, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show KDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True)
    )

    # Observe changes in all sliders
    for child in interactive_plot.children:
        if isinstance(child, FloatSlider) or isinstance(child, IntSlider):
            child.observe(update_vertical_offset_range, names='value')

    out = Output()

    def display_plot(*args):
        with out:
            clear_output(wait=True)
            plot_edc(interactive_plot.children[0].value,
                     interactive_plot.children[1].value,
                     interactive_plot.children[2].value,
                     interactive_plot.children[3].value,
                     interactive_plot.children[4].value,
                     interactive_plot.children[5].value,
                     interactive_plot.children[6].value,
                     interactive_plot.children[7].value,
                     interactive_plot.children[8].value,
                     interactive_plot.children[9].value)

    interactive_plot.observe(display_plot, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, out, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    fig, ax = plt.subplots(figsize=(10, 6))
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
