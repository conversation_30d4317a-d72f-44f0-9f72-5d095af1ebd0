# IPython log file

import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # Create a custom normalization
    norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
    # Create a colormap with the specified range
    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
    cmap = ListedColormap(cmap_array)
    
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
    ax.set_ylabel(r'$E-E_f$ (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),
                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # Create a custom normalization
    norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
    # Create a colormap with the specified range
    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
    cmap = ListedColormap(cmap_array)
    
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
    ax.set_ylabel(r'$E-E_f$ (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),
                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))
interactive_plot
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.0": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\nd\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 398)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.0.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.0.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.1": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef c\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 402)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.1.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.1.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.2": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("impocallable", 12, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.2.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.2.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.3": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("impocollections", 15, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.3.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.3.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.4": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("impoconfig", 10, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.4.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.4.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.5": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("impoconversion", 14, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.5.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.5.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.6": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef convert\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 408)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.6.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.6.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.7": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef convert_\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 409)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.7.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.7.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.8": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef convert_t\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 410)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.8.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.8.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.9": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef convert_to\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 411)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.9.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.9.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.10": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef k\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 402)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.10.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.10.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.11": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("impok_parallel", 14, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.11.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.11.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.12": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef ks\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 403)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.12.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.12.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.13": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef ksp\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 404)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.13.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.13.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.14": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef kspa\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 405)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.14.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.14.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.15": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef kspac\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 406)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.15.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.15.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.16": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef kspace(d)\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 409)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.16.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.16.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.17": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("import numpdata", 15, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.17.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.17.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.18": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef kspace(datas)\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 413)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.18.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.18.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.19": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef kspace(dataset)\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 415)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.19.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.19.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.20": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef kspace(dataset, w)\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 418)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.20.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.20.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.21": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("import numpy as np\niwarnings", 28, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.21.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.21.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.22": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("import numpy as np\niwork_function", 33, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.22.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.22.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.23": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef kspace(dataset, work_function):\n    data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n    E_photon = data.attrs['hv']  # Photon energy from the attributes\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n    # Calculate kinetic energy and momentum\n    E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n    k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n    # Get the energy values\n    energy_values = -data.eV.values  # Negative energy values\n\n    ## Define the plotting function\n    def plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title('ARPES Data in Momentum Space')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Create the interactive widget\n    max_data_value = np.max(data.values)\n    interactive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\n    interactive_plot\n    data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n    # Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plotdata = read_single_pxt(d)\n", 4784)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.23.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.23.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.24": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef kspace(dataset, work_function):\n    data = read_single_pxt(d)\n\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n    E_photon = data.attrs['hv']  # Photon energy from the attributes  \n    # Calculate kinetic energy and momentum\n    E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n    k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n    # Get the energy values\n    energy_values = -data.eV.values  # Negative energy values\n\n    ## Define the plotting function\n    def plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title('ARPES Data in Momentum Space')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Create the interactive widget\n    max_data_value = np.max(data.values)\n    interactive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\n    interactive_plot\n    data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n", 461)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.24.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.24.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.25": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef kspace(dataset, work_function):\n    data = read_single_pxt(da)\n\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n    E_photon = data.attrs['hv']  # Photon energy from the attributes  \n    # Calculate kinetic energy and momentum\n    E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n    k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n    # Get the energy values\n    energy_values = -data.eV.values  # Negative energy values\n\n    ## Define the plotting function\n    def plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title('ARPES Data in Momentum Space')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Create the interactive widget\n    max_data_value = np.max(data.values)\n    interactive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\n    interactive_plot\n    data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n", 462)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.25.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.25.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128
def kspace(dataset, work_function):
    data = read_single_pxt(dataset)

    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg
    E_photon = data.attrs['hv']  # Photon energy from the attributes  
    # Calculate kinetic energy and momentum
    E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
    k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

    # Get the energy values
    energy_values = -data.eV.values  # Negative energy values

    ## Define the plotting function
    def plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):
        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title('ARPES Data in Momentum Space')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        plt.show()

    # Create the interactive widget
    max_data_value = np.max(data.values)
    interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),
                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))
    interactive_plot
    data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')

def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.26": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("ks", 2)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.26.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.26.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.27": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef kspace(dataset, work_function):\n    data = read_single_pxt(dataset)\n\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n    E_photon = data.attrs['hv']  # Photon energy from the attributes  \n    # Calculate kinetic energy and momentum\n    E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n    k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n    # Get the energy values\n    energy_values = -data.eV.values  # Negative energy values\n\n    ## Define the plotting function\n    def plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title('ARPES Data in Momentum Space')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Create the interactive widget\n    max_data_value = np.max(data.values)\n    interactive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\n    interactive_plot\n    data1 = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt')\n\n", 2492)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.27.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.27.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128
def kspace(dataset, work_function):
    data = read_single_pxt(dataset)

    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg
    E_photon = data.attrs['hv']  # Photon energy from the attributes  
    # Calculate kinetic energy and momentum
    E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
    k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

    # Get the energy values
    energy_values = -data.eV.values  # Negative energy values

    ## Define the plotting function
    def plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):
        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title('ARPES Data in Momentum Space')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        plt.show()

    # Create the interactive widget
    max_data_value = np.max(data.values)
    interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),
                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))
    interactive_plot
    data1 = '/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt'

def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.28": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("kspace(d)", 8)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.28.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.28.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.29": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("kspace(data1)", 12)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.29.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.29.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
kspace(data1)
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128
def kspace(dataset, work_function):
    data = read_single_pxt(dataset)

    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg
    E_photon = data.attrs['hv']  # Photon energy from the attributes  
    # Calculate kinetic energy and momentum
    E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
    k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

    # Get the energy values
    energy_values = -data.eV.values  # Negative energy values

    ## Define the plotting function
    def plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):
        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title('ARPES Data in Momentum Space')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        plt.show()

    # Create the interactive widget
    max_data_value = np.max(data.values)
    interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),
                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))
    interactive_plot
data1 = '/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt'

kspace(data1)
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.30": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("kspace(data1,4.)", 15)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.30.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.30.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
kspace(data1,4.5)
kspace(data1,4.5)
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.31": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\ndef kspace(dataset, work_function):\n    data = read_single_pxt(dataset)\n\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n    E_photon = data.attrs['hv']  # Photon energy from the attributes  \n    # Calculate kinetic energy and momentum\n    E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n    k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n    # Get the energy values\n    energy_values = -data.eV.values  # Negative energy values\n\n    ## Define the plotting function\n    def plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title('ARPES Data in Momentum Space')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Create the interactive widget\n    max_data_value = np.max(data.values)\n    interactive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\n    rinteractive_plot\ndata1 = '/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt'\n\n", 2467)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.31.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.31.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128
def kspace(dataset, work_function):
    data = read_single_pxt(dataset)

    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg
    E_photon = data.attrs['hv']  # Photon energy from the attributes  
    # Calculate kinetic energy and momentum
    E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
    k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

    # Get the energy values
    energy_values = -data.eV.values  # Negative energy values

    ## Define the plotting function
    def plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):
        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title('ARPES Data in Momentum Space')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        plt.show()

    # Create the interactive widget
    max_data_value = np.max(data.values)
    interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),
                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))
    return interactive_plot
data1 = '/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_097_S032.pxt'

kspace(data1,4.5)
kspace(data1,100)
kspace(data1,8)
kspace(data1,80)
kspace(data1,20)
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.32": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("kspace(data1,4.)", 15)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.32.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.32.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
kspace(data1,4.5)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    def plot_arpes(file_index, vmin, vmax, scale, cmap_start, cmap_end):
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   file_index=IntSlider(value=0, min=0, max=len(data_files)-1, step=1, description='File Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    def plot_arpes(file_index, vmin, vmax, scale, cmap_start, cmap_end):
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   file_index=IntSlider(value=0, min=0, max=len(data_files)-1, step=1, description='File Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib.cbook import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def kspace(data_files, work_function):
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib.cbook import matplotlib.MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def kspace(data_files, work_function):
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def kspace(data_files, work_function):
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.33": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n\ndef kspace(data_files, work_function):\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        file_index = scan_index - 1  # Convert scan_index to zero-based index\n        data = read_single_pxt(data_files[file_index])\n\n        # Constants\n        hbar = 6.582119569e-16  # eV*s\n        m_e = 9.1093837015e-31  # kg\n        E_photon = data.attrs['hv']  # Photon energy from the attributes  \n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.n(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Get the maximum data value across all files\n    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),\n                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))\n    return interactive_plot\n\n# Usage\nfolder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\ndata_files = load_data_files(folder_path)\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\ninteractive_plot = kspace(data_files, work_function)\ninteractive_plot", 1806)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.33.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.33.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.34": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n\ndef kspace(data_files, work_function):\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        file_index = scan_index - 1  # Convert scan_index to zero-based index\n        data = read_single_pxt(data_files[file_index])\n\n        # Constants\n        hbar = 6.582119569e-16  # eV*s\n        m_e = 9.1093837015e-31  # kg\n        E_photon = data.attrs['hv']  # Photon energy from the attributes  \n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.ni(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Get the maximum data value across all files\n    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),\n                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))\n    return interactive_plot\n\n# Usage\nfolder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\ndata_files = load_data_files(folder_path)\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\ninteractive_plot = kspace(data_files, work_function)\ninteractive_plot", 1807)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.34.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.34.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.35": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n\ndef kspace(data_files, work_function):\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        file_index = scan_index - 1  # Convert scan_index to zero-based index\n        data = read_single_pxt(data_files[file_index])\n\n        # Constants\n        hbar = 6.582119569e-16  # eV*s\n        m_e = 9.1093837015e-31  # kg\n        E_photon = data.attrs['hv']  # Photon energy from the attributes  \n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.nip(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Get the maximum data value across all files\n    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),\n                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))\n    return interactive_plot\n\n# Usage\nfolder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\ndata_files = load_data_files(folder_path)\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\ninteractive_plot = kspace(data_files, work_function)\ninteractive_plot", 1808)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.35.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.35.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.36": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n\ndef kspace(data_files, work_function):\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        file_index = scan_index - 1  # Convert scan_index to zero-based index\n        data = read_single_pxt(data_files[file_index])\n\n        # Constants\n        hbar = 6.582119569e-16  # eV*s\n        m_e = 9.1093837015e-31  # kg\n        E_photon = data.attrs['hv']  # Photon energy from the attributes  \n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.nipy(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Get the maximum data value across all files\n    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),\n                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))\n    return interactive_plot\n\n# Usage\nfolder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\ndata_files = load_data_files(folder_path)\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\ninteractive_plot = kspace(data_files, work_function)\ninteractive_plot", 1809)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.36.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.36.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.37": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n\ndef kspace(data_files, work_function):\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        file_index = scan_index - 1  # Convert scan_index to zero-based index\n        data = read_single_pxt(data_files[file_index])\n\n        # Constants\n        hbar = 6.582119569e-16  # eV*s\n        m_e = 9.1093837015e-31  # kg\n        E_photon = data.attrs['hv']  # Photon energy from the attributes  \n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.nipy_(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Get the maximum data value across all files\n    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),\n                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))\n    return interactive_plot\n\n# Usage\nfolder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\ndata_files = load_data_files(folder_path)\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\ninteractive_plot = kspace(data_files, work_function)\ninteractive_plot", 1810)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.37.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.37.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.38": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n\ndef kspace(data_files, work_function):\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        file_index = scan_index - 1  # Convert scan_index to zero-based index\n        data = read_single_pxt(data_files[file_index])\n\n        # Constants\n        hbar = 6.582119569e-16  # eV*s\n        m_e = 9.1093837015e-31  # kg\n        E_photon = data.attrs['hv']  # Photon energy from the attributes  \n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.nipy_s(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Get the maximum data value across all files\n    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),\n                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))\n    return interactive_plot\n\n# Usage\nfolder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\ndata_files = load_data_files(folder_path)\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\ninteractive_plot = kspace(data_files, work_function)\ninteractive_plot", 1811)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.38.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.38.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.39": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n\ndef kspace(data_files, work_function):\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        file_index = scan_index - 1  # Convert scan_index to zero-based index\n        data = read_single_pxt(data_files[file_index])\n\n        # Constants\n        hbar = 6.582119569e-16  # eV*s\n        m_e = 9.1093837015e-31  # kg\n        E_photon = data.attrs['hv']  # Photon energy from the attributes  \n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.nipy_sp(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Get the maximum data value across all files\n    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),\n                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))\n    return interactive_plot\n\n# Usage\nfolder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\ndata_files = load_data_files(folder_path)\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\ninteractive_plot = kspace(data_files, work_function)\ninteractive_plot", 1812)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.39.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.39.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.40": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n\ndef kspace(data_files, work_function):\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        file_index = scan_index - 1  # Convert scan_index to zero-based index\n        data = read_single_pxt(data_files[file_index])\n\n        # Constants\n        hbar = 6.582119569e-16  # eV*s\n        m_e = 9.1093837015e-31  # kg\n        E_photon = data.attrs['hv']  # Photon energy from the attributes  \n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.nipy_spe(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Get the maximum data value across all files\n    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),\n                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))\n    return interactive_plot\n\n# Usage\nfolder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\ndata_files = load_data_files(folder_path)\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\ninteractive_plot = kspace(data_files, work_function)\ninteractive_plot", 1813)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.40.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.40.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.41": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n\ndef kspace(data_files, work_function):\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        file_index = scan_index - 1  # Convert scan_index to zero-based index\n        data = read_single_pxt(data_files[file_index])\n\n        # Constants\n        hbar = 6.582119569e-16  # eV*s\n        m_e = 9.1093837015e-31  # kg\n        E_photon = data.attrs['hv']  # Photon energy from the attributes  \n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.nipy_spec(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Get the maximum data value across all files\n    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),\n                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))\n    return interactive_plot\n\n# Usage\nfolder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\ndata_files = load_data_files(folder_path)\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\ninteractive_plot = kspace(data_files, work_function)\ninteractive_plot", 1814)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.41.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.41.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.42": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n\ndef kspace(data_files, work_function):\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        file_index = scan_index - 1  # Convert scan_index to zero-based index\n        data = read_single_pxt(data_files[file_index])\n\n        # Constants\n        hbar = 6.582119569e-16  # eV*s\n        m_e = 9.1093837015e-31  # kg\n        E_photon = data.attrs['hv']  # Photon energy from the attributes  \n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.nipy_spect(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Get the maximum data value across all files\n    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),\n                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))\n    return interactive_plot\n\n# Usage\nfolder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\ndata_files = load_data_files(folder_path)\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\ninteractive_plot = kspace(data_files, work_function)\ninteractive_plot", 1815)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.42.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.42.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.43": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n\ndef kspace(data_files, work_function):\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        file_index = scan_index - 1  # Convert scan_index to zero-based index\n        data = read_single_pxt(data_files[file_index])\n\n        # Constants\n        hbar = 6.582119569e-16  # eV*s\n        m_e = 9.1093837015e-31  # kg\n        E_photon = data.attrs['hv']  # Photon energy from the attributes  \n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.nipy_spectr(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Get the maximum data value across all files\n    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),\n                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))\n    return interactive_plot\n\n# Usage\nfolder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\ndata_files = load_data_files(folder_path)\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\ninteractive_plot = kspace(data_files, work_function)\ninteractive_plot", 1816)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.43.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.43.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.44": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n\ndef kspace(data_files, work_function):\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        file_index = scan_index - 1  # Convert scan_index to zero-based index\n        data = read_single_pxt(data_files[file_index])\n\n        # Constants\n        hbar = 6.582119569e-16  # eV*s\n        m_e = 9.1093837015e-31  # kg\n        E_photon = data.attrs['hv']  # Photon energy from the attributes  \n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.nipy_spectra(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Get the maximum data value across all files\n    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),\n                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))\n    return interactive_plot\n\n# Usage\nfolder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\ndata_files = load_data_files(folder_path)\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\ninteractive_plot = kspace(data_files, work_function)\ninteractive_plot", 1817)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.44.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.44.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.45": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\n\ndef kspace(data_files, work_function):\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        file_index = scan_index - 1  # Convert scan_index to zero-based index\n        data = read_single_pxt(data_files[file_index])\n\n        # Constants\n        hbar = 6.582119569e-16  # eV*s\n        m_e = 9.1093837015e-31  # kg\n        E_photon = data.attrs['hv']  # Photon energy from the attributes  \n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.nipy_spectral(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n        fig.colorbar(im, ax=ax, label='Intensity')\n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n        ax.set_ylabel(r'$E-E_f$ (eV)')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')\n        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n        plt.show()\n\n    # Get the maximum data value across all files\n    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),\n                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))\n    return interactive_plot\n\n# Usage\nfolder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\ndata_files = load_data_files(folder_path)\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\ninteractive_plot = kspace(data_files, work_function)\ninteractive_plot", 1818)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.45.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.45.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def kspace(data_files, work_function):
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.nipy_spectral(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def kspace(data_files, work_function):
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def kspace(data_files, work_function):
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 512))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def kspace(data_files, work_function):
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 1024))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
import warnings
from matplotlib.cbook import MatplotlibDeprecationWarning

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress the specific warning
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def kspace(data_files, work_function):
    fig, ax = plt.subplots(figsize=(10, 8))
    output = Output()
    
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        ax.clear()  # Clear the previous plot
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        plt.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        
        with output:
            output.clear_output(wait=True)
            plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    
    # Display the output and interactive controls
    display(output, interactive_plot)

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

kspace(data_files, work_function)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
import warnings
from matplotlib.cbook import MatplotlibDeprecationWarning

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress the specific warning
#warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def kspace(data_files, work_function):
    fig, ax = plt.subplots(figsize=(10, 8))
    output = Output()
    
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        ax.clear()  # Clear the previous plot
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        plt.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        
        with output:
            output.clear_output(wait=True)
            plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    
    # Display the output and interactive controls
    display(output, interactive_plot)

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

kspace(data_files, work_function)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
import warnings

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    fig, ax = plt.subplots(figsize=(10, 8))
    im = ax.pcolormesh([], [], [])
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
    ax.set_ylabel(r'$E-E_f$ (eV)')
    plt.close(fig)  # Prevent the empty figure from displaying

    out = Output()

    @out.capture(clear_output=True)
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        # Clear the previous plot
        ax.clear()
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        fig.canvas.draw_idle()
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    
    return interactive_plot, out

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot, out = kspace(data_files, work_function)
display(interactive_plot, out)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
import warnings

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    fig, ax = plt.subplots(figsize=(10, 8))
    plt.close(fig)  # Prevent the empty figure from displaying

    out = Output()

    @out.capture(clear_output=True)
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        # Clear the previous plot
        ax.clear()
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        fig.canvas.draw_idle()
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    
    return interactive_plot, out

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot, out = kspace(data_files, work_function)
display(interactive_plot, out)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
import warnings

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

#warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    fig, ax = plt.subplots(figsize=(10, 8))
    plt.close(fig)  # Prevent the empty figure from displaying

    out = Output()

    @out.capture(clear_output=True)
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        # Clear the previous plot
        ax.clear()
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        fig.canvas.draw_idle()
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    
    return interactive_plot, out

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot, out = kspace(data_files, work_function)
display(interactive_plot, out)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
import warnings

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    fig, ax = plt.subplots(figsize=(10, 8))
    plt.close(fig)  # Prevent the empty figure from displaying

    out = Output()

    @out.capture(clear_output=True)
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        # Clear the previous plot
        ax.clear()
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        fig.canvas.draw_idle()
        display(fig)
        plt.close(fig)  # Close the figure to prevent it from being displayed twice

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    
    return interactive_plot, out

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot, out = kspace(data_files, work_function)
display(interactive_plot, out)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
import warnings

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    fig, ax = plt.subplots(figsize=(10, 8))
    im = ax.pcolormesh([], [], [])  # Create an empty pcolormesh
    cbar = fig.colorbar(im, ax=ax, label='Intensity')
    
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        # Update the existing pcolormesh
        im.set_array(None)
        im.set_edgecolors('none')
        im.set_cmap(cmap)
        im.set_norm(norm)
        im.set_offsets(np.c_[k_parallel.ravel(), energy_values.ravel()])
        im.set_array(data.values.ravel())

        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_xlim(k_parallel.min(), k_parallel.max())
        ax.set_ylim(energy_values.min(), energy_values.max())

        # Update colorbar
        cbar.update_normal(im)
        
        fig.canvas.draw_idle()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    
    return interactive_plot, fig

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot, fig = kspace(data_files, work_function)
display(interactive_plot)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
import warnings

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Load the first file to initialize the plot
    initial_data = read_single_pxt(data_files[0])
    
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg
    E_photon = initial_data.attrs['hv']  # Photon energy from the attributes  

    # Calculate kinetic energy and momentum for initial data
    E_b = work_function + np.abs(initial_data.eV) - E_photon  # Negative E_b values
    k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(initial_data.phi)) / hbar

    # Get the energy values for initial data
    energy_values = -initial_data.eV.values  # Negative energy values

    fig, ax = plt.subplots(figsize=(10, 8))
    im = ax.pcolormesh(k_parallel, energy_values, initial_data.values, shading='auto')
    cbar = fig.colorbar(im, ax=ax, label='Intensity')
    
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        # Update the existing pcolormesh
        im.set_array(None)
        im.set_edgecolors('none')
        im.set_cmap(cmap)
        im.set_norm(norm)
        im.set_offsets(np.c_[k_parallel.ravel(), energy_values.ravel()])
        im.set_array(data.values.ravel())

        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_xlim(k_parallel.min(), k_parallel.max())
        ax.set_ylim(energy_values.min(), energy_values.max())

        # Update colorbar
        cbar.update_normal(im)
        
        fig.canvas.draw_idle()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    
    return interactive_plot, fig

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot, fig = kspace(data_files, work_function)
display(interactive_plot)
plt.show()
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
import warnings

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Load the first file to initialize the plot
    initial_data = read_single_pxt(data_files[0])
    
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg
    E_photon = initial_data.attrs['hv']  # Photon energy from the attributes  

    # Calculate kinetic energy and momentum for initial data
    E_b = work_function + np.abs(initial_data.eV) - E_photon  # Negative E_b values
    k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(initial_data.phi))
    k_parallel = k_parallel.values / hbar  # Convert to numpy array and divide by hbar

    # Get the energy values for initial data
    energy_values = -initial_data.eV.values  # Negative energy values

    fig, ax = plt.subplots(figsize=(10, 8))
    im = ax.pcolormesh(k_parallel, energy_values, initial_data.values, shading='auto')
    cbar = fig.colorbar(im, ax=ax, label='Intensity')
    
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi))
        k_parallel = k_parallel.values / hbar  # Convert to numpy array and divide by hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        # Update the existing pcolormesh
        im.set_array(None)
        im.set_edgecolors('none')
        im.set_cmap(cmap)
        im.set_norm(norm)
        im.set_offsets(np.c_[k_parallel.ravel(), energy_values.ravel()])
        im.set_array(data.values.ravel())

        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_xlim(k_parallel.min(), k_parallel.max())
        ax.set_ylim(energy_values.min(), energy_values.max())

        # Update colorbar
        cbar.update_normal(im)
        
        fig.canvas.draw_idle()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    
    return interactive_plot, fig

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot, fig = kspace(data_files, work_function)
display(interactive_plot)
plt.show()
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
import warnings

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Load the first file to initialize the plot
    initial_data = read_single_pxt(data_files[0])
    
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg
    E_photon = initial_data.attrs['hv']  # Photon energy from the attributes  

    # Calculate kinetic energy and momentum for initial data
    E_b = work_function + np.abs(initial_data.eV) - E_photon  # Negative E_b values
    k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(initial_data.phi))
    k_parallel = k_parallel.values / hbar  # Convert to numpy array and divide by hbar

    # Get the energy values for initial data
    energy_values = -initial_data.eV.values  # Negative energy values

    fig, ax = plt.subplots(figsize=(10, 8))
    im = ax.pcolormesh(k_parallel, energy_values, initial_data.values, shading='auto')
    cbar = fig.colorbar(im, ax=ax, label='Intensity')
    
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi))
        k_parallel = k_parallel.values / hbar  # Convert to numpy array and divide by hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        # Create meshgrid
        K, E = np.meshgrid(k_parallel, energy_values)

        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        # Update the existing pcolormesh
        im.set_array(None)
        im.set_edgecolors('none')
        im.set_cmap(cmap)
        im.set_norm(norm)
        im.set_offsets(np.c_[K.ravel(), E.ravel()])
        im.set_array(data.values.ravel())

        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_xlim(k_parallel.min(), k_parallel.max())
        ax.set_ylim(energy_values.min(), energy_values.max())

        # Update colorbar
        cbar.update_normal(im)
        
        fig.canvas.draw_idle()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    
    return interactive_plot, fig

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot, fig = kspace(data_files, work_function)
display(interactive_plot)
plt.show()
