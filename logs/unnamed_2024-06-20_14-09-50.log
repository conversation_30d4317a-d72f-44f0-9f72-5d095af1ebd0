# IPython log file

from arpes.io import load_data
from arpes.endstations.plugin import *
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = complex
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S009.pxt', location='BL4')
from arpes.io import load_data
from arpes.endstations.plugin import *
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = complex
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')
from arpes.io import load_data
from arpes.endstations.plugin import *
import numpy as np
#if not hasattr(np, 'complex'):
    #np.complex = complex
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')
