# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from ipywidgets import Dropdown, VBox, Output
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
import matplotlib.pyplot as plt
from ipywidgets import Output, VBox, Dropdown
from matplotlib.backends.backend_agg import FigureCanvasAgg
from IPython.display import display, clear_output

#%matplotlib widget
# Suppress warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128


class ARPESAnalysisSuite:
    def __init__(self):
        self.data_files = []
        self.work_function = 4.5
        self.plot_modules = {}
        self.filter_modules = {}
        self.dft_data = []
        self.current_plot = None
        self.plot_output = Output()
        self.fig, self.ax = plt.subplots()
        self.canvas = FigureCanvasAgg(self.fig)

    def load_data_files(self):
        root = tk.Tk()
        root.withdraw()
        folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
        root.destroy()
        if folder_path:
            self.data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
        else:
            print("No folder selected.")

    def add_plot_module(self, name, module_class):
        self.plot_modules[name] = module_class

    def add_filter_module(self, name, module):
        self.filter_modules[name] = module

    def run(self):
        if not self.data_files:
            print("No data files loaded. Please load data files first.")
            return
        
        plot_selector = self.create_plot_selector()
        main_interface = VBox([plot_selector, self.plot_output])
        display(main_interface)
        self.update_plot(list(self.plot_modules.keys())[0])

    def create_plot_selector(self):
        plot_options = list(self.plot_modules.keys())
        plot_dropdown = Dropdown(options=plot_options, description='Plot Type:')
        plot_dropdown.observe(self.on_plot_change, names='value')
        return plot_dropdown

    def on_plot_change(self, change):
        self.update_plot(change.new)

    def update_plot(self, plot_type):
        with self.plot_output:
            clear_output(wait=True)
            self.ax.clear()
            
            if plot_type in self.plot_modules:
                module_class = self.plot_modules[plot_type]
                module_instance = module_class(self.data_files, self.work_function)
                
                self.current_plot = module_instance.run()
                self.ax.add_artist(self.current_plot)
                
            self.canvas.draw()
            display(self.fig)
class ARPESPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.interactive_widgets = {}
        self.filter_widgets = {}
        self.dft_data = []
        self.scan_types = self.determine_scan_type()
        self.rainbow_light = self.rainbowlightct()
        self.add_filter_widget('canny_enabled', Checkbox(value=False, description='Canny Edge Detection'))
        self.add_filter_widget('moving_average_enabled', Checkbox(value=False, description='Moving Average'))
        self.preprocess_data()  # Call this before initialize_widgets
        self.initialize_widgets()

    def initialize_widgets(self):
        self.add_interactive_widget('scan_index', IntSlider(
            value=1, min=1, max=len(self.data_files), step=1,
            description='Scan Index', continuous_update=False,
            layout=Layout(width='50%')
        ))
        self.add_interactive_widget('vmin', FloatSlider(
            value=self.min_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Min Value',
            continuous_update=False
        ))
        self.add_interactive_widget('vmax', FloatSlider(
            value=self.max_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Max Value',
            continuous_update=False
        ))
        self.add_interactive_widget('scale', FloatSlider(
            value=1.0, min=0.1, max=2.0, step=0.1,
            description='Scale', continuous_update=False
        ))
        self.add_interactive_widget('sigma', FloatSlider(
            value=1.0, min=0.1, max=20.0, step=0.1,
            description='Canny Sigma', continuous_update=False
        ))
        self.add_interactive_widget('low_threshold', FloatSlider(
            value=0.1, min=0.0, max=1.0, step=0.01,
            description='Canny Low Threshold', continuous_update=False
        ))
        self.add_interactive_widget('high_threshold', FloatSlider(
            value=0.2, min=0.0, max=1.0, step=0.01,
            description='Canny High Threshold', continuous_update=False
        ))
        self.add_interactive_widget('moving_average_size', IntSlider(
            value=3, min=3, max=50, step=1,
            description='Moving Average Size', continuous_update=False
        ))

    def rainbowlightct(self):
        # Normalize the RGB values to the range 0-1
        normalized_data = igor_data / 65535.0
        
        # Create the ListedColormap
        return ListedColormap(normalized_data)


    def determine_scan_type(self):
        scan_types = []
        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            if 'polar' in data.attrs:
                scan_types.append(('polar', data.attrs['polar']))
            elif 'hv' in data.attrs:
                scan_types.append(('hv', data.attrs['hv']))
            else:
                scan_types.append(('unknown', None))
        return scan_types

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10
            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })
            self.max_data_value = max(self.max_data_value, np.max(data.values))

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap = self.rainbowlightct(), norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, canny_enabled, sigma, low_threshold, high_threshold, moving_average_enabled, moving_average_size, dft_x_offset, dft_y_offset):
        with self.output:
            clear_output(wait=True)
        
            plot_data = self.all_plots[scan_index - 1]
            data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbowlightct()

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
        display(self.fig)

    def moving_average(self, data, window_size):
        kernel = np.ones((window_size, window_size)) / (window_size ** 2)
        return np.convolve(data.flatten(), kernel.flatten(), mode='same').reshape(data.shape)

    def add_interactive_widget(self, name, widget):
        self.interactive_widgets[name] = widget

    def add_filter_widget(self, name, widget):
        self.filter_widgets[name] = widget

    def create_interface(self):
        interactive_plot = interactive(self.update_plot, **self.interactive_widgets)
        filter_checkboxes = [widget for widget in self.filter_widgets.values()]
        
        choose_dft_button = Button(description="Choose DFT")
        choose_dft_button.on_click(self.choose_dft_files)
        
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)

        output = VBox([
            interactive_plot,
            HBox(filter_checkboxes),
            HBox([save_button, choose_dft_button])
        ])
        return output


    def choose_dft_files(self, b):
        import tkinter as tk
        from tkinter import filedialog

        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data

    def save_plot(self, b):
        current_values = {name: widget.value for name, widget in self.interactive_widgets.items()}
        plot_data = self.all_plots[current_values['scan_index'] - 1]
        current_file = plot_data['file_name']
        save_folder = os.path.dirname(current_file)
        filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def run(self):
        self.preprocess_data()
        self.create_plot()
        self.output = Output()
        with self.output:
            display(self.fig)
        interface = self.create_interface()
        display(VBox([self.output, interface]))


class KSpacePlotModule(ARPESPlotModule):
    def __init__(self, data_files, work_function):
        self.hbar = 1.054571817e-34  # J*s
        self.m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        super().__init__(data_files, work_function)
        self.preprocess_data()  # Call this again to calculate k-space specific values
        

    def initialize_widgets(self):
        super().initialize_widgets()
        self.add_interactive_widget('dft_x_offset', FloatSlider(
            value=0, min=self.min_k, max=self.max_k,
            step=(self.max_k - self.min_k) / 200,
            description='DFT X Offset', continuous_update=True
        ))
        self.add_interactive_widget('dft_y_offset', FloatSlider(
            value=0, min=self.min_energy, max=self.max_energy,
            step=(self.max_energy - self.min_energy) / 200,
            description='DFT Y Offset', continuous_update=True
        ))


    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * self.m_e * E_b) * np.sin(np.radians(data.phi)) / self.hbar) / 10**10
            energy_values = -data.eV.values
            
            self.min_k = min(self.min_k, np.min(k_parallel))
            self.max_k = max(self.max_k, np.max(k_parallel))
            self.min_energy = min(self.min_energy, np.min(energy_values))
            self.max_energy = max(self.max_energy, np.max(energy_values))
            self.min_data_value = min(self.min_data_value, np.min(data.values))
            self.max_data_value = max(self.max_data_value, np.max(data.values))

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap=self.rainbow_light, norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, sigma, low_threshold, high_threshold,
                moving_average_size, dft_x_offset, dft_y_offset):
        canny_enabled = self.filter_widgets['canny_enabled'].value
        moving_average_enabled = self.filter_widgets['moving_average_enabled'].value
        plot_data = self.all_plots[scan_index - 1]
        data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbow_light

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
    def run(self):
        self.preprocess_data()
        self.create_plot()
        interface = self.create_interface()
        display(interface)
        return self




    def choose_dft_files(self, b):
        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data


class EDCPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.global_k_min = float('inf')
        self.global_k_max = float('-inf')
        self.global_e_min = float('inf')
        self.global_e_max = float('-inf')
        self.global_intensity_max = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.preprocess_data()

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']

            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': os.path.basename(file_path)
            })

            self.global_k_min = min(self.global_k_min, np.min(k_parallel))
            self.global_k_max = max(self.global_k_max, np.max(k_parallel))
            self.global_e_min = min(self.global_e_min, np.min(energy_values))
            self.global_e_max = max(self.global_e_max, np.max(energy_values))
            self.global_intensity_max = max(self.global_intensity_max, np.max(data.values))

    def run(self):
        interactive_plot = self.create_interactive_plot()
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)
        output = VBox([interactive_plot, save_button])
        display(output)

    def create_interactive_plot(self):
        return interactive(
            self.plot_edc,
            scan_index=IntSlider(value=1, min=1, max=len(self.data_files), step=1, description='Scan Index', continuous_update=False),
            n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
            vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
            show_edc=Checkbox(value=True, description='Show EDCs'),
            show_fit=Checkbox(value=False, description='Show Fits'),
            num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
            k_min=FloatSlider(value=self.global_k_min, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_min (Å⁻¹)', continuous_update=True),
            k_max=FloatSlider(value=self.global_k_max, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_max (Å⁻¹)', continuous_update=True),
            e_min=FloatSlider(value=self.global_e_min, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_min (eV)', continuous_update=True),
            e_max=FloatSlider(value=self.global_e_max, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_max (eV)', continuous_update=True),
            use_canny=Checkbox(value=False, description='Use Canny Filter'),
            sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
            low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
            high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
            enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
            averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
        )

    def plot_edc(self, scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size):
        plot_data = self.all_plots[scan_index - 1]
        self.ax.clear()

        data_to_plot = plot_data['data_values'].copy()
        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel)

        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            if not use_canny:
                kdc = kdc / np.max(kdc)

            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                self.ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')

            self.ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                self.ax.plot(energy_values, offset_fit, '--', label=fr'Fit $k_\parallel$ = {actual_k:.2f}', color=f'C{i}')

                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)
                self.ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                self.ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        self.ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        self.ax.legend()
        self.ax.tick_params(axis='both', which='major', labelsize=10)

        self.ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        self.ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        self.fig.canvas.draw_idle()



    def save_plot(self, b):
        current_values = {
            'scan_index': self.interactive_plot.children[0].value,
            'n': self.interactive_plot.children[1].value,
            'vertical_offset': self.interactive_plot.children[2].value,
            'show_edc': self.interactive_plot.children[3].value,
            'show_fit': self.interactive_plot.children[4].value,
            'num_peaks': self.interactive_plot.children[5].value,
            'k_min': self.interactive_plot.children[6].value,
            'k_max': self.interactive_plot.children[7].value,
            'e_min': self.interactive_plot.children[8].value,
            'e_max': self.interactive_plot.children[9].value,
            'use_canny': self.interactive_plot.children[10].value,
            'sigma': self.interactive_plot.children[11].value,
            'low_threshold': self.interactive_plot.children[12].value,
            'high_threshold': self.interactive_plot.children[13].value,
            'enable_averaging': self.interactive_plot.children[14].value,
            'averaging_kernel_size': self.interactive_plot.children[15].value
        }
        self.plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")
if __name__ == "__main__":
    suite = ARPESAnalysisSuite()
    suite.load_data_files()
    
    # Add plot modules
    suite.add_plot_module("K-Space Plot", KSpacePlotModule)
    suite.add_plot_module("EDC Plot", EDCPlotModule)
    
    suite.run()
igor_data = np.array([
    [57600, 54784, 58112],
    [56561.95, 53415.66, 57121.13],
    [55523.89, 52047.31, 56130.26],
    [54485.84, 50678.96, 55139.39],
    [53447.78, 49310.62, 54148.52],
    [52409.73, 47942.27, 53157.65],
    [51371.67, 46655.25, 52193.88],
    [50333.62, 45428.45, 51250.2],
    [49295.56, 44201.66, 50306.51],
    [48257.51, 42974.87, 49362.82],
    [47219.45, 41748.08, 48419.14],
    [46223.56, 40563.45, 47510.59],
    [45468.61, 39619.77, 46802.82],
    [44713.66, 38676.08, 46095.06],
    [43958.71, 37732.39, 45387.29],
    [43203.77, 36788.71, 44679.53],
    [42448.82, 35845.02, 43971.77],
    [41693.87, 34833.07, 43195.73],
    [40938.92, 33795.01, 42393.6],
    [40183.97, 32756.96, 41591.46],
    [39429.02, 31718.9, 40789.33],
    [38674.07, 30680.85, 39987.2],
    [37919.12, 29670.9, 39171.01],
    [37164.17, 28727.21, 38321.7],
    [36409.22, 27783.53, 37472.38],
    [35654.27, 26839.84, 36623.06],
    [34899.32, 25896.16, 35773.74],
    [34144.38, 24952.47, 34924.42],
    [33512.91, 24173.43, 34239.75],
    [32899.52, 23418.48, 33579.17],
    [32286.12, 22663.53, 32918.59],
    [31672.72, 21908.58, 32258.01],
    [31059.33, 21153.63, 31597.43],
    [30467.01, 20419.77, 30957.93],
    [29900.8, 19712, 30344.53],
    [29334.59, 19004.23, 29731.14],
    [28768.38, 18296.47, 29117.74],
    [28202.16, 17588.71, 28504.35],
    [27641.98, 16886.96, 27901.99],
    [27358.87, 16462.31, 27807.62],
    [27075.77, 16037.65, 27713.26],
    [26792.66, 15612.99, 27618.89],
    [26509.55, 15188.33, 27524.52],
    [26226.45, 14763.67, 27430.15],
    [26027.67, 14479.56, 27448.22],
    [25886.12, 14290.82, 27542.59],
    [25744.56, 14102.09, 27636.96],
    [25603.01, 13913.35, 27731.33],
    [25461.46, 13724.61, 27825.69],
    [25279.75, 13503.75, 27944.16],
    [24902.28, 13126.27, 28180.08],
    [24524.8, 12748.8, 28416],
    [24147.33, 12371.33, 28651.92],
    [23769.85, 11993.85, 28887.84],
    [23392.38, 11616.38, 29123.77],
    [22874.35, 11168.63, 29359.69],
    [22308.14, 10696.78, 29595.61],
    [21741.93, 10224.94, 29831.53],
    [21175.72, 9753.098, 30067.45],
    [20609.51, 9281.255, 30303.37],
    [19952.94, 8899.765, 30539.29],
    [19103.62, 8711.027, 30775.21],
    [18254.31, 8522.29, 31011.14],
    [17404.99, 8333.553, 31247.06],
    [16555.67, 8144.816, 31482.98],
    [15706.35, 7956.079, 31718.9],
    [14688.38, 7893.835, 31828.33],
    [13650.32, 7846.651, 31922.7],
    [12612.27, 7799.467, 32017.07],
    [11574.21, 7752.282, 32111.44],
    [10536.16, 7705.098, 32205.8],
    [9807.31, 7922.949, 32388.52],
    [9429.835, 8441.977, 32671.62],
    [9052.36, 8961.004, 32954.73],
    [8674.887, 9480.031, 33237.84],
    [8297.412, 9999.059, 33520.94],
    [7911.906, 10526.12, 33812.08],
    [7345.694, 11233.88, 34283.92],
    [6779.482, 11941.65, 34755.77],
    [6213.271, 12649.41, 35227.61],
    [5647.059, 13357.18, 35699.45],
    [5080.847, 14064.94, 36171.29],
    [4543.749, 14714.48, 36614.02],
    [4024.722, 15327.87, 37038.68],
    [3505.694, 15941.27, 37463.34],
    [2986.667, 16554.67, 37888],
    [2467.639, 17168.06, 38312.66],
    [1984.753, 17790.49, 38764.42],
    [1654.463, 18451.07, 39330.64],
    [1324.173, 19111.65, 39896.85],
    [993.8823, 19772.23, 40463.06],
    [663.5922, 20432.82, 41029.27],
    [333.302, 21093.4, 41595.48],
    [256, 21464.85, 41944.85],
    [256, 21747.95, 42227.95],
    [256, 22031.06, 42511.06],
    [256, 22314.16, 42794.16],
    [256, 22597.27, 43077.27],
    [239.9373, 23008.88, 43456.75],
    [192.7529, 23669.46, 44022.96],
    [145.5686, 24330.04, 44589.18],
    [98.38432, 24990.62, 45155.39],
    [51.2, 25651.2, 45721.6],
    [4.015687, 26311.78, 46287.81],
    [0, 26972.36, 46897.19],
    [0, 27632.94, 47510.59],
    [0, 28293.52, 48123.98],
    [0, 28954.1, 48737.38],
    [0, 29614.68, 49350.78],
    [0, 30344.53, 50033.44],
    [0, 31146.67, 50788.39],
    [0, 31948.8, 51543.34],
    [0, 32750.93, 52298.29],
    [0, 33553.07, 53053.24],
    [0, 34358.21, 53805.18],
    [0, 35207.53, 54512.94],
    [0, 36056.85, 55220.71],
    [0, 36906.16, 55928.47],
    [0, 37755.48, 56636.23],
    [0, 38604.8, 57344],
    [0, 39062.59, 57208.47],
    [0, 39298.51, 56595.07],
    [0, 39534.43, 55981.68],
    [0, 39770.35, 55368.28],
    [0, 40006.27, 54754.89],
    [0, 40181.96, 54041.1],
    [0, 40134.78, 52955.86],
    [0, 40087.59, 51870.62],
    [0, 40040.41, 50785.38],
    [0, 39993.22, 49700.14],
    [0, 39946.04, 48614.9],
    [0, 39936, 47641.1],
    [0, 39936, 46697.41],
    [0, 39936, 45753.73],
    [0, 39936, 44810.04],
    [0, 39936, 43866.35],
    [0, 39918.93, 42854.4],
    [0, 39871.75, 41721.98],
    [0, 39824.57, 40589.55],
    [0, 39777.38, 39457.13],
    [0, 39730.2, 38324.71],
    [0, 39683.01, 37192.28],
    [0, 39680, 36369.07],
    [0, 39680, 35566.93],
    [0, 39680, 34764.8],
    [0, 39680, 33962.67],
    [0, 39680, 33160.54],
    [0, 39680, 32527.06],
    [0, 39680, 32055.21],
    [0, 39680, 31583.37],
    [0, 39680, 31111.53],
    [0, 39680, 30639.69],
    [0, 39675.98, 30123.67],
    [0, 39628.8, 29132.8],
    [0, 39581.62, 28141.93],
    [0, 39534.43, 27151.06],
    [0, 39487.25, 26160.19],
    [0, 39440.06, 25169.32],
    [0, 39361.76, 24240.69],
    [0, 39267.39, 23344.19],
    [0, 39173.02, 22447.69],
    [0, 39078.65, 21551.18],
    [0, 38984.28, 20654.68],
    [0, 38923.04, 19835.48],
    [0, 38970.23, 19269.27],
    [0, 39017.41, 18703.06],
    [0, 39064.6, 18136.85],
    [0, 39111.78, 17570.63],
    [0, 39158.96, 17004.42],
    [0, 39435.04, 16781.55],
    [0, 39765.33, 16640],
    [0, 40095.62, 16498.45],
    [0, 40425.91, 16356.89],
    [0, 40756.2, 16215.34],
    [993.8823, 41122.64, 16073.79],
    [3589.02, 41547.29, 15932.24],
    [6184.157, 41971.95, 15790.68],
    [8779.294, 42396.61, 15649.13],
    [11374.43, 42821.27, 15507.58],
    [13969.57, 43245.93, 15366.02],
    [15796.71, 43715.77, 15224.47],
    [17589.71, 44187.61, 15082.92],
    [19382.71, 44659.45, 14941.36],
    [21175.72, 45131.29, 14799.81],
    [22968.72, 45603.14, 14658.26],
    [24686.43, 46100.08, 14516.71],
    [26337.88, 46619.11, 14375.15],
    [27989.33, 47138.13, 14233.6],
    [29640.79, 47657.16, 14092.05],
    [31292.23, 48176.19, 13950.49],
    [32933.65, 48705.25, 13798.9],
    [34490.73, 49318.65, 13562.98],
    [36047.81, 49932.05, 13327.06],
    [37604.89, 50545.44, 13091.14],
    [39161.98, 51158.84, 12855.22],
    [40719.06, 51772.23, 12619.29],
    [41922.76, 52225, 12415.5],
    [42960.82, 52602.48, 12226.76],
    [43998.87, 52979.95, 12038.02],
    [45036.93, 53357.43, 11849.29],
    [46074.98, 53734.9, 11660.55],
    [47293.74, 54196.71, 11411.58],
    [49039.56, 54904.47, 10986.92],
    [50785.38, 55612.23, 10562.26],
    [52531.2, 56320, 10137.6],
    [54277.02, 57027.77, 9712.941],
    [56022.84, 57735.53, 9288.282],
    [57494.59, 58325.84, 8785.317],
    [58910.12, 58892.05, 8266.29],
    [60325.65, 59458.26, 7747.263],
    [61741.18, 60024.47, 7228.235],
    [63156.71, 60590.68, 6709.208],
    [64076.3, 60470.21, 6457.224],
    [64265.04, 59337.79, 6598.776],
    [64453.77, 58205.36, 6740.33],
    [64642.51, 57072.94, 6881.882],
    [64831.25, 55940.52, 7023.435],
    [65019.98, 54808.09, 7164.988],
    [64746.92, 53260.05, 7398.902],
    [64463.81, 51702.96, 7634.824],
    [64180.71, 50145.88, 7870.745],
    [63897.6, 48588.8, 8106.667],
    [63614.49, 47031.72, 8342.588],
    [63592.41, 45605.14, 8474.102],
    [63781.14, 44283.98, 8521.286],
    [63969.88, 42962.82, 8568.471],
    [64158.62, 41641.66, 8615.655],
    [64347.36, 40320.5, 8662.839],
    [64415.62, 38993.32, 8704],
    [63660.68, 37624.97, 8704],
    [62905.73, 36256.63, 8704],
    [62150.78, 34888.28, 8704],
    [61395.83, 33519.94, 8704],
    [60640.88, 32151.59, 8704],
    [60283.48, 30882.63, 8704],
    [60094.75, 29655.84, 8704],
    [59906.01, 28429.05, 8704],
    [59717.27, 27202.26, 8704],
    [59528.54, 25975.47, 8704],
    [59339.8, 24722.57, 8704],
    [59151.06, 23401.41, 8704],
    [58962.32, 22080.25, 8704],
    [58773.59, 20759.09, 8704],
    [58584.85, 19437.93, 8704],
    [58396.11, 18116.77, 8704],
    [58287.69, 17197.18, 8704],
    [58193.32, 16347.86, 8704],
    [58098.95, 15498.54, 8704],
    [58004.58, 14649.22, 8704],
    [57910.21, 13799.91, 8704],
    [57795.77, 12267.92, 8704],
    [57654.21, 9814.337, 8704],
    [57512.66, 7360.753, 8704],
    [57371.11, 4907.168, 8704],
    [57229.55, 2453.584, 8704],
    [57088, 0, 8704]])
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from ipywidgets import Dropdown, VBox, Output
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
import matplotlib.pyplot as plt
from ipywidgets import Output, VBox, Dropdown
from matplotlib.backends.backend_agg import FigureCanvasAgg
from IPython.display import display, clear_output

#%matplotlib widget
# Suppress warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128


class ARPESAnalysisSuite:
    def __init__(self):
        self.data_files = []
        self.work_function = 4.5
        self.plot_modules = {}
        self.filter_modules = {}
        self.dft_data = []
        self.current_plot = None
        self.plot_output = Output()
        self.fig, self.ax = plt.subplots()
        self.canvas = FigureCanvasAgg(self.fig)

    def load_data_files(self):
        root = tk.Tk()
        root.withdraw()
        folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
        root.destroy()
        if folder_path:
            self.data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
        else:
            print("No folder selected.")

    def add_plot_module(self, name, module_class):
        self.plot_modules[name] = module_class

    def add_filter_module(self, name, module):
        self.filter_modules[name] = module

    def run(self):
        if not self.data_files:
            print("No data files loaded. Please load data files first.")
            return
        
        plot_selector = self.create_plot_selector()
        main_interface = VBox([plot_selector, self.plot_output])
        display(main_interface)
        self.update_plot(list(self.plot_modules.keys())[0])

    def create_plot_selector(self):
        plot_options = list(self.plot_modules.keys())
        plot_dropdown = Dropdown(options=plot_options, description='Plot Type:')
        plot_dropdown.observe(self.on_plot_change, names='value')
        return plot_dropdown

    def on_plot_change(self, change):
        self.update_plot(change.new)

    def update_plot(self, plot_type):
        with self.plot_output:
            clear_output(wait=True)
            self.ax.clear()
            
            if plot_type in self.plot_modules:
                module_class = self.plot_modules[plot_type]
                module_instance = module_class(self.data_files, self.work_function)
                
                self.current_plot = module_instance.run()
                self.ax.add_artist(self.current_plot)
                
            self.canvas.draw()
            display(self.fig)
class ARPESPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.interactive_widgets = {}
        self.filter_widgets = {}
        self.dft_data = []
        self.scan_types = self.determine_scan_type()
        self.rainbow_light = self.rainbowlightct()
        self.add_filter_widget('canny_enabled', Checkbox(value=False, description='Canny Edge Detection'))
        self.add_filter_widget('moving_average_enabled', Checkbox(value=False, description='Moving Average'))
        self.preprocess_data()  # Call this before initialize_widgets
        self.initialize_widgets()

    def initialize_widgets(self):
        self.add_interactive_widget('scan_index', IntSlider(
            value=1, min=1, max=len(self.data_files), step=1,
            description='Scan Index', continuous_update=False,
            layout=Layout(width='50%')
        ))
        self.add_interactive_widget('vmin', FloatSlider(
            value=self.min_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Min Value',
            continuous_update=False
        ))
        self.add_interactive_widget('vmax', FloatSlider(
            value=self.max_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Max Value',
            continuous_update=False
        ))
        self.add_interactive_widget('scale', FloatSlider(
            value=1.0, min=0.1, max=2.0, step=0.1,
            description='Scale', continuous_update=False
        ))
        self.add_interactive_widget('sigma', FloatSlider(
            value=1.0, min=0.1, max=20.0, step=0.1,
            description='Canny Sigma', continuous_update=False
        ))
        self.add_interactive_widget('low_threshold', FloatSlider(
            value=0.1, min=0.0, max=1.0, step=0.01,
            description='Canny Low Threshold', continuous_update=False
        ))
        self.add_interactive_widget('high_threshold', FloatSlider(
            value=0.2, min=0.0, max=1.0, step=0.01,
            description='Canny High Threshold', continuous_update=False
        ))
        self.add_interactive_widget('moving_average_size', IntSlider(
            value=3, min=3, max=50, step=1,
            description='Moving Average Size', continuous_update=False
        ))

    def rainbowlightct(self):
        # Normalize the RGB values to the range 0-1
        normalized_data = igor_data / 65535.0
        
        # Create the ListedColormap
        return ListedColormap(normalized_data)


    def determine_scan_type(self):
        scan_types = []
        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            if 'polar' in data.attrs:
                scan_types.append(('polar', data.attrs['polar']))
            elif 'hv' in data.attrs:
                scan_types.append(('hv', data.attrs['hv']))
            else:
                scan_types.append(('unknown', None))
        return scan_types

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10
            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })
            self.max_data_value = max(self.max_data_value, np.max(data.values))

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap = self.rainbowlightct(), norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, canny_enabled, sigma, low_threshold, high_threshold, moving_average_enabled, moving_average_size, dft_x_offset, dft_y_offset):
        with self.output:
            clear_output(wait=True)
        
            plot_data = self.all_plots[scan_index - 1]
            data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbowlightct()

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
        display(self.fig)

    def moving_average(self, data, window_size):
        kernel = np.ones((window_size, window_size)) / (window_size ** 2)
        return np.convolve(data.flatten(), kernel.flatten(), mode='same').reshape(data.shape)

    def add_interactive_widget(self, name, widget):
        self.interactive_widgets[name] = widget

    def add_filter_widget(self, name, widget):
        self.filter_widgets[name] = widget

    def create_interface(self):
        interactive_plot = interactive(self.update_plot, **self.interactive_widgets)
        filter_checkboxes = [widget for widget in self.filter_widgets.values()]
        
        choose_dft_button = Button(description="Choose DFT")
        choose_dft_button.on_click(self.choose_dft_files)
        
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)

        output = VBox([
            interactive_plot,
            HBox(filter_checkboxes),
            HBox([save_button, choose_dft_button])
        ])
        return output


    def choose_dft_files(self, b):
        import tkinter as tk
        from tkinter import filedialog

        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data

    def save_plot(self, b):
        current_values = {name: widget.value for name, widget in self.interactive_widgets.items()}
        plot_data = self.all_plots[current_values['scan_index'] - 1]
        current_file = plot_data['file_name']
        save_folder = os.path.dirname(current_file)
        filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def run(self):
        self.preprocess_data()
        self.create_plot()
        self.output = Output()
        with self.output:
            display(self.fig)
        interface = self.create_interface()
        display(VBox([self.output, interface]))


class KSpacePlotModule(ARPESPlotModule):
    def __init__(self, data_files, work_function):
        self.hbar = 1.054571817e-34  # J*s
        self.m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        super().__init__(data_files, work_function)
        self.preprocess_data()  # Call this again to calculate k-space specific values
        

    def initialize_widgets(self):
        super().initialize_widgets()
        self.add_interactive_widget('dft_x_offset', FloatSlider(
            value=0, min=self.min_k, max=self.max_k,
            step=(self.max_k - self.min_k) / 200,
            description='DFT X Offset', continuous_update=True
        ))
        self.add_interactive_widget('dft_y_offset', FloatSlider(
            value=0, min=self.min_energy, max=self.max_energy,
            step=(self.max_energy - self.min_energy) / 200,
            description='DFT Y Offset', continuous_update=True
        ))


    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * self.m_e * E_b) * np.sin(np.radians(data.phi)) / self.hbar) / 10**10
            energy_values = -data.eV.values
            
            self.min_k = min(self.min_k, np.min(k_parallel))
            self.max_k = max(self.max_k, np.max(k_parallel))
            self.min_energy = min(self.min_energy, np.min(energy_values))
            self.max_energy = max(self.max_energy, np.max(energy_values))
            self.min_data_value = min(self.min_data_value, np.min(data.values))
            self.max_data_value = max(self.max_data_value, np.max(data.values))

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap=self.rainbow_light, norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, sigma, low_threshold, high_threshold,
                moving_average_size, dft_x_offset, dft_y_offset):
        canny_enabled = self.filter_widgets['canny_enabled'].value
        moving_average_enabled = self.filter_widgets['moving_average_enabled'].value
        plot_data = self.all_plots[scan_index - 1]
        data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbow_light

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
    def run(self):
        self.preprocess_data()
        self.create_plot()
        interface = self.create_interface()
        display(interface)
        return self




    def choose_dft_files(self, b):
        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data


class EDCPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.global_k_min = float('inf')
        self.global_k_max = float('-inf')
        self.global_e_min = float('inf')
        self.global_e_max = float('-inf')
        self.global_intensity_max = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.preprocess_data()

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']

            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': os.path.basename(file_path)
            })

            self.global_k_min = min(self.global_k_min, np.min(k_parallel))
            self.global_k_max = max(self.global_k_max, np.max(k_parallel))
            self.global_e_min = min(self.global_e_min, np.min(energy_values))
            self.global_e_max = max(self.global_e_max, np.max(energy_values))
            self.global_intensity_max = max(self.global_intensity_max, np.max(data.values))

    def run(self):
        interactive_plot = self.create_interactive_plot()
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)
        output = VBox([interactive_plot, save_button])
        display(output)

    def create_interactive_plot(self):
        return interactive(
            self.plot_edc,
            scan_index=IntSlider(value=1, min=1, max=len(self.data_files), step=1, description='Scan Index', continuous_update=False),
            n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
            vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
            show_edc=Checkbox(value=True, description='Show EDCs'),
            show_fit=Checkbox(value=False, description='Show Fits'),
            num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
            k_min=FloatSlider(value=self.global_k_min, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_min (Å⁻¹)', continuous_update=True),
            k_max=FloatSlider(value=self.global_k_max, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_max (Å⁻¹)', continuous_update=True),
            e_min=FloatSlider(value=self.global_e_min, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_min (eV)', continuous_update=True),
            e_max=FloatSlider(value=self.global_e_max, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_max (eV)', continuous_update=True),
            use_canny=Checkbox(value=False, description='Use Canny Filter'),
            sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
            low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
            high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
            enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
            averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
        )

    def plot_edc(self, scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size):
        plot_data = self.all_plots[scan_index - 1]
        self.ax.clear()

        data_to_plot = plot_data['data_values'].copy()
        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel)

        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            if not use_canny:
                kdc = kdc / np.max(kdc)

            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                self.ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')

            self.ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                self.ax.plot(energy_values, offset_fit, '--', label=fr'Fit $k_\parallel$ = {actual_k:.2f}', color=f'C{i}')

                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)
                self.ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                self.ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        self.ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        self.ax.legend()
        self.ax.tick_params(axis='both', which='major', labelsize=10)

        self.ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        self.ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        self.fig.canvas.draw_idle()



    def save_plot(self, b):
        current_values = {
            'scan_index': self.interactive_plot.children[0].value,
            'n': self.interactive_plot.children[1].value,
            'vertical_offset': self.interactive_plot.children[2].value,
            'show_edc': self.interactive_plot.children[3].value,
            'show_fit': self.interactive_plot.children[4].value,
            'num_peaks': self.interactive_plot.children[5].value,
            'k_min': self.interactive_plot.children[6].value,
            'k_max': self.interactive_plot.children[7].value,
            'e_min': self.interactive_plot.children[8].value,
            'e_max': self.interactive_plot.children[9].value,
            'use_canny': self.interactive_plot.children[10].value,
            'sigma': self.interactive_plot.children[11].value,
            'low_threshold': self.interactive_plot.children[12].value,
            'high_threshold': self.interactive_plot.children[13].value,
            'enable_averaging': self.interactive_plot.children[14].value,
            'averaging_kernel_size': self.interactive_plot.children[15].value
        }
        self.plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")
if __name__ == "__main__":
    suite = ARPESAnalysisSuite()
    suite.load_data_files()
    
    # Add plot modules
    suite.add_plot_module("K-Space Plot", KSpacePlotModule)
    suite.add_plot_module("EDC Plot", EDCPlotModule)
    
    suite.run()
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from ipywidgets import Dropdown, VBox, Output
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
import matplotlib.pyplot as plt
from ipywidgets import Output, VBox, Dropdown
from matplotlib.backends.backend_agg import FigureCanvasAgg
from IPython.display import display, clear_output

get_ipython().run_line_magic('matplotlib', 'widget')
# Suppress warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128


class ARPESAnalysisSuite:
    def __init__(self):
        self.data_files = []
        self.work_function = 4.5
        self.plot_modules = {}
        self.filter_modules = {}
        self.dft_data = []
        self.current_plot = None
        self.plot_output = Output()
        self.fig, self.ax = plt.subplots()
        self.canvas = FigureCanvasAgg(self.fig)

    def load_data_files(self):
        root = tk.Tk()
        root.withdraw()
        folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
        root.destroy()
        if folder_path:
            self.data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
        else:
            print("No folder selected.")

    def add_plot_module(self, name, module_class):
        self.plot_modules[name] = module_class

    def add_filter_module(self, name, module):
        self.filter_modules[name] = module

    def run(self):
        if not self.data_files:
            print("No data files loaded. Please load data files first.")
            return
        
        plot_selector = self.create_plot_selector()
        main_interface = VBox([plot_selector, self.plot_output])
        display(main_interface)
        self.update_plot(list(self.plot_modules.keys())[0])

    def create_plot_selector(self):
        plot_options = list(self.plot_modules.keys())
        plot_dropdown = Dropdown(options=plot_options, description='Plot Type:')
        plot_dropdown.observe(self.on_plot_change, names='value')
        return plot_dropdown

    def on_plot_change(self, change):
        self.update_plot(change.new)

    def update_plot(self, plot_type):
        with self.plot_output:
            clear_output(wait=True)
            self.ax.clear()
            
            if plot_type in self.plot_modules:
                module_class = self.plot_modules[plot_type]
                module_instance = module_class(self.data_files, self.work_function)
                
                self.current_plot = module_instance.run()
                self.ax.add_artist(self.current_plot)
                
            self.canvas.draw()
            display(self.fig)
class ARPESPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.interactive_widgets = {}
        self.filter_widgets = {}
        self.dft_data = []
        self.scan_types = self.determine_scan_type()
        self.rainbow_light = self.rainbowlightct()
        self.add_filter_widget('canny_enabled', Checkbox(value=False, description='Canny Edge Detection'))
        self.add_filter_widget('moving_average_enabled', Checkbox(value=False, description='Moving Average'))
        self.preprocess_data()  # Call this before initialize_widgets
        self.initialize_widgets()

    def initialize_widgets(self):
        self.add_interactive_widget('scan_index', IntSlider(
            value=1, min=1, max=len(self.data_files), step=1,
            description='Scan Index', continuous_update=False,
            layout=Layout(width='50%')
        ))
        self.add_interactive_widget('vmin', FloatSlider(
            value=self.min_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Min Value',
            continuous_update=False
        ))
        self.add_interactive_widget('vmax', FloatSlider(
            value=self.max_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Max Value',
            continuous_update=False
        ))
        self.add_interactive_widget('scale', FloatSlider(
            value=1.0, min=0.1, max=2.0, step=0.1,
            description='Scale', continuous_update=False
        ))
        self.add_interactive_widget('sigma', FloatSlider(
            value=1.0, min=0.1, max=20.0, step=0.1,
            description='Canny Sigma', continuous_update=False
        ))
        self.add_interactive_widget('low_threshold', FloatSlider(
            value=0.1, min=0.0, max=1.0, step=0.01,
            description='Canny Low Threshold', continuous_update=False
        ))
        self.add_interactive_widget('high_threshold', FloatSlider(
            value=0.2, min=0.0, max=1.0, step=0.01,
            description='Canny High Threshold', continuous_update=False
        ))
        self.add_interactive_widget('moving_average_size', IntSlider(
            value=3, min=3, max=50, step=1,
            description='Moving Average Size', continuous_update=False
        ))

    def rainbowlightct(self):
        # Normalize the RGB values to the range 0-1
        normalized_data = igor_data / 65535.0
        
        # Create the ListedColormap
        return ListedColormap(normalized_data)


    def determine_scan_type(self):
        scan_types = []
        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            if 'polar' in data.attrs:
                scan_types.append(('polar', data.attrs['polar']))
            elif 'hv' in data.attrs:
                scan_types.append(('hv', data.attrs['hv']))
            else:
                scan_types.append(('unknown', None))
        return scan_types

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10
            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })
            self.max_data_value = max(self.max_data_value, np.max(data.values))

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap = self.rainbowlightct(), norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, canny_enabled, sigma, low_threshold, high_threshold, moving_average_enabled, moving_average_size, dft_x_offset, dft_y_offset):
        with self.output:
            clear_output(wait=True)
        
            plot_data = self.all_plots[scan_index - 1]
            data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbowlightct()

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
        display(self.fig)

    def moving_average(self, data, window_size):
        kernel = np.ones((window_size, window_size)) / (window_size ** 2)
        return np.convolve(data.flatten(), kernel.flatten(), mode='same').reshape(data.shape)

    def add_interactive_widget(self, name, widget):
        self.interactive_widgets[name] = widget

    def add_filter_widget(self, name, widget):
        self.filter_widgets[name] = widget

    def create_interface(self):
        interactive_plot = interactive(self.update_plot, **self.interactive_widgets)
        filter_checkboxes = [widget for widget in self.filter_widgets.values()]
        
        choose_dft_button = Button(description="Choose DFT")
        choose_dft_button.on_click(self.choose_dft_files)
        
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)

        output = VBox([
            interactive_plot,
            HBox(filter_checkboxes),
            HBox([save_button, choose_dft_button])
        ])
        return output


    def choose_dft_files(self, b):
        import tkinter as tk
        from tkinter import filedialog

        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data

    def save_plot(self, b):
        current_values = {name: widget.value for name, widget in self.interactive_widgets.items()}
        plot_data = self.all_plots[current_values['scan_index'] - 1]
        current_file = plot_data['file_name']
        save_folder = os.path.dirname(current_file)
        filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def run(self):
        self.preprocess_data()
        self.create_plot()
        self.output = Output()
        with self.output:
            display(self.fig)
        interface = self.create_interface()
        display(VBox([self.output, interface]))


class KSpacePlotModule(ARPESPlotModule):
    def __init__(self, data_files, work_function):
        self.hbar = 1.054571817e-34  # J*s
        self.m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        super().__init__(data_files, work_function)
        self.preprocess_data()  # Call this again to calculate k-space specific values
        

    def initialize_widgets(self):
        super().initialize_widgets()
        self.add_interactive_widget('dft_x_offset', FloatSlider(
            value=0, min=self.min_k, max=self.max_k,
            step=(self.max_k - self.min_k) / 200,
            description='DFT X Offset', continuous_update=True
        ))
        self.add_interactive_widget('dft_y_offset', FloatSlider(
            value=0, min=self.min_energy, max=self.max_energy,
            step=(self.max_energy - self.min_energy) / 200,
            description='DFT Y Offset', continuous_update=True
        ))


    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * self.m_e * E_b) * np.sin(np.radians(data.phi)) / self.hbar) / 10**10
            energy_values = -data.eV.values
            
            self.min_k = min(self.min_k, np.min(k_parallel))
            self.max_k = max(self.max_k, np.max(k_parallel))
            self.min_energy = min(self.min_energy, np.min(energy_values))
            self.max_energy = max(self.max_energy, np.max(energy_values))
            self.min_data_value = min(self.min_data_value, np.min(data.values))
            self.max_data_value = max(self.max_data_value, np.max(data.values))

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap=self.rainbow_light, norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, sigma, low_threshold, high_threshold,
                moving_average_size, dft_x_offset, dft_y_offset):
        canny_enabled = self.filter_widgets['canny_enabled'].value
        moving_average_enabled = self.filter_widgets['moving_average_enabled'].value
        plot_data = self.all_plots[scan_index - 1]
        data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbow_light

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
    def run(self):
        self.preprocess_data()
        self.create_plot()
        interface = self.create_interface()
        display(interface)
        return self




    def choose_dft_files(self, b):
        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data


class EDCPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.global_k_min = float('inf')
        self.global_k_max = float('-inf')
        self.global_e_min = float('inf')
        self.global_e_max = float('-inf')
        self.global_intensity_max = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.preprocess_data()

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']

            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': os.path.basename(file_path)
            })

            self.global_k_min = min(self.global_k_min, np.min(k_parallel))
            self.global_k_max = max(self.global_k_max, np.max(k_parallel))
            self.global_e_min = min(self.global_e_min, np.min(energy_values))
            self.global_e_max = max(self.global_e_max, np.max(energy_values))
            self.global_intensity_max = max(self.global_intensity_max, np.max(data.values))

    def run(self):
        interactive_plot = self.create_interactive_plot()
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)
        output = VBox([interactive_plot, save_button])
        display(output)

    def create_interactive_plot(self):
        return interactive(
            self.plot_edc,
            scan_index=IntSlider(value=1, min=1, max=len(self.data_files), step=1, description='Scan Index', continuous_update=False),
            n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
            vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
            show_edc=Checkbox(value=True, description='Show EDCs'),
            show_fit=Checkbox(value=False, description='Show Fits'),
            num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
            k_min=FloatSlider(value=self.global_k_min, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_min (Å⁻¹)', continuous_update=True),
            k_max=FloatSlider(value=self.global_k_max, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_max (Å⁻¹)', continuous_update=True),
            e_min=FloatSlider(value=self.global_e_min, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_min (eV)', continuous_update=True),
            e_max=FloatSlider(value=self.global_e_max, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_max (eV)', continuous_update=True),
            use_canny=Checkbox(value=False, description='Use Canny Filter'),
            sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
            low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
            high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
            enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
            averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
        )

    def plot_edc(self, scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size):
        plot_data = self.all_plots[scan_index - 1]
        self.ax.clear()

        data_to_plot = plot_data['data_values'].copy()
        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel)

        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            if not use_canny:
                kdc = kdc / np.max(kdc)

            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                self.ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')

            self.ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                self.ax.plot(energy_values, offset_fit, '--', label=fr'Fit $k_\parallel$ = {actual_k:.2f}', color=f'C{i}')

                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)
                self.ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                self.ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        self.ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        self.ax.legend()
        self.ax.tick_params(axis='both', which='major', labelsize=10)

        self.ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        self.ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        self.fig.canvas.draw_idle()



    def save_plot(self, b):
        current_values = {
            'scan_index': self.interactive_plot.children[0].value,
            'n': self.interactive_plot.children[1].value,
            'vertical_offset': self.interactive_plot.children[2].value,
            'show_edc': self.interactive_plot.children[3].value,
            'show_fit': self.interactive_plot.children[4].value,
            'num_peaks': self.interactive_plot.children[5].value,
            'k_min': self.interactive_plot.children[6].value,
            'k_max': self.interactive_plot.children[7].value,
            'e_min': self.interactive_plot.children[8].value,
            'e_max': self.interactive_plot.children[9].value,
            'use_canny': self.interactive_plot.children[10].value,
            'sigma': self.interactive_plot.children[11].value,
            'low_threshold': self.interactive_plot.children[12].value,
            'high_threshold': self.interactive_plot.children[13].value,
            'enable_averaging': self.interactive_plot.children[14].value,
            'averaging_kernel_size': self.interactive_plot.children[15].value
        }
        self.plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")
if __name__ == "__main__":
    suite = ARPESAnalysisSuite()
    suite.load_data_files()
    
    # Add plot modules
    suite.add_plot_module("K-Space Plot", KSpacePlotModule)
    suite.add_plot_module("EDC Plot", EDCPlotModule)
    
    suite.run()
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from ipywidgets import Dropdown, VBox, Output
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
import matplotlib.pyplot as plt
from ipywidgets import Output, VBox, Dropdown
from matplotlib.backends.backend_agg import FigureCanvasAgg
from IPython.display import display, clear_output

get_ipython().run_line_magic('matplotlib', 'widget')
# Suppress warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128


class ARPESAnalysisSuite:
    def __init__(self):
        self.data_files = []
        self.work_function = 4.5
        self.plot_modules = {}
        self.filter_modules = {}
        self.dft_data = []
        self.current_plot = None
        self.plot_output = Output()
        self.main_output = Output()


    def load_data_files(self):
        root = tk.Tk()
        root.withdraw()
        folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
        root.destroy()
        if folder_path:
            self.data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
        else:
            print("No folder selected.")

    def add_plot_module(self, name, module_class):
        self.plot_modules[name] = module_class

    def add_filter_module(self, name, module):
        self.filter_modules[name] = module

    def run(self):
        if not self.data_files:
            print("No data files loaded. Please load data files first.")
            return
        
        plot_selector = self.create_plot_selector()
        main_interface = VBox([plot_selector, self.plot_output])
        display(main_interface)
        self.update_plot(list(self.plot_modules.keys())[0])

    def create_plot_selector(self):
        plot_options = list(self.plot_modules.keys())
        plot_dropdown = Dropdown(options=plot_options, description='Plot Type:')
        plot_dropdown.observe(self.on_plot_change, names='value')
        return plot_dropdown

    def on_plot_change(self, change):
        self.update_plot(change.new)

    def update_plot(self, plot_type):
        with self.plot_output:
            clear_output(wait=True)
            plt.close('all')  # Close all existing figures
            
            if plot_type in self.plot_modules:
                module_class = self.plot_modules[plot_type]
                module_instance = module_class(self.data_files, self.work_function)
                
                plt.figure()
                self.current_plot = module_instance.run()
                plt.show()
class ARPESPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.interactive_widgets = {}
        self.filter_widgets = {}
        self.dft_data = []
        self.scan_types = self.determine_scan_type()
        self.rainbow_light = self.rainbowlightct()
        self.add_filter_widget('canny_enabled', Checkbox(value=False, description='Canny Edge Detection'))
        self.add_filter_widget('moving_average_enabled', Checkbox(value=False, description='Moving Average'))
        self.preprocess_data()  # Call this before initialize_widgets
        self.initialize_widgets()

    def initialize_widgets(self):
        self.add_interactive_widget('scan_index', IntSlider(
            value=1, min=1, max=len(self.data_files), step=1,
            description='Scan Index', continuous_update=False,
            layout=Layout(width='50%')
        ))
        self.add_interactive_widget('vmin', FloatSlider(
            value=self.min_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Min Value',
            continuous_update=False
        ))
        self.add_interactive_widget('vmax', FloatSlider(
            value=self.max_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Max Value',
            continuous_update=False
        ))
        self.add_interactive_widget('scale', FloatSlider(
            value=1.0, min=0.1, max=2.0, step=0.1,
            description='Scale', continuous_update=False
        ))
        self.add_interactive_widget('sigma', FloatSlider(
            value=1.0, min=0.1, max=20.0, step=0.1,
            description='Canny Sigma', continuous_update=False
        ))
        self.add_interactive_widget('low_threshold', FloatSlider(
            value=0.1, min=0.0, max=1.0, step=0.01,
            description='Canny Low Threshold', continuous_update=False
        ))
        self.add_interactive_widget('high_threshold', FloatSlider(
            value=0.2, min=0.0, max=1.0, step=0.01,
            description='Canny High Threshold', continuous_update=False
        ))
        self.add_interactive_widget('moving_average_size', IntSlider(
            value=3, min=3, max=50, step=1,
            description='Moving Average Size', continuous_update=False
        ))

    def rainbowlightct(self):
        # Normalize the RGB values to the range 0-1
        normalized_data = igor_data / 65535.0
        
        # Create the ListedColormap
        return ListedColormap(normalized_data)


    def determine_scan_type(self):
        scan_types = []
        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            if 'polar' in data.attrs:
                scan_types.append(('polar', data.attrs['polar']))
            elif 'hv' in data.attrs:
                scan_types.append(('hv', data.attrs['hv']))
            else:
                scan_types.append(('unknown', None))
        return scan_types

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10
            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })
            self.max_data_value = max(self.max_data_value, np.max(data.values))

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap = self.rainbowlightct(), norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, canny_enabled, sigma, low_threshold, high_threshold, moving_average_enabled, moving_average_size, dft_x_offset, dft_y_offset):
        with self.output:
            clear_output(wait=True)
        
            plot_data = self.all_plots[scan_index - 1]
            data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbowlightct()

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
        display(self.fig)

    def moving_average(self, data, window_size):
        kernel = np.ones((window_size, window_size)) / (window_size ** 2)
        return np.convolve(data.flatten(), kernel.flatten(), mode='same').reshape(data.shape)

    def add_interactive_widget(self, name, widget):
        self.interactive_widgets[name] = widget

    def add_filter_widget(self, name, widget):
        self.filter_widgets[name] = widget

    def create_interface(self):
        interactive_plot = interactive(self.update_plot, **self.interactive_widgets)
        filter_checkboxes = [widget for widget in self.filter_widgets.values()]
        
        choose_dft_button = Button(description="Choose DFT")
        choose_dft_button.on_click(self.choose_dft_files)
        
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)

        output = VBox([
            interactive_plot,
            HBox(filter_checkboxes),
            HBox([save_button, choose_dft_button])
        ])
        return output


    def choose_dft_files(self, b):
        import tkinter as tk
        from tkinter import filedialog

        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data

    def save_plot(self, b):
        current_values = {name: widget.value for name, widget in self.interactive_widgets.items()}
        plot_data = self.all_plots[current_values['scan_index'] - 1]
        current_file = plot_data['file_name']
        save_folder = os.path.dirname(current_file)
        filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def run(self):
        self.preprocess_data()
        self.create_plot()
        self.output = Output()
        with self.output:
            display(self.fig)
        interface = self.create_interface()
        display(VBox([self.output, interface]))


class KSpacePlotModule(ARPESPlotModule):
    def __init__(self, data_files, work_function):
        self.hbar = 1.054571817e-34  # J*s
        self.m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        super().__init__(data_files, work_function)
        self.preprocess_data()  # Call this again to calculate k-space specific values
        

    def initialize_widgets(self):
        super().initialize_widgets()
        self.add_interactive_widget('dft_x_offset', FloatSlider(
            value=0, min=self.min_k, max=self.max_k,
            step=(self.max_k - self.min_k) / 200,
            description='DFT X Offset', continuous_update=True
        ))
        self.add_interactive_widget('dft_y_offset', FloatSlider(
            value=0, min=self.min_energy, max=self.max_energy,
            step=(self.max_energy - self.min_energy) / 200,
            description='DFT Y Offset', continuous_update=True
        ))


    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * self.m_e * E_b) * np.sin(np.radians(data.phi)) / self.hbar) / 10**10
            energy_values = -data.eV.values
            
            self.min_k = min(self.min_k, np.min(k_parallel))
            self.max_k = max(self.max_k, np.max(k_parallel))
            self.min_energy = min(self.min_energy, np.min(energy_values))
            self.max_energy = max(self.max_energy, np.max(energy_values))
            self.min_data_value = min(self.min_data_value, np.min(data.values))
            self.max_data_value = max(self.max_data_value, np.max(data.values))

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap=self.rainbow_light, norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, sigma, low_threshold, high_threshold,
                moving_average_size, dft_x_offset, dft_y_offset):
        canny_enabled = self.filter_widgets['canny_enabled'].value
        moving_average_enabled = self.filter_widgets['moving_average_enabled'].value
        plot_data = self.all_plots[scan_index - 1]
        data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbow_light

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
    def run(self):
        self.preprocess_data()
        self.create_plot()
        interface = self.create_interface()
        display(interface)
        return self




    def choose_dft_files(self, b):
        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data


class EDCPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.global_k_min = float('inf')
        self.global_k_max = float('-inf')
        self.global_e_min = float('inf')
        self.global_e_max = float('-inf')
        self.global_intensity_max = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.preprocess_data()

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']

            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': os.path.basename(file_path)
            })

            self.global_k_min = min(self.global_k_min, np.min(k_parallel))
            self.global_k_max = max(self.global_k_max, np.max(k_parallel))
            self.global_e_min = min(self.global_e_min, np.min(energy_values))
            self.global_e_max = max(self.global_e_max, np.max(energy_values))
            self.global_intensity_max = max(self.global_intensity_max, np.max(data.values))

    def run(self):
        interactive_plot = self.create_interactive_plot()
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)
        output = VBox([interactive_plot, save_button])
        display(output)

    def create_interactive_plot(self):
        return interactive(
            self.plot_edc,
            scan_index=IntSlider(value=1, min=1, max=len(self.data_files), step=1, description='Scan Index', continuous_update=False),
            n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
            vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
            show_edc=Checkbox(value=True, description='Show EDCs'),
            show_fit=Checkbox(value=False, description='Show Fits'),
            num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
            k_min=FloatSlider(value=self.global_k_min, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_min (Å⁻¹)', continuous_update=True),
            k_max=FloatSlider(value=self.global_k_max, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_max (Å⁻¹)', continuous_update=True),
            e_min=FloatSlider(value=self.global_e_min, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_min (eV)', continuous_update=True),
            e_max=FloatSlider(value=self.global_e_max, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_max (eV)', continuous_update=True),
            use_canny=Checkbox(value=False, description='Use Canny Filter'),
            sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
            low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
            high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
            enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
            averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
        )

    def plot_edc(self, scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size):
        plot_data = self.all_plots[scan_index - 1]
        self.ax.clear()

        data_to_plot = plot_data['data_values'].copy()
        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel)

        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            if not use_canny:
                kdc = kdc / np.max(kdc)

            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                self.ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')

            self.ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                self.ax.plot(energy_values, offset_fit, '--', label=fr'Fit $k_\parallel$ = {actual_k:.2f}', color=f'C{i}')

                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)
                self.ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                self.ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        self.ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        self.ax.legend()
        self.ax.tick_params(axis='both', which='major', labelsize=10)

        self.ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        self.ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        self.fig.canvas.draw_idle()



    def save_plot(self, b):
        current_values = {
            'scan_index': self.interactive_plot.children[0].value,
            'n': self.interactive_plot.children[1].value,
            'vertical_offset': self.interactive_plot.children[2].value,
            'show_edc': self.interactive_plot.children[3].value,
            'show_fit': self.interactive_plot.children[4].value,
            'num_peaks': self.interactive_plot.children[5].value,
            'k_min': self.interactive_plot.children[6].value,
            'k_max': self.interactive_plot.children[7].value,
            'e_min': self.interactive_plot.children[8].value,
            'e_max': self.interactive_plot.children[9].value,
            'use_canny': self.interactive_plot.children[10].value,
            'sigma': self.interactive_plot.children[11].value,
            'low_threshold': self.interactive_plot.children[12].value,
            'high_threshold': self.interactive_plot.children[13].value,
            'enable_averaging': self.interactive_plot.children[14].value,
            'averaging_kernel_size': self.interactive_plot.children[15].value
        }
        self.plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")
if __name__ == "__main__":
    suite = ARPESAnalysisSuite()
    suite.load_data_files()
    
    # Add plot modules
    suite.add_plot_module("K-Space Plot", KSpacePlotModule)
    suite.add_plot_module("EDC Plot", EDCPlotModule)
    
    suite.run()
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from ipywidgets import Dropdown, VBox, Output
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
import matplotlib.pyplot as plt
from ipywidgets import Output, VBox, Dropdown
from matplotlib.backends.backend_agg import FigureCanvasAgg
from IPython.display import display, clear_output

get_ipython().run_line_magic('matplotlib', 'widget')
# Suppress warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128


class ARPESAnalysisSuite:
    def __init__(self):
        self.data_files = []
        self.work_function = 4.5
        self.plot_modules = {}
        self.filter_modules = {}
        self.dft_data = []
        self.current_plot = None
        self.plot_output = Output()
        self.main_output = Output()


    def load_data_files(self):
        root = tk.Tk()
        root.withdraw()
        folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
        root.destroy()
        if folder_path:
            self.data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
        else:
            print("No folder selected.")

    def add_plot_module(self, name, module_class):
        self.plot_modules[name] = module_class

    def add_filter_module(self, name, module):
        self.filter_modules[name] = module

    def run(self):
        if not self.data_files:
            print("No data files loaded. Please load data files first.")
            return
        
        plot_selector = self.create_plot_selector()
        main_interface = VBox([plot_selector, self.plot_output])
        display(main_interface)
        self.update_plot(list(self.plot_modules.keys())[0])

    def create_plot_selector(self):
        plot_options = list(self.plot_modules.keys())
        plot_dropdown = Dropdown(options=plot_options, description='Plot Type:')
        plot_dropdown.observe(self.on_plot_change, names='value')
        return plot_dropdown

    def on_plot_change(self, change):
        self.update_plot(change.new)

    def update_plot(self, plot_type):
        with self.plot_output:
            clear_output(wait=True)
            plt.close('all')  # Close all existing figures
            if plot_type in self.plot_modules:
                module_class = self.plot_modules[plot_type]
                module_instance = module_class(self.data_files, self.work_function)
                self.current_plot = module_instance.run()
class ARPESPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.interactive_widgets = {}
        self.filter_widgets = {}
        self.dft_data = []
        self.scan_types = self.determine_scan_type()
        self.rainbow_light = self.rainbowlightct()
        self.add_filter_widget('canny_enabled', Checkbox(value=False, description='Canny Edge Detection'))
        self.add_filter_widget('moving_average_enabled', Checkbox(value=False, description='Moving Average'))
        self.preprocess_data()  # Call this before initialize_widgets
        self.initialize_widgets()

    def initialize_widgets(self):
        self.add_interactive_widget('scan_index', IntSlider(
            value=1, min=1, max=len(self.data_files), step=1,
            description='Scan Index', continuous_update=False,
            layout=Layout(width='50%')
        ))
        self.add_interactive_widget('vmin', FloatSlider(
            value=self.min_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Min Value',
            continuous_update=False
        ))
        self.add_interactive_widget('vmax', FloatSlider(
            value=self.max_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Max Value',
            continuous_update=False
        ))
        self.add_interactive_widget('scale', FloatSlider(
            value=1.0, min=0.1, max=2.0, step=0.1,
            description='Scale', continuous_update=False
        ))
        self.add_interactive_widget('sigma', FloatSlider(
            value=1.0, min=0.1, max=20.0, step=0.1,
            description='Canny Sigma', continuous_update=False
        ))
        self.add_interactive_widget('low_threshold', FloatSlider(
            value=0.1, min=0.0, max=1.0, step=0.01,
            description='Canny Low Threshold', continuous_update=False
        ))
        self.add_interactive_widget('high_threshold', FloatSlider(
            value=0.2, min=0.0, max=1.0, step=0.01,
            description='Canny High Threshold', continuous_update=False
        ))
        self.add_interactive_widget('moving_average_size', IntSlider(
            value=3, min=3, max=50, step=1,
            description='Moving Average Size', continuous_update=False
        ))

    def rainbowlightct(self):
        # Normalize the RGB values to the range 0-1
        normalized_data = igor_data / 65535.0
        
        # Create the ListedColormap
        return ListedColormap(normalized_data)


    def determine_scan_type(self):
        scan_types = []
        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            if 'polar' in data.attrs:
                scan_types.append(('polar', data.attrs['polar']))
            elif 'hv' in data.attrs:
                scan_types.append(('hv', data.attrs['hv']))
            else:
                scan_types.append(('unknown', None))
        return scan_types

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10
            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })
            self.max_data_value = max(self.max_data_value, np.max(data.values))

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap = self.rainbowlightct(), norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, canny_enabled, sigma, low_threshold, high_threshold, moving_average_enabled, moving_average_size, dft_x_offset, dft_y_offset):
        with self.output:
            clear_output(wait=True)
            plt.close('all')  # Close all existing figures
        
        # ... (rest of the method remains the same)
        
        
            plot_data = self.all_plots[scan_index - 1]
            data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbowlightct()

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
        display(self.fig)

    def moving_average(self, data, window_size):
        kernel = np.ones((window_size, window_size)) / (window_size ** 2)
        return np.convolve(data.flatten(), kernel.flatten(), mode='same').reshape(data.shape)

    def add_interactive_widget(self, name, widget):
        self.interactive_widgets[name] = widget

    def add_filter_widget(self, name, widget):
        self.filter_widgets[name] = widget

    def create_interface(self):
        interactive_plot = interactive(self.update_plot, **self.interactive_widgets)
        filter_checkboxes = [widget for widget in self.filter_widgets.values()]
        
        choose_dft_button = Button(description="Choose DFT")
        choose_dft_button.on_click(self.choose_dft_files)
        
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)

        output = VBox([
            interactive_plot,
            HBox(filter_checkboxes),
            HBox([save_button, choose_dft_button])
        ])
        return output


    def choose_dft_files(self, b):
        import tkinter as tk
        from tkinter import filedialog

        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data

    def save_plot(self, b):
        current_values = {name: widget.value for name, widget in self.interactive_widgets.items()}
        plot_data = self.all_plots[current_values['scan_index'] - 1]
        current_file = plot_data['file_name']
        save_folder = os.path.dirname(current_file)
        filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def run(self):
        self.preprocess_data()
        self.create_plot()
        self.output = Output()
        with self.output:
            display(self.fig)
        interface = self.create_interface()
        display(VBox([self.output, interface]))


class KSpacePlotModule(ARPESPlotModule):
    def __init__(self, data_files, work_function):
        self.hbar = 1.054571817e-34  # J*s
        self.m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        super().__init__(data_files, work_function)
        self.preprocess_data()  # Call this again to calculate k-space specific values
        

    def initialize_widgets(self):
        super().initialize_widgets()
        self.add_interactive_widget('dft_x_offset', FloatSlider(
            value=0, min=self.min_k, max=self.max_k,
            step=(self.max_k - self.min_k) / 200,
            description='DFT X Offset', continuous_update=True
        ))
        self.add_interactive_widget('dft_y_offset', FloatSlider(
            value=0, min=self.min_energy, max=self.max_energy,
            step=(self.max_energy - self.min_energy) / 200,
            description='DFT Y Offset', continuous_update=True
        ))


    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * self.m_e * E_b) * np.sin(np.radians(data.phi)) / self.hbar) / 10**10
            energy_values = -data.eV.values
            
            self.min_k = min(self.min_k, np.min(k_parallel))
            self.max_k = max(self.max_k, np.max(k_parallel))
            self.min_energy = min(self.min_energy, np.min(energy_values))
            self.max_energy = max(self.max_energy, np.max(energy_values))
            self.min_data_value = min(self.min_data_value, np.min(data.values))
            self.max_data_value = max(self.max_data_value, np.max(data.values))

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap=self.rainbow_light, norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, sigma, low_threshold, high_threshold, moving_average_size, dft_x_offset, dft_y_offset):
        clear_output(wait=True)
        plt.close('all')  # Close all existing figures
        
        canny_enabled = self.filter_widgets['canny_enabled'].value
        moving_average_enabled = self.filter_widgets['moving_average_enabled'].value
        plot_data = self.all_plots[scan_index - 1]
        data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbow_light

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
        display(self.fig)

    def run(self):
        self.preprocess_data()
        self.create_plot()
        interface = self.create_interface()
        display(interface)
        return self




    def choose_dft_files(self, b):
        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data


class EDCPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.global_k_min = float('inf')
        self.global_k_max = float('-inf')
        self.global_e_min = float('inf')
        self.global_e_max = float('-inf')
        self.global_intensity_max = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.preprocess_data()

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']

            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': os.path.basename(file_path)
            })

            self.global_k_min = min(self.global_k_min, np.min(k_parallel))
            self.global_k_max = max(self.global_k_max, np.max(k_parallel))
            self.global_e_min = min(self.global_e_min, np.min(energy_values))
            self.global_e_max = max(self.global_e_max, np.max(energy_values))
            self.global_intensity_max = max(self.global_intensity_max, np.max(data.values))

    def run(self):
        interactive_plot = self.create_interactive_plot()
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)
        output = VBox([interactive_plot, save_button])
        display(output)

    def create_interactive_plot(self):
        return interactive(
            self.plot_edc,
            scan_index=IntSlider(value=1, min=1, max=len(self.data_files), step=1, description='Scan Index', continuous_update=False),
            n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
            vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
            show_edc=Checkbox(value=True, description='Show EDCs'),
            show_fit=Checkbox(value=False, description='Show Fits'),
            num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
            k_min=FloatSlider(value=self.global_k_min, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_min (Å⁻¹)', continuous_update=True),
            k_max=FloatSlider(value=self.global_k_max, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_max (Å⁻¹)', continuous_update=True),
            e_min=FloatSlider(value=self.global_e_min, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_min (eV)', continuous_update=True),
            e_max=FloatSlider(value=self.global_e_max, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_max (eV)', continuous_update=True),
            use_canny=Checkbox(value=False, description='Use Canny Filter'),
            sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
            low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
            high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
            enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
            averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
        )

    def plot_edc(self, scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size):
        clear_output(wait=True)
        plt.close('all')  # Close all existing figures
        
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        plot_data = self.all_plots[scan_index - 1]
        self.ax.clear()

        data_to_plot = plot_data['data_values'].copy()
        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel)

        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            if not use_canny:
                kdc = kdc / np.max(kdc)

            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                self.ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')

            self.ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                self.ax.plot(energy_values, offset_fit, '--', label=fr'Fit $k_\parallel$ = {actual_k:.2f}', color=f'C{i}')

                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)
                self.ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                self.ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        self.ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        self.ax.legend()
        self.ax.tick_params(axis='both', which='major', labelsize=10)

        self.ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        self.ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        display(self.fig)



    def save_plot(self, b):
        current_values = {
            'scan_index': self.interactive_plot.children[0].value,
            'n': self.interactive_plot.children[1].value,
            'vertical_offset': self.interactive_plot.children[2].value,
            'show_edc': self.interactive_plot.children[3].value,
            'show_fit': self.interactive_plot.children[4].value,
            'num_peaks': self.interactive_plot.children[5].value,
            'k_min': self.interactive_plot.children[6].value,
            'k_max': self.interactive_plot.children[7].value,
            'e_min': self.interactive_plot.children[8].value,
            'e_max': self.interactive_plot.children[9].value,
            'use_canny': self.interactive_plot.children[10].value,
            'sigma': self.interactive_plot.children[11].value,
            'low_threshold': self.interactive_plot.children[12].value,
            'high_threshold': self.interactive_plot.children[13].value,
            'enable_averaging': self.interactive_plot.children[14].value,
            'averaging_kernel_size': self.interactive_plot.children[15].value
        }
        self.plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")
if __name__ == "__main__":
    suite = ARPESAnalysisSuite()
    suite.load_data_files()
    
    # Add plot modules
    suite.add_plot_module("K-Space Plot", KSpacePlotModule)
    suite.add_plot_module("EDC Plot", EDCPlotModule)
    
    suite.run()
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from ipywidgets import Dropdown, VBox, Output
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
import matplotlib.pyplot as plt
from ipywidgets import Output, VBox, Dropdown
from matplotlib.backends.backend_agg import FigureCanvasAgg
from IPython.display import display, clear_output

get_ipython().run_line_magic('matplotlib', 'widget')
# Suppress warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128


class ARPESAnalysisSuite:
    def __init__(self):
        self.data_files = []
        self.work_function = 4.5
        self.plot_modules = {}
        self.filter_modules = {}
        self.dft_data = []
        self.current_plot = None
        self.plot_output = Output()
        self.main_output = Output()


    def load_data_files(self):
        root = tk.Tk()
        root.withdraw()
        folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
        root.destroy()
        if folder_path:
            self.data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
        else:
            print("No folder selected.")

    def add_plot_module(self, name, module_class):
        self.plot_modules[name] = module_class

    def add_filter_module(self, name, module):
        self.filter_modules[name] = module

    def run(self):
        if not self.data_files:
            print("No data files loaded. Please load data files first.")
            return
        
        plot_selector = self.create_plot_selector()
        main_interface = VBox([plot_selector, self.plot_output])
        display(main_interface)
        self.update_plot(list(self.plot_modules.keys())[0])

    def create_plot_selector(self):
        plot_options = list(self.plot_modules.keys())
        plot_dropdown = Dropdown(options=plot_options, description='Plot Type:')
        plot_dropdown.observe(self.on_plot_change, names='value')
        return plot_dropdown

    def on_plot_change(self, change):
        self.update_plot(change.new)

    def update_plot(self, plot_type):
        with self.plot_output:
            clear_output(wait=True)
            plt.close('all')  # Close all existing figures
            if plot_type in self.plot_modules:
                module_class = self.plot_modules[plot_type]
                module_instance = module_class(self.data_files, self.work_function)
                self.current_plot = module_instance.run()
class ARPESPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.interactive_widgets = {}
        self.filter_widgets = {}
        self.dft_data = []
        self.scan_types = self.determine_scan_type()
        self.rainbow_light = self.rainbowlightct()
        self.add_filter_widget('canny_enabled', Checkbox(value=False, description='Canny Edge Detection'))
        self.add_filter_widget('moving_average_enabled', Checkbox(value=False, description='Moving Average'))
        self.preprocess_data()  # Call this before initialize_widgets
        self.initialize_widgets()

    def initialize_widgets(self):
        self.add_interactive_widget('scan_index', IntSlider(
            value=1, min=1, max=len(self.data_files), step=1,
            description='Scan Index', continuous_update=False,
            layout=Layout(width='50%')
        ))
        self.add_interactive_widget('vmin', FloatSlider(
            value=self.min_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Min Value',
            continuous_update=False
        ))
        self.add_interactive_widget('vmax', FloatSlider(
            value=self.max_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Max Value',
            continuous_update=False
        ))
        self.add_interactive_widget('scale', FloatSlider(
            value=1.0, min=0.1, max=2.0, step=0.1,
            description='Scale', continuous_update=False
        ))
        self.add_interactive_widget('sigma', FloatSlider(
            value=1.0, min=0.1, max=20.0, step=0.1,
            description='Canny Sigma', continuous_update=False
        ))
        self.add_interactive_widget('low_threshold', FloatSlider(
            value=0.1, min=0.0, max=1.0, step=0.01,
            description='Canny Low Threshold', continuous_update=False
        ))
        self.add_interactive_widget('high_threshold', FloatSlider(
            value=0.2, min=0.0, max=1.0, step=0.01,
            description='Canny High Threshold', continuous_update=False
        ))
        self.add_interactive_widget('moving_average_size', IntSlider(
            value=3, min=3, max=50, step=1,
            description='Moving Average Size', continuous_update=False
        ))

    def rainbowlightct(self):
        # Normalize the RGB values to the range 0-1
        normalized_data = igor_data / 65535.0
        
        # Create the ListedColormap
        return ListedColormap(normalized_data)


    def determine_scan_type(self):
        scan_types = []
        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            if 'polar' in data.attrs:
                scan_types.append(('polar', data.attrs['polar']))
            elif 'hv' in data.attrs:
                scan_types.append(('hv', data.attrs['hv']))
            else:
                scan_types.append(('unknown', None))
        return scan_types

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10
            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })
            self.max_data_value = max(self.max_data_value, np.max(data.values))

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap = self.rainbowlightct(), norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, canny_enabled, sigma, low_threshold, high_threshold, moving_average_enabled, moving_average_size, dft_x_offset, dft_y_offset):
        with self.output:
            clear_output(wait=True)
            plt.close('all')  # Close all existing figures
        
        # ... (rest of the method remains the same)
        
        
            plot_data = self.all_plots[scan_index - 1]
            data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbowlightct()

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
        display(self.fig)

    def moving_average(self, data, window_size):
        kernel = np.ones((window_size, window_size)) / (window_size ** 2)
        return np.convolve(data.flatten(), kernel.flatten(), mode='same').reshape(data.shape)

    def add_interactive_widget(self, name, widget):
        self.interactive_widgets[name] = widget

    def add_filter_widget(self, name, widget):
        self.filter_widgets[name] = widget

    def create_interface(self):
        interactive_plot = interactive(self.update_plot, **self.interactive_widgets)
        filter_checkboxes = [widget for widget in self.filter_widgets.values()]
        
        choose_dft_button = Button(description="Choose DFT")
        choose_dft_button.on_click(self.choose_dft_files)
        
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)

        output = VBox([
            interactive_plot,
            HBox(filter_checkboxes),
            HBox([save_button, choose_dft_button])
        ])
        return output


    def choose_dft_files(self, b):
        import tkinter as tk
        from tkinter import filedialog

        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data

    def save_plot(self, b):
        current_values = {name: widget.value for name, widget in self.interactive_widgets.items()}
        plot_data = self.all_plots[current_values['scan_index'] - 1]
        current_file = plot_data['file_name']
        save_folder = os.path.dirname(current_file)
        filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def run(self):
        self.preprocess_data()
        self.create_plot()
        self.output = Output()
        with self.output:
            display(self.fig)
        interface = self.create_interface()
        display(VBox([self.output, interface]))


class KSpacePlotModule(ARPESPlotModule):
    def __init__(self, data_files, work_function):
        self.hbar = 1.054571817e-34  # J*s
        self.m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        super().__init__(data_files, work_function)
        self.preprocess_data()  # Call this again to calculate k-space specific values
        

    def initialize_widgets(self):
        super().initialize_widgets()
        self.add_interactive_widget('dft_x_offset', FloatSlider(
            value=0, min=self.min_k, max=self.max_k,
            step=(self.max_k - self.min_k) / 200,
            description='DFT X Offset', continuous_update=True
        ))
        self.add_interactive_widget('dft_y_offset', FloatSlider(
            value=0, min=self.min_energy, max=self.max_energy,
            step=(self.max_energy - self.min_energy) / 200,
            description='DFT Y Offset', continuous_update=True
        ))


    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * self.m_e * E_b) * np.sin(np.radians(data.phi)) / self.hbar) / 10**10
            energy_values = -data.eV.values
            
            self.min_k = min(self.min_k, np.min(k_parallel))
            self.max_k = max(self.max_k, np.max(k_parallel))
            self.min_energy = min(self.min_energy, np.min(energy_values))
            self.max_energy = max(self.max_energy, np.max(energy_values))
            self.min_data_value = min(self.min_data_value, np.min(data.values))
            self.max_data_value = max(self.max_data_value, np.max(data.values))

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap=self.rainbow_light, norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, sigma, low_threshold, high_threshold, moving_average_size, dft_x_offset, dft_y_offset):
        clear_output(wait=True)
        plt.close('all')  # Close all existing figures
        
        canny_enabled = self.filter_widgets['canny_enabled'].value
        moving_average_enabled = self.filter_widgets['moving_average_enabled'].value
        plot_data = self.all_plots[scan_index - 1]
        data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbow_light

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
        display(self.fig)

    def run(self):
        self.preprocess_data()
        self.create_plot()
        interface = self.create_interface()
        display(interface)
        return self




    def choose_dft_files(self, b):
        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data


class EDCPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.global_k_min = float('inf')
        self.global_k_max = float('-inf')
        self.global_e_min = float('inf')
        self.global_e_max = float('-inf')
        self.global_intensity_max = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.preprocess_data()

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']

            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': os.path.basename(file_path)
            })

            self.global_k_min = min(self.global_k_min, np.min(k_parallel))
            self.global_k_max = max(self.global_k_max, np.max(k_parallel))
            self.global_e_min = min(self.global_e_min, np.min(energy_values))
            self.global_e_max = max(self.global_e_max, np.max(energy_values))
            self.global_intensity_max = max(self.global_intensity_max, np.max(data.values))

    def run(self):
        interactive_plot = self.create_interactive_plot()
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)
        output = VBox([interactive_plot, save_button])
        display(output)

    def create_interactive_plot(self):
        return interactive(
            self.plot_edc,
            scan_index=IntSlider(value=1, min=1, max=len(self.data_files), step=1, description='Scan Index', continuous_update=False),
            n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
            vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
            show_edc=Checkbox(value=True, description='Show EDCs'),
            show_fit=Checkbox(value=False, description='Show Fits'),
            num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
            k_min=FloatSlider(value=self.global_k_min, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_min (Å⁻¹)', continuous_update=True),
            k_max=FloatSlider(value=self.global_k_max, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_max (Å⁻¹)', continuous_update=True),
            e_min=FloatSlider(value=self.global_e_min, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_min (eV)', continuous_update=True),
            e_max=FloatSlider(value=self.global_e_max, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_max (eV)', continuous_update=True),
            use_canny=Checkbox(value=False, description='Use Canny Filter'),
            sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
            low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
            high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
            enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
            averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
        )

    def plot_edc(self, scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size):
        clear_output(wait=True)
        plt.close('all')  # Close all existing figures
        
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        plot_data = self.all_plots[scan_index - 1]
        self.ax.clear()

        data_to_plot = plot_data['data_values'].copy()
        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel)

        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            if not use_canny:
                kdc = kdc / np.max(kdc)

            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                self.ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')

            self.ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                self.ax.plot(energy_values, offset_fit, '--', label=fr'Fit $k_\parallel$ = {actual_k:.2f}', color=f'C{i}')

                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)
                self.ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                self.ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        self.ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        self.ax.legend()
        self.ax.tick_params(axis='both', which='major', labelsize=10)

        self.ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        self.ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        display(self.fig)



    def save_plot(self, b):
        current_values = {
            'scan_index': self.interactive_plot.children[0].value,
            'n': self.interactive_plot.children[1].value,
            'vertical_offset': self.interactive_plot.children[2].value,
            'show_edc': self.interactive_plot.children[3].value,
            'show_fit': self.interactive_plot.children[4].value,
            'num_peaks': self.interactive_plot.children[5].value,
            'k_min': self.interactive_plot.children[6].value,
            'k_max': self.interactive_plot.children[7].value,
            'e_min': self.interactive_plot.children[8].value,
            'e_max': self.interactive_plot.children[9].value,
            'use_canny': self.interactive_plot.children[10].value,
            'sigma': self.interactive_plot.children[11].value,
            'low_threshold': self.interactive_plot.children[12].value,
            'high_threshold': self.interactive_plot.children[13].value,
            'enable_averaging': self.interactive_plot.children[14].value,
            'averaging_kernel_size': self.interactive_plot.children[15].value
        }
        self.plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")
if __name__ == "__main__":
    suite = ARPESAnalysisSuite()
    suite.load_data_files()
    
    # Add plot modules
    suite.add_plot_module("K-Space Plot", KSpacePlotModule)
    suite.add_plot_module("EDC Plot", EDCPlotModule)
    
    suite.run()
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from ipywidgets import Dropdown, VBox, Output
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
import matplotlib.pyplot as plt
from ipywidgets import Output, VBox, Dropdown
from matplotlib.backends.backend_agg import FigureCanvasAgg
from IPython.display import display, clear_output

get_ipython().run_line_magic('matplotlib', 'widget')
# Suppress warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128


class ARPESAnalysisSuite:
    def __init__(self):
        self.data_files = []
        self.work_function = 4.5
        self.plot_modules = {}
        self.filter_modules = {}
        self.dft_data = []
        self.current_plot = None
        self.plot_output = Output()
        self.main_output = Output()


    def load_data_files(self):
        root = tk.Tk()
        root.withdraw()
        folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
        root.destroy()
        if folder_path:
            self.data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
        else:
            print("No folder selected.")

    def add_plot_module(self, name, module_class):
        self.plot_modules[name] = module_class

    def add_filter_module(self, name, module):
        self.filter_modules[name] = module

    def run(self):
        if not self.data_files:
            print("No data files loaded. Please load data files first.")
            return
        
        plot_selector = self.create_plot_selector()
        main_interface = VBox([plot_selector, self.plot_output])
        display(main_interface)
        self.update_plot(list(self.plot_modules.keys())[0])

    def create_plot_selector(self):
        plot_options = list(self.plot_modules.keys())
        plot_dropdown = Dropdown(options=plot_options, description='Plot Type:')
        plot_dropdown.observe(self.on_plot_change, names='value')
        return plot_dropdown

    def on_plot_change(self, change):
        self.update_plot(change.new)

    def update_plot(self, plot_type):
        with self.plot_output:
            clear_output(wait=True)
            plt.close('all')  # Close all existing figures
            if plot_type in self.plot_modules:
                module_class = self.plot_modules[plot_type]
                module_instance = module_class(self.data_files, self.work_function)
                self.current_plot = module_instance.run()
class ARPESPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.interactive_widgets = {}
        self.filter_widgets = {}
        self.dft_data = []
        self.scan_types = self.determine_scan_type()
        self.rainbow_light = self.rainbowlightct()
        self.add_filter_widget('canny_enabled', Checkbox(value=False, description='Canny Edge Detection'))
        self.add_filter_widget('moving_average_enabled', Checkbox(value=False, description='Moving Average'))
        self.preprocess_data()  # Call this before initialize_widgets
        self.initialize_widgets()

    def initialize_widgets(self):
        self.add_interactive_widget('scan_index', IntSlider(
            value=1, min=1, max=len(self.data_files), step=1,
            description='Scan Index', continuous_update=False,
            layout=Layout(width='50%')
        ))
        self.add_interactive_widget('vmin', FloatSlider(
            value=self.min_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Min Value',
            continuous_update=False
        ))
        self.add_interactive_widget('vmax', FloatSlider(
            value=self.max_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Max Value',
            continuous_update=False
        ))
        self.add_interactive_widget('scale', FloatSlider(
            value=1.0, min=0.1, max=2.0, step=0.1,
            description='Scale', continuous_update=False
        ))
        self.add_interactive_widget('sigma', FloatSlider(
            value=1.0, min=0.1, max=20.0, step=0.1,
            description='Canny Sigma', continuous_update=False
        ))
        self.add_interactive_widget('low_threshold', FloatSlider(
            value=0.1, min=0.0, max=1.0, step=0.01,
            description='Canny Low Threshold', continuous_update=False
        ))
        self.add_interactive_widget('high_threshold', FloatSlider(
            value=0.2, min=0.0, max=1.0, step=0.01,
            description='Canny High Threshold', continuous_update=False
        ))
        self.add_interactive_widget('moving_average_size', IntSlider(
            value=3, min=3, max=50, step=1,
            description='Moving Average Size', continuous_update=False
        ))

    def rainbowlightct(self):
        # Normalize the RGB values to the range 0-1
        normalized_data = igor_data / 65535.0
        
        # Create the ListedColormap
        return ListedColormap(normalized_data)


    def determine_scan_type(self):
        scan_types = []
        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            if 'polar' in data.attrs:
                scan_types.append(('polar', data.attrs['polar']))
            elif 'hv' in data.attrs:
                scan_types.append(('hv', data.attrs['hv']))
            else:
                scan_types.append(('unknown', None))
        return scan_types

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10
            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })
            self.max_data_value = max(self.max_data_value, np.max(data.values))

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap = self.rainbowlightct(), norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, canny_enabled, sigma, low_threshold, high_threshold, moving_average_enabled, moving_average_size, dft_x_offset, dft_y_offset):
        with self.output:
            clear_output(wait=True)
            plt.close('all')  # Close all existing figures
        
        # ... (rest of the method remains the same)
        
        
            plot_data = self.all_plots[scan_index - 1]
            data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbowlightct()

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
        display(self.fig)

    def moving_average(self, data, window_size):
        kernel = np.ones((window_size, window_size)) / (window_size ** 2)
        return np.convolve(data.flatten(), kernel.flatten(), mode='same').reshape(data.shape)

    def add_interactive_widget(self, name, widget):
        self.interactive_widgets[name] = widget

    def add_filter_widget(self, name, widget):
        self.filter_widgets[name] = widget

    def create_interface(self):
        interactive_plot = interactive(self.update_plot, **self.interactive_widgets)
        filter_checkboxes = [widget for widget in self.filter_widgets.values()]
        
        choose_dft_button = Button(description="Choose DFT")
        choose_dft_button.on_click(self.choose_dft_files)
        
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)

        output = VBox([
            interactive_plot,
            HBox(filter_checkboxes),
            HBox([save_button, choose_dft_button])
        ])
        return output


    def choose_dft_files(self, b):
        import tkinter as tk
        from tkinter import filedialog

        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data

    def save_plot(self, b):
        current_values = {name: widget.value for name, widget in self.interactive_widgets.items()}
        plot_data = self.all_plots[current_values['scan_index'] - 1]
        current_file = plot_data['file_name']
        save_folder = os.path.dirname(current_file)
        filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def run(self):
        self.preprocess_data()
        self.create_plot()
        self.output = Output()
        with self.output:
            display(self.fig)
        interface = self.create_interface()
        display(VBox([self.output, interface]))


class KSpacePlotModule(ARPESPlotModule):
    def __init__(self, data_files, work_function):
        self.hbar = 1.054571817e-34  # J*s
        self.m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        super().__init__(data_files, work_function)
        self.preprocess_data()  # Call this again to calculate k-space specific values
        

    def initialize_widgets(self):
        super().initialize_widgets()
        self.add_interactive_widget('dft_x_offset', FloatSlider(
            value=0, min=self.min_k, max=self.max_k,
            step=(self.max_k - self.min_k) / 200,
            description='DFT X Offset', continuous_update=True
        ))
        self.add_interactive_widget('dft_y_offset', FloatSlider(
            value=0, min=self.min_energy, max=self.max_energy,
            step=(self.max_energy - self.min_energy) / 200,
            description='DFT Y Offset', continuous_update=True
        ))


    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * self.m_e * E_b) * np.sin(np.radians(data.phi)) / self.hbar) / 10**10
            energy_values = -data.eV.values
            
            self.min_k = min(self.min_k, np.min(k_parallel))
            self.max_k = max(self.max_k, np.max(k_parallel))
            self.min_energy = min(self.min_energy, np.min(energy_values))
            self.max_energy = max(self.max_energy, np.max(energy_values))
            self.min_data_value = min(self.min_data_value, np.min(data.values))
            self.max_data_value = max(self.max_data_value, np.max(data.values))

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap=self.rainbow_light, norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, sigma, low_threshold, high_threshold, moving_average_size, dft_x_offset, dft_y_offset):
        clear_output(wait=True)
        plt.close('all')  # Close all existing figures
        
        canny_enabled = self.filter_widgets['canny_enabled'].value
        moving_average_enabled = self.filter_widgets['moving_average_enabled'].value
        plot_data = self.all_plots[scan_index - 1]
        data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbow_light

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
        display(self.fig)

    def run(self):
        self.preprocess_data()
        self.create_plot()
        interface = self.create_interface()
        display(interface)
        return self




    def choose_dft_files(self, b):
        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data


class EDCPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.global_k_min = float('inf')
        self.global_k_max = float('-inf')
        self.global_e_min = float('inf')
        self.global_e_max = float('-inf')
        self.global_intensity_max = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.preprocess_data()

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']

            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': os.path.basename(file_path)
            })

            self.global_k_min = min(self.global_k_min, np.min(k_parallel))
            self.global_k_max = max(self.global_k_max, np.max(k_parallel))
            self.global_e_min = min(self.global_e_min, np.min(energy_values))
            self.global_e_max = max(self.global_e_max, np.max(energy_values))
            self.global_intensity_max = max(self.global_intensity_max, np.max(data.values))

    def run(self):
        clear_output(wait=True)
        plt.close('all')  # Close all existing figures
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        interactive_plot = self.create_interactive_plot()
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)
        output = VBox([interactive_plot, save_button])
        display(output)
    def create_interactive_plot(self):
        return interactive(
            self.plot_edc,
            scan_index=IntSlider(value=1, min=1, max=len(self.data_files), step=1, description='Scan Index', continuous_update=False),
            n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
            vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
            show_edc=Checkbox(value=True, description='Show EDCs'),
            show_fit=Checkbox(value=False, description='Show Fits'),
            num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
            k_min=FloatSlider(value=self.global_k_min, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_min (Å⁻¹)', continuous_update=True),
            k_max=FloatSlider(value=self.global_k_max, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_max (Å⁻¹)', continuous_update=True),
            e_min=FloatSlider(value=self.global_e_min, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_min (eV)', continuous_update=True),
            e_max=FloatSlider(value=self.global_e_max, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_max (eV)', continuous_update=True),
            use_canny=Checkbox(value=False, description='Use Canny Filter'),
            sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
            low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
            high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
            enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
            averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
        )

    def plot_edc(self, scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size):
        clear_output(wait=True)
        plt.close('all')  # Close all existing figures
        
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        plot_data = self.all_plots[scan_index - 1]
        self.ax.clear()

        data_to_plot = plot_data['data_values'].copy()
        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel)

        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            if not use_canny:
                kdc = kdc / np.max(kdc)

            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                self.ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')

            self.ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                self.ax.plot(energy_values, offset_fit, '--', label=fr'Fit $k_\parallel$ = {actual_k:.2f}', color=f'C{i}')

                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)
                self.ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                self.ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        self.ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        self.ax.legend()
        self.ax.tick_params(axis='both', which='major', labelsize=10)

        self.ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        self.ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        self.fig.canvas.draw_idle()
        display(self.fig)

    def save_plot(self, b):
        current_values = {
            'scan_index': self.interactive_plot.children[0].value,
            'n': self.interactive_plot.children[1].value,
            'vertical_offset': self.interactive_plot.children[2].value,
            'show_edc': self.interactive_plot.children[3].value,
            'show_fit': self.interactive_plot.children[4].value,
            'num_peaks': self.interactive_plot.children[5].value,
            'k_min': self.interactive_plot.children[6].value,
            'k_max': self.interactive_plot.children[7].value,
            'e_min': self.interactive_plot.children[8].value,
            'e_max': self.interactive_plot.children[9].value,
            'use_canny': self.interactive_plot.children[10].value,
            'sigma': self.interactive_plot.children[11].value,
            'low_threshold': self.interactive_plot.children[12].value,
            'high_threshold': self.interactive_plot.children[13].value,
            'enable_averaging': self.interactive_plot.children[14].value,
            'averaging_kernel_size': self.interactive_plot.children[15].value
        }
        self.plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")
if __name__ == "__main__":
    suite = ARPESAnalysisSuite()
    suite.load_data_files()
    
    # Add plot modules
    suite.add_plot_module("K-Space Plot", KSpacePlotModule)
    suite.add_plot_module("EDC Plot", EDCPlotModule)
    
    suite.run()
