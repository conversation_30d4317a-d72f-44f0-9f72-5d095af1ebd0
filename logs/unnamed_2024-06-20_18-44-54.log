# IPython log file

import numpy as np
import xarray as xr
import matplotlib.pyplot as plt

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = E_photon - work_function - np.abs(data.eV)
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Create the plot
plt.figure(figsize=(10, 8))
plt.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral_r')
plt.colorbar(label='Intensity')
plt.xlabel('k_parallel (Å^-1)')
plt.ylabel('Energy (eV)')
plt.title('ARPES Data in Momentum Space')

# Adjust the y-axis direction
plt.gca().invert_yaxis()

plt.show()
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt

# Load data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = E_photon - work_function - np.abs(data.eV)
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Create the plot
plt.figure(figsize=(10, 8))
plt.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral_r_r')  # Reversed colormap
plt.colorbar(label='Intensity')
plt.xlabel('k_parallel (Å^-1)')
plt.ylabel('Energy (eV)')
plt.title('ARPES Data in Momentum Space')

# Adjust the y-axis direction
plt.gca().invert_yaxis()

plt.show()
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = E_photon - work_function - np.abs(data.eV)
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Create the plot
plt.figure(figsize=(10, 8))
plt.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral_r')
plt.colorbar(label='Intensity')
plt.xlabel('k_parallel (Å^-1)')
plt.ylabel('Energy (eV)')
plt.title('ARPES Data in Momentum Space')

# Adjust the y-axis direction
plt.gca().invert_yaxis()

plt.show()
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = E_photon - work_function - np.abs(data.eV)
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Create the plot
plt.figure(figsize=(10, 8))
plt.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral')
plt.colorbar(label='Intensity')
plt.xlabel('k_parallel (Å^-1)')
plt.ylabel('Energy (eV)')
plt.title('ARPES Data in Momentum Space')

# Adjust the y-axis direction
plt.gca().invert_yaxis()

plt.show()
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from matplotlib.widgets import Slider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = E_photon - work_function - np.abs(data.eV)
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Create the figure and axis
fig, ax = plt.subplots(figsize=(10, 8))
plt.subplots_adjust(bottom=0.25)  # Make room for the slider

# Create the plot
im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral')
plt.colorbar(im, ax=ax, label='Intensity')
ax.set_xlabel('k_parallel (Å^-1)')
ax.set_ylabel('Energy (eV)')
ax.set_title('ARPES Data in Momentum Space')

# Adjust the y-axis direction
ax.invert_yaxis()

# Create the slider
ax_slider = plt.axes([0.25, 0.1, 0.65, 0.03])
intensity_slider = Slider(ax_slider, 'Intensity Normalization', 0.1, 10.0, valinit=1.0, valstep=0.1)

# Update the plot when the slider value changes
def update_plot(val):
    im.set_norm(plt.Normalize(vmin=data.values.min(), vmax=data.values.max() / val))
    fig.canvas.draw_idle()

intensity_slider.on_changed(update_plot)

plt.show()
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import Slider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = E_photon - work_function - np.abs(data.eV)
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Create the figure and axis
fig, ax = plt.subplots(figsize=(10, 8))
plt.subplots_adjust(bottom=0.25)  # Make room for the slider

# Create the plot
im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral')
plt.colorbar(im, ax=ax, label='Intensity')
ax.set_xlabel('k_parallel (Å^-1)')
ax.set_ylabel('Energy (eV)')
ax.set_title('ARPES Data in Momentum Space')

# Adjust the y-axis direction
ax.invert_yaxis()

# Create the slider
ax_slider = plt.axes([0.25, 0.1, 0.65, 0.03])
intensity_slider = Slider(ax_slider, 'Intensity Normalization', 0.1, 10.0, valinit=1.0, valstep=0.1)

# Update the plot when the slider value changes
def update_plot(val):
    im.set_norm(plt.Normalize(vmin=data.values.min(), vmax=data.values.max() / val))
    fig.canvas.draw_idle()

intensity_slider.on_changed(update_plot)

plt.show()
pip install ipywidgets

get_ipython().run_line_magic('pip', 'install ipywidgets')
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = E_photon - work_function - np.abs(data.eV)
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Define the plotting function
def plot_arpes(vmin):
    fig, ax = plt.subplots(figsize=(10, 8))
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.invert_yaxis()
    plt.show()

# Create the interactive widget
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=np.max(data.values), step=1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = E_photon - work_function - np.abs(data.eV)
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Define the plotting function
def plot_arpes(scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmin = np.min(data.values)
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.invert_yaxis()
    plt.show()

# Create the interactive widget
interactive_plot = interactive(plot_arpes, scale=FloatSlider(value=1.0, min=0.1, max=10.0, step=0.1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = E_photon - work_function - np.abs(data.eV)
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.invert_yaxis()
    plt.show()

# Create the interactive widget
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=np.max(data.values), step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=10.0, step=0.1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = E_photon - work_function - np.abs(data.eV)
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.invert_yaxis()
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
