# IPython log file

import tkinter as tk
from tkinter import filedialog
import matplotlib.pyplot as plt
import numpy as np

class InteractivePlot:
    def __init__(self, filenames):
        self.filenames = filenames
        self.fig, self.ax = plt.subplots(figsize=(12, 6))
        self.colors = ['blue', 'red']
        self.annotations = []
        self.plot_data()
        self.setup_interaction()

    def plot_data(self):
        for i, filename in enumerate(self.filenames):
            data = np.loadtxt(filename)
            x, y = data[:, 0], data[:, 1]
            self.ax.plot(y, color=self.colors[i], label=f'Data from {filename}')
            
            if i == 0:
                self.ax.set_xlabel(f'X-axis for {filename}', color=self.colors[i])
                self.ax.tick_params(axis='x', colors=self.colors[i])
                self.ax.set_xlim(0, len(y) - 1)
                self.ax.set_xticks(np.linspace(0, len(y) - 1, 5))
                self.ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
            else:
                ax2 = self.ax.twiny()
                ax2.set_xlim(self.ax.get_xlim())
                ax2.set_xlabel(f'X-axis for {filename}', color=self.colors[i])
                ax2.tick_params(axis='x', colors=self.colors[i])
                ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
                ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

        self.ax.set_ylabel('Y-axis')
        self.ax.set_title('Overlaid Data from Two .dat Files')
        self.ax.grid(True)
        self.ax.legend()
        plt.tight_layout()

    def setup_interaction(self):
        self.fig.canvas.mpl_connect('button_press_event', self.on_click)

    def on_click(self, event):
        if event.inaxes == self.ax:
            x, y = event.xdata, event.ydata
            label = f'({x:.2f}, {y:.2f})'
            annotation = self.ax.annotate(label, (x, y), xytext=(10, 10), 
                                          textcoords='offset points',
                                          bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.5),
                                          arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
            self.annotations.append(annotation)
            self.fig.canvas.draw()

def select_files():
    root = tk.Tk()
    root.withdraw()
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        plot = InteractivePlot(filenames)
        plt.show()
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
import tkinter as tk
from tkinter import filedialog
import matplotlib
matplotlib.use('TkAgg')  # Force the use of TkAgg backend
import matplotlib.pyplot as plt
import numpy as np

class InteractivePlot:
    def __init__(self, filenames):
        self.filenames = filenames
        self.fig, self.ax = plt.subplots(figsize=(12, 6))
        self.colors = ['blue', 'red']
        self.annotations = []
        self.plot_data()
        self.setup_interaction()

    def plot_data(self):
        for i, filename in enumerate(self.filenames):
            data = np.loadtxt(filename)
            x, y = data[:, 0], data[:, 1]
            self.ax.plot(y, color=self.colors[i], label=f'Data from {filename}')
            
            if i == 0:
                self.ax.set_xlabel(f'X-axis for {filename}', color=self.colors[i])
                self.ax.tick_params(axis='x', colors=self.colors[i])
                self.ax.set_xlim(0, len(y) - 1)
                self.ax.set_xticks(np.linspace(0, len(y) - 1, 5))
                self.ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
            else:
                ax2 = self.ax.twiny()
                ax2.set_xlim(self.ax.get_xlim())
                ax2.set_xlabel(f'X-axis for {filename}', color=self.colors[i])
                ax2.tick_params(axis='x', colors=self.colors[i])
                ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
                ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

        self.ax.set_ylabel('Y-axis')
        self.ax.set_title('Overlaid Data from Two .dat Files')
        self.ax.grid(True)
        self.ax.legend()
        plt.tight_layout()

    def setup_interaction(self):
        self.cid = self.fig.canvas.mpl_connect('button_press_event', self.on_click)
        print("Interaction setup complete. Click event connected.")

    def on_click(self, event):
        print(f"Click detected at x={event.xdata}, y={event.ydata}")
        if event.inaxes == self.ax:
            x, y = event.xdata, event.ydata
            label = f'({x:.2f}, {y:.2f})'
            annotation = self.ax.annotate(label, (x, y), xytext=(10, 10), 
                                          textcoords='offset points',
                                          bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.5),
                                          arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
            self.annotations.append(annotation)
            self.fig.canvas.draw()
            print(f"Annotation added at ({x:.2f}, {y:.2f})")

def select_files():
    root = tk.Tk()
    root.withdraw()
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        plot = InteractivePlot(filenames)
        plt.show()
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
import tkinter as tk
from tkinter import filedialog
import matplotlib.pyplot as plt
import numpy as np
from IPython.display import display, clear_output
get_ipython().run_line_magic('matplotlib', 'inline')

class InteractivePlot:
    def __init__(self, filenames):
        self.filenames = filenames
        self.fig, self.ax = plt.subplots(figsize=(12, 6))
        self.colors = ['blue', 'red']
        self.annotations = []
        self.plot_data()
        self.setup_interaction()

    def plot_data(self):
        for i, filename in enumerate(self.filenames):
            data = np.loadtxt(filename)
            x, y = data[:, 0], data[:, 1]
            self.ax.plot(y, color=self.colors[i], label=f'Data from {filename}')
            
            if i == 0:
                self.ax.set_xlabel(f'X-axis for {filename}', color=self.colors[i])
                self.ax.tick_params(axis='x', colors=self.colors[i])
                self.ax.set_xlim(0, len(y) - 1)
                self.ax.set_xticks(np.linspace(0, len(y) - 1, 5))
                self.ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
            else:
                ax2 = self.ax.twiny()
                ax2.set_xlim(self.ax.get_xlim())
                ax2.set_xlabel(f'X-axis for {filename}', color=self.colors[i])
                ax2.tick_params(axis='x', colors=self.colors[i])
                ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
                ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

        self.ax.set_ylabel('Y-axis')
        self.ax.set_title('Overlaid Data from Two .dat Files')
        self.ax.grid(True)
        self.ax.legend()
        plt.tight_layout()

    def setup_interaction(self):
        self.fig.canvas.mpl_connect('button_press_event', self.on_click)

    def on_click(self, event):
        if event.inaxes == self.ax:
            x, y = event.xdata, event.ydata
            label = f'({x:.2f}, {y:.2f})'
            annotation = self.ax.annotate(label, (x, y), xytext=(10, 10), 
                                          textcoords='offset points',
                                          bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.5),
                                          arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
            self.annotations.append(annotation)
            clear_output(wait=True)
            display(self.fig)

def select_files():
    root = tk.Tk()
    root.withdraw()
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        return InteractivePlot(filenames)
    else:
        print("Please select exactly two .dat files.")
        root.destroy()
        return None

# Use this in a Jupyter Notebook cell
plot = select_files()
if plot:
    display(plot.fig)
import tkinter as tk
from tkinter import filedialog
import matplotlib.pyplot as plt
import numpy as np
from IPython.display import display, clear_output
get_ipython().run_line_magic('matplotlib', 'inline')

class InteractivePlot:
    def __init__(self, filenames):
        self.filenames = filenames
        self.fig, self.ax = plt.subplots(figsize=(12, 6))
        self.colors = ['blue', 'red']
        self.annotations = []
        self.plot_data()
        self.setup_interaction()

    def plot_data(self):
        for i, filename in enumerate(self.filenames):
            data = np.loadtxt(filename)
            x, y = data[:, 0], data[:, 1]
            self.ax.plot(y, color=self.colors[i], label=f'Data from {filename}')
            
            if i == 0:
                self.ax.set_xlabel(f'X-axis for {filename}', color=self.colors[i])
                self.ax.tick_params(axis='x', colors=self.colors[i])
                self.ax.set_xlim(0, len(y) - 1)
                self.ax.set_xticks(np.linspace(0, len(y) - 1, 5))
                self.ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
            else:
                ax2 = self.ax.twiny()
                ax2.set_xlim(self.ax.get_xlim())
                ax2.set_xlabel(f'X-axis for {filename}', color=self.colors[i])
                ax2.tick_params(axis='x', colors=self.colors[i])
                ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
                ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

        self.ax.set_ylabel('Y-axis')
        self.ax.set_title('Overlaid Data from Two .dat Files')
        self.ax.grid(True)
        self.ax.legend()
        plt.tight_layout()

    def setup_interaction(self):
        self.fig.canvas.mpl_connect('button_press_event', self.on_click)

    def on_click(self, event):
        if event.inaxes == self.ax:
            x, y = event.xdata, event.ydata
            label = f'({x:.2f}, {y:.2f})'
            annotation = self.ax.annotate(label, (x, y), xytext=(10, 10), 
                                          textcoords='offset points',
                                          bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.5),
                                          arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
            self.annotations.append(annotation)
            clear_output(wait=True)
            display(self.fig)

def select_files():
    root = tk.Tk()
    root.withdraw()
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        return InteractivePlot(filenames)
    else:
        print("Please select exactly two .dat files.")
        root.destroy()
        return None

# Use this in a Jupyter Notebook cell
plot = select_files()
if plot:
    display(plot.fig)
import tkinter as tk
from tkinter import filedialog
import matplotlib.pyplot as plt
import numpy as np
from IPython.display import display, clear_output
get_ipython().run_line_magic('matplotlib', 'inline')

class InteractivePlot:
    def __init__(self, filenames):
        self.filenames = filenames
        self.fig, self.ax = plt.subplots(figsize=(12, 6))
        self.colors = ['blue', 'red']
        self.annotations = []
        self.plot_data()
        self.setup_interaction()

    def plot_data(self):
        for i, filename in enumerate(self.filenames):
            data = np.loadtxt(filename)
            x, y = data[:, 0], data[:, 1]
            self.ax.plot(y, color=self.colors[i], label=f'Data from {filename}')
            
            if i == 0:
                self.ax.set_xlabel(f'X-axis for {filename}', color=self.colors[i])
                self.ax.tick_params(axis='x', colors=self.colors[i])
                self.ax.set_xlim(0, len(y) - 1)
                self.ax.set_xticks(np.linspace(0, len(y) - 1, 5))
                self.ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
            else:
                ax2 = self.ax.twiny()
                ax2.set_xlim(self.ax.get_xlim())
                ax2.set_xlabel(f'X-axis for {filename}', color=self.colors[i])
                ax2.tick_params(axis='x', colors=self.colors[i])
                ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
                ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

        self.ax.set_ylabel('Y-axis')
        self.ax.set_title('Overlaid Data from Two .dat Files')
        self.ax.grid(True)
        self.ax.legend()
        plt.tight_layout()

    def setup_interaction(self):
        self.fig.canvas.mpl_connect('button_press_event', self.on_click)

    def on_click(self, event):
        if event.inaxes == self.ax:
            x, y = event.xdata, event.ydata
            label = f'({x:.2f}, {y:.2f})'
            annotation = self.ax.annotate(label, (x, y), xytext=(10, 10), 
                                          textcoords='offset points',
                                          bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.5),
                                          arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
            self.annotations.append(annotation)
            clear_output(wait=True)
            display(self.fig)

def select_files():
    root = tk.Tk()
    root.withdraw()
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        return InteractivePlot(filenames)
    else:
        print("Please select exactly two .dat files.")
        root.destroy()
        return None

# Use this in a Jupyter Notebook cell
plot = select_files()
if plot:
    display(plot.fig)
import tkinter as tk
from tkinter import filedialog
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk

class InteractivePlotter:
    def __init__(self, master):
        self.master = master
        self.master.title("Interactive Plotter")
        self.fig, self.ax = plt.subplots(figsize=(12, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.master)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.toolbar = NavigationToolbar2Tk(self.canvas, self.master)
        self.toolbar.update()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.annotation_color = tk.StringVar(value="black")
        self.create_ui()

        self.canvas.mpl_connect('button_press_event', self.on_click)

    def create_ui(self):
        frame = tk.Frame(self.master)
        frame.pack(side=tk.BOTTOM, fill=tk.X)

        tk.Label(frame, text="Annotation Color:").pack(side=tk.LEFT)
        color_menu = tk.OptionMenu(frame, self.annotation_color, "black", "red", "blue", "green", "orange")
        color_menu.pack(side=tk.LEFT)

        tk.Button(frame, text="Add Arrow", command=self.add_arrow_mode).pack(side=tk.LEFT)
        tk.Button(frame, text="Add Text", command=self.add_text_mode).pack(side=tk.LEFT)

    def add_arrow_mode(self):
        self.mode = "arrow"
        self.start_point = None

    def add_text_mode(self):
        self.mode = "text"

    def on_click(self, event):
        if event.inaxes != self.ax:
            return

        if self.mode == "arrow":
            if self.start_point is None:
                self.start_point = (event.xdata, event.ydata)
            else:
                end_point = (event.xdata, event.ydata)
                self.ax.annotate("", xy=end_point, xytext=self.start_point,
                                 arrowprops=dict(arrowstyle="->", color=self.annotation_color.get()))
                self.start_point = None
                self.canvas.draw()
        elif self.mode == "text":
            text = tk.simpledialog.askstring("Input", "Enter annotation text:")
            if text:
                self.ax.annotate(text, xy=(event.xdata, event.ydata), xytext=(event.xdata, event.ydata),
                                 color=self.annotation_color.get())
                self.canvas.draw()

    def plot_data(self, filenames):
        self.ax.clear()
        colors = ['blue', 'red']
        for i, filename in enumerate(filenames):
            data = np.loadtxt(filename)
            x, y = data[:, 0], data[:, 1]
            self.ax.plot(y, color=colors[i], label=f'Data from {filename}')
            
            if i == 0:
                self.ax.set_xlabel(f'X-axis for {filename}', color=colors[i])
                self.ax.tick_params(axis='x', colors=colors[i])
                self.ax.set_xlim(0, len(y) - 1)
                self.ax.set_xticks(np.linspace(0, len(y) - 1, 5))
                self.ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
            else:
                ax2 = self.ax.twiny()
                ax2.set_xlim(self.ax.get_xlim())
                ax2.set_xlabel(f'X-axis for {filename}', color=colors[i])
                ax2.tick_params(axis='x', colors=colors[i])
                ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
                ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

        self.ax.set_ylabel('Y-axis')
        self.ax.set_title('Overlaid Data from Two .dat Files')
        self.ax.grid(True)
        self.ax.legend()
        self.fig.tight_layout()
        self.canvas.draw()

def select_files():
    root = tk.Tk()
    root.withdraw()
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        main_window = tk.Tk()
        plotter = InteractivePlotter(main_window)
        plotter.plot_data(filenames)
        main_window.mainloop()
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
import tkinter as tk
from tkinter import filedialog, simpledialog
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk

class InteractivePlotter:
    def __init__(self, master):
        self.master = master
        self.master.title("Interactive Plotter")
        self.fig, self.ax = plt.subplots(figsize=(12, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.master)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.toolbar = NavigationToolbar2Tk(self.canvas, self.master)
        self.toolbar.update()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.annotation_color = tk.StringVar(value="black")
        self.mode = None
        self.start_point = None
        self.create_ui()

        self.canvas.mpl_connect('button_press_event', self.on_click)

    def create_ui(self):
        frame = tk.Frame(self.master)
        frame.pack(side=tk.BOTTOM, fill=tk.X)

        tk.Label(frame, text="Annotation Color:").pack(side=tk.LEFT)
        color_menu = tk.OptionMenu(frame, self.annotation_color, "black", "red", "blue", "green", "orange")
        color_menu.pack(side=tk.LEFT)

        tk.Button(frame, text="Add Arrow", command=self.add_arrow_mode).pack(side=tk.LEFT)
        tk.Button(frame, text="Add Text", command=self.add_text_mode).pack(side=tk.LEFT)

    def add_arrow_mode(self):
        self.mode = "arrow"
        self.start_point = None
        self.master.config(cursor="cross")

    def add_text_mode(self):
        self.mode = "text"
        self.master.config(cursor="tcross")

    def on_click(self, event):
        if event.inaxes != self.ax:
            return

        if self.mode == "arrow":
            if self.start_point is None:
                self.start_point = (event.xdata, event.ydata)
            else:
                end_point = (event.xdata, event.ydata)
                self.ax.annotate("", xy=end_point, xytext=self.start_point,
                                 arrowprops=dict(arrowstyle="->", color=self.annotation_color.get()))
                self.start_point = None
                self.canvas.draw()
                self.mode = None
                self.master.config(cursor="")
        elif self.mode == "text":
            text = simpledialog.askstring("Input", "Enter annotation text:")
            if text:
                self.ax.annotate(text, xy=(event.xdata, event.ydata), xytext=(event.xdata, event.ydata),
                                 color=self.annotation_color.get())
                self.canvas.draw()
            self.mode = None
            self.master.config(cursor="")

    def plot_data(self, filenames):
        self.ax.clear()
        colors = ['blue', 'red']
        for i, filename in enumerate(filenames):
            data = np.loadtxt(filename)
            x, y = data[:, 0], data[:, 1]
            self.ax.plot(y, color=colors[i], label=f'Data from {filename}')
            
            if i == 0:
                self.ax.set_xlabel(f'X-axis for {filename}', color=colors[i])
                self.ax.tick_params(axis='x', colors=colors[i])
                self.ax.set_xlim(0, len(y) - 1)
                self.ax.set_xticks(np.linspace(0, len(y) - 1, 5))
                self.ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
            else:
                ax2 = self.ax.twiny()
                ax2.set_xlim(self.ax.get_xlim())
                ax2.set_xlabel(f'X-axis for {filename}', color=colors[i])
                ax2.tick_params(axis='x', colors=colors[i])
                ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
                ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

        self.ax.set_ylabel('Y-axis')
        self.ax.set_title('Overlaid Data from Two .dat Files')
        self.ax.grid(True)
        self.ax.legend()
        self.fig.tight_layout()
        self.canvas.draw()

def select_files():
    root = tk.Tk()
    root.withdraw()
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        main_window = tk.Tk()
        plotter = InteractivePlotter(main_window)
        plotter.plot_data(filenames)
        main_window.mainloop()
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
import tkinter as tk
from tkinter import filedialog, simpledialog
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk

class InteractivePlotter:
    def __init__(self, master):
        self.master = master
        self.master.title("Interactive Plotter")
        self.fig, self.ax = plt.subplots(figsize=(12, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.master)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.toolbar = NavigationToolbar2Tk(self.canvas, self.master)
        self.toolbar.update()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.annotation_color = tk.StringVar(value="black")
        self.mode = None
        self.start_point = None
        self.create_ui()

        self.canvas.mpl_connect('button_press_event', self.on_click)

    def create_ui(self):
        frame = tk.Frame(self.master)
        frame.pack(side=tk.BOTTOM, fill=tk.X)

        tk.Label(frame, text="Annotation Color:").pack(side=tk.LEFT)
        color_menu = tk.OptionMenu(frame, self.annotation_color, "black", "red", "blue", "green", "orange")
        color_menu.pack(side=tk.LEFT)

        tk.Button(frame, text="Add Arrow", command=self.add_arrow_mode).pack(side=tk.LEFT)
        tk.Button(frame, text="Add Text", command=self.add_text_mode).pack(side=tk.LEFT)

    def add_arrow_mode(self):
        self.mode = "arrow"
        self.start_point = None
        self.master.config(cursor="cross")

    def add_text_mode(self):
        self.mode = "text"
        self.master.config(cursor="tcross")

    def on_click(self, event):
        if event.inaxes != self.ax:
            return

        if self.mode == "arrow":
            if self.start_point is None:
                self.start_point = (event.xdata, event.ydata)
            else:
                end_point = (event.xdata, event.ydata)
                self.ax.annotate("", xy=end_point, xytext=self.start_point,
                                 arrowprops=dict(arrowstyle="->", color=self.annotation_color.get()))
                self.start_point = None
                self.canvas.draw()
                self.mode = None
                self.master.config(cursor="")
        elif self.mode == "text":
            text = simpledialog.askstring("Input", "Enter annotation text:")
            if text:
                self.ax.annotate(text, xy=(event.xdata, event.ydata), xytext=(event.xdata, event.ydata),
                                 color=self.annotation_color.get())
                self.canvas.draw()
            self.mode = None
            self.master.config(cursor="")

    def plot_data(self, filenames):
        self.ax.clear()
        colors = ['blue', 'red']
        for i, filename in enumerate(filenames):
            data = np.loadtxt(filename)
            x, y = data[:, 0], data[:, 1]
            self.ax.plot(y, color=colors[i], label=f'Data from {filename}')
            
            if i == 0:
                self.ax.set_xlabel(f'X-axis for {filename}', color=colors[i])
                self.ax.tick_params(axis='x', colors=colors[i])
                self.ax.set_xlim(0, len(y) - 1)
                self.ax.set_xticks(np.linspace(0, len(y) - 1, 5))
                self.ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
            else:
                ax2 = self.ax.twiny()
                ax2.set_xlim(self.ax.get_xlim())
                ax2.set_xlabel(f'X-axis for {filename}', color=colors[i])
                ax2.tick_params(axis='x', colors=colors[i])
                ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
                ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

        self.ax.set_ylabel('Y-axis')
        self.ax.set_title('Overlaid Data from Two .dat Files')
        self.ax.grid(True)
        self.ax.legend()
        self.fig.tight_layout()
        self.canvas.draw()

def select_files():
    root = tk.Tk()
    root.withdraw()
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        main_window = tk.Tk()
        plotter = InteractivePlotter(main_window)
        plotter.plot_data(filenames)
        main_window.mainloop()
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
import tkinter as tk
from tkinter import filedialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import numpy as np

class InteractivePlotter:
    def __init__(self, master):
        self.master = master
        self.master.title("Interactive Plotter")
        self.fig, self.ax = plt.subplots(figsize=(12, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.master)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.toolbar = NavigationToolbar2Tk(self.canvas, self.master)
        self.toolbar.update()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.annotation_color = tk.StringVar(value="black")
        self.create_ui()

    def create_ui(self):
        frame = tk.Frame(self.master)
        frame.pack(side=tk.BOTTOM, fill=tk.X)

        tk.Label(frame, text="Annotation Color:").pack(side=tk.LEFT)
        color_menu = tk.OptionMenu(frame, self.annotation_color, "black", "red", "blue", "green", "orange")
        color_menu.pack(side=tk.LEFT)

        tk.Button(frame, text="Add Arrow", command=self.add_arrow).pack(side=tk.LEFT)
        tk.Button(frame, text="Add Text", command=self.add_text).pack(side=tk.LEFT)

    def add_arrow(self):
        arrow = self.ax.annotate("", xy=(0, 0), xytext=(0.2, 0.2), 
                                 arrowprops=dict(arrowstyle="->", color=self.annotation_color.get()),
                                 xycoords='data')
        self.ax.get_figure().canvas.draw()
        
        annot = ArrowDragCallback(arrow)
        self.fig.canvas.mpl_connect('button_press_event', annot.on_press)
        self.fig.canvas.mpl_connect('button_release_event', annot.on_release)
        self.fig.canvas.mpl_connect('motion_notify_event', annot.on_motion)

    def add_text(self):
        text = self.ax.text(0.5, 0.5, "Drag me", ha="center", va="center",
                            color=self.annotation_color.get())
        self.ax.get_figure().canvas.draw()
        
        annot = TextDragCallback(text)
        self.fig.canvas.mpl_connect('button_press_event', annot.on_press)
        self.fig.canvas.mpl_connect('button_release_event', annot.on_release)
        self.fig.canvas.mpl_connect('motion_notify_event', annot.on_motion)

    def plot_data(self, filenames):
        self.ax.clear()
        colors = ['blue', 'red']
        for i, filename in enumerate(filenames):
            data = np.loadtxt(filename)
            x, y = data[:, 0], data[:, 1]
            self.ax.plot(y, color=colors[i], label=f'Data from {filename}')
            
            if i == 0:
                self.ax.set_xlabel(f'X-axis for {filename}', color=colors[i])
                self.ax.tick_params(axis='x', colors=colors[i])
                self.ax.set_xlim(0, len(y) - 1)
                self.ax.set_xticks(np.linspace(0, len(y) - 1, 5))
                self.ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
            else:
                ax2 = self.ax.twiny()
                ax2.set_xlim(self.ax.get_xlim())
                ax2.set_xlabel(f'X-axis for {filename}', color=colors[i])
                ax2.tick_params(axis='x', colors=colors[i])
                ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
                ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

        self.ax.set_ylabel('Y-axis')
        self.ax.set_title('Overlaid Data from Two .dat Files')
        self.ax.grid(True)
        self.ax.legend()
        self.fig.tight_layout()
        self.canvas.draw()

class ArrowDragCallback:
    def __init__(self, arrow):
        self.arrow = arrow
        self.press = None

    def on_press(self, event):
        if event.inaxes != self.arrow.axes: return
        contains, _ = self.arrow.contains(event)
        if not contains: return
        self.press = self.arrow.get_xytext(), event.xdata, event.ydata

    def on_motion(self, event):
        if self.press is None: return
        if event.inaxes != self.arrow.axes: return
        xytext, xpress, ypress = self.press
        dx = event.xdata - xpress
        dy = event.ydata - ypress
        self.arrow.set_position((xytext[0]+dx, xytext[1]+dy))
        self.arrow.set_xy((event.xdata, event.ydata))
        self.arrow.figure.canvas.draw()

    def on_release(self, event):
        self.press = None
        self.arrow.figure.canvas.draw()

class TextDragCallback:
    def __init__(self, text):
        self.text = text
        self.press = None

    def on_press(self, event):
        if event.inaxes != self.text.axes: return
        contains, _ = self.text.contains(event)
        if not contains: return
        self.press = self.text.get_position(), event.xdata, event.ydata

    def on_motion(self, event):
        if self.press is None: return
        if event.inaxes != self.text.axes: return
        position, xpress, ypress = self.press
        dx = event.xdata - xpress
        dy = event.ydata - ypress
        self.text.set_position((position[0]+dx, position[1]+dy))
        self.text.figure.canvas.draw()

    def on_release(self, event):
        self.press = None
        self.text.figure.canvas.draw()

def select_files():
    root = tk.Tk()
    root.withdraw()
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        main_window = tk.Tk()
        plotter = InteractivePlotter(main_window)
        plotter.plot_data(filenames)
        main_window.mainloop()
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
import tkinter as tk
from tkinter import filedialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.widgets import Annotation
import numpy as np

class InteractivePlotter:
    def __init__(self, master):
        self.master = master
        self.master.title("Interactive Plotter")
        self.fig, self.ax = plt.subplots(figsize=(12, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.master)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.toolbar = NavigationToolbar2Tk(self.canvas, self.master)
        self.toolbar.update()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.annotation_color = tk.StringVar(value="black")
        self.create_ui()

        self.annotations = []

    def create_ui(self):
        frame = tk.Frame(self.master)
        frame.pack(side=tk.BOTTOM, fill=tk.X)

        tk.Label(frame, text="Annotation Color:").pack(side=tk.LEFT)
        color_menu = tk.OptionMenu(frame, self.annotation_color, "black", "red", "blue", "green", "orange")
        color_menu.pack(side=tk.LEFT)

        tk.Button(frame, text="Add Arrow", command=self.add_arrow).pack(side=tk.LEFT)
        tk.Button(frame, text="Add Text", command=self.add_text).pack(side=tk.LEFT)

    def add_arrow(self):
        annotation = Annotation("", xy=(0.5, 0.5), xytext=(0.6, 0.6),
                                xycoords='axes fraction',
                                arrowprops=dict(arrowstyle="->", color=self.annotation_color.get()),
                                annotation_clip=False)
        self.ax.add_artist(annotation)
        annotation.draggable()
        self.annotations.append(annotation)
        self.canvas.draw()

    def add_text(self):
        annotation = Annotation("Edit me", xy=(0.5, 0.5), xytext=(0.5, 0.5),
                                xycoords='axes fraction',
                                ha='center', va='center',
                                annotation_clip=False)
        self.ax.add_artist(annotation)
        annotation.draggable()
        self.annotations.append(annotation)
        self.canvas.draw()

        # Enable text editing on double click
        self.canvas.mpl_connect('button_press_event', lambda event: self.on_text_click(event, annotation))

    def on_text_click(self, event, annotation):
        if event.dblclick and annotation.contains(event)[0]:
            new_text = tk.simpledialog.askstring("Edit Text", "Enter new text:", initialvalue=annotation.get_text())
            if new_text:
                annotation.set_text(new_text)
                self.canvas.draw()

    def plot_data(self, filenames):
        self.ax.clear()
        colors = ['blue', 'red']
        for i, filename in enumerate(filenames):
            data = np.loadtxt(filename)
            x, y = data[:, 0], data[:, 1]
            self.ax.plot(y, color=colors[i], label=f'Data from {filename}')
            
            if i == 0:
                self.ax.set_xlabel(f'X-axis for {filename}', color=colors[i])
                self.ax.tick_params(axis='x', colors=colors[i])
                self.ax.set_xlim(0, len(y) - 1)
                self.ax.set_xticks(np.linspace(0, len(y) - 1, 5))
                self.ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
            else:
                ax2 = self.ax.twiny()
                ax2.set_xlim(self.ax.get_xlim())
                ax2.set_xlabel(f'X-axis for {filename}', color=colors[i])
                ax2.tick_params(axis='x', colors=colors[i])
                ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
                ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

        self.ax.set_ylabel('Y-axis')
        self.ax.set_title('Overlaid Data from Two .dat Files')
        self.ax.grid(True)
        self.ax.legend()
        self.fig.tight_layout()
        self.canvas.draw()

def select_files():
    root = tk.Tk()
    root.withdraw()
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        main_window = tk.Tk()
        plotter = InteractivePlotter(main_window)
        plotter.plot_data(filenames)
        main_window.mainloop()
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
import tkinter as tk
from tkinter import filedialog, simpledialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import numpy as np

class InteractivePlotter:
    def __init__(self, master):
        self.master = master
        self.master.title("Interactive Plotter")
        self.fig, self.ax = plt.subplots(figsize=(12, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.master)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.toolbar = NavigationToolbar2Tk(self.canvas, self.master)
        self.toolbar.update()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.annotation_color = tk.StringVar(value="black")
        self.create_ui()

        self.arrow_start = None
        self.text_position = None
        self.current_action = None

    def create_ui(self):
        frame = tk.Frame(self.master)
        frame.pack(side=tk.BOTTOM, fill=tk.X)

        tk.Label(frame, text="Annotation Color:").pack(side=tk.LEFT)
        color_menu = tk.OptionMenu(frame, self.annotation_color, "black", "red", "blue", "green", "orange")
        color_menu.pack(side=tk.LEFT)

        tk.Button(frame, text="Add Arrow", command=self.start_arrow).pack(side=tk.LEFT)
        tk.Button(frame, text="Add Text", command=self.start_text).pack(side=tk.LEFT)

    def start_arrow(self):
        self.current_action = "arrow"
        self.canvas.mpl_connect('button_press_event', self.on_click)

    def start_text(self):
        self.current_action = "text"
        self.canvas.mpl_connect('button_press_event', self.on_click)

    def on_click(self, event):
        if event.inaxes != self.ax:
            return

        if self.current_action == "arrow":
            if self.arrow_start is None:
                self.arrow_start = (event.xdata, event.ydata)
            else:
                self.add_arrow(self.arrow_start, (event.xdata, event.ydata))
                self.arrow_start = None
                self.current_action = None

        elif self.current_action == "text":
            self.text_position = (event.xdata, event.ydata)
            self.add_text()
            self.current_action = None

    def add_arrow(self, start, end):
        arrow = self.ax.annotate("", xy=end, xytext=start, 
                                 arrowprops=dict(arrowstyle="->", color=self.annotation_color.get()),
                                 xycoords='data')
        self.ax.get_figure().canvas.draw()

    def add_text(self):
        text = simpledialog.askstring("Input", "Enter text:")
        if text:
            self.ax.text(self.text_position[0], self.text_position[1], text,
                         ha="center", va="center", color=self.annotation_color.get())
            self.ax.get_figure().canvas.draw()

    def plot_data(self, filenames):
        self.ax.clear()
        colors = ['blue', 'red']
        for i, filename in enumerate(filenames):
            data = np.loadtxt(filename)
            x, y = data[:, 0], data[:, 1]
            self.ax.plot(y, color=colors[i], label=f'Data from {filename}')
            
            if i == 0:
                self.ax.set_xlabel(f'X-axis for {filename}', color=colors[i])
                self.ax.tick_params(axis='x', colors=colors[i])
                self.ax.set_xlim(0, len(y) - 1)
                self.ax.set_xticks(np.linspace(0, len(y) - 1, 5))
                self.ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
            else:
                ax2 = self.ax.twiny()
                ax2.set_xlim(self.ax.get_xlim())
                ax2.set_xlabel(f'X-axis for {filename}', color=colors[i])
                ax2.tick_params(axis='x', colors=colors[i])
                ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
                ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

        self.ax.set_ylabel('Y-axis')
        self.ax.set_title('Overlaid Data from Two .dat Files')
        self.ax.grid(True)
        self.ax.legend()
        self.fig.tight_layout()
        self.canvas.draw()

def select_files():
    root = tk.Tk()
    root.withdraw()
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        main_window = tk.Tk()
        plotter = InteractivePlotter(main_window)
        plotter.plot_data(filenames)
        main_window.mainloop()
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
import tkinter as tk
from tkinter import filedialog
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.widgets import Button

def plot_data(filenames):
    # Create a new figure
    fig, ax = plt.subplots(figsize=(12, 6))

    colors = ['blue', 'red']
    for i, filename in enumerate(filenames):
        # Load data from the file
        data = np.loadtxt(filename)
        
        # Extract x and y data (assuming first column is x and second is y)
        x = data[:, 0]
        y = data[:, 1]
        
        # Plot the data
        ax.plot(y, color=colors[i], label=f'Data from {filename}')
        
        # Set up the x-axis
        if i == 0:
            # Bottom x-axis
            ax.set_xlabel(f'X-axis for {filename}', color=colors[i])
            ax.tick_params(axis='x', colors=colors[i])
            ax.set_xlim(0, len(y) - 1)
            ax.set_xticks(np.linspace(0, len(y) - 1, 5))
            ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
        else:
            # Top x-axis
            ax2 = ax.twiny()
            ax2.set_xlim(ax.get_xlim())
            ax2.set_xlabel(f'X-axis for {filename}', color=colors[i])
            ax2.tick_params(axis='x', colors=colors[i])
            ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
            ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

    # Customize the plot
    ax.set_ylabel('Y-axis')
    ax.set_title('Overlaid Data from Two .dat Files')
    ax.grid(True)
    ax.legend()

    # Adjust layout
    plt.tight_layout()

    # Add annotation functionality
    annotation_mode = [False]
    annotations = []

    def toggle_annotation(event):
        annotation_mode[0] = not annotation_mode[0]
        if annotation_mode[0]:
            annotation_button.label.set_text('Finish Annotation')
            plt.title('Click to add arrow, right-click to add text')
        else:
            annotation_button.label.set_text('Start Annotation')
            plt.title('Overlaid Data from Two .dat Files')
        plt.draw()

    def on_click(event):
        if annotation_mode[0] and event.inaxes == ax:
            if event.button == 1:  # Left click
                arrow = ax.annotate('', xy=(event.xdata, event.ydata), xytext=(event.xdata-0.5, event.ydata-0.5),
                                    arrowprops=dict(arrowstyle='->'))
                annotations.append(arrow)
            elif event.button == 3:  # Right click
                text = ax.text(event.xdata, event.ydata, 'Text', fontsize=10)
                annotations.append(text)
            plt.draw()

    # Add a button to toggle annotation mode
    ax_button = plt.axes([0.81, 0.05, 0.1, 0.075])
    annotation_button = Button(ax_button, 'Start Annotation')
    annotation_button.on_clicked(toggle_annotation)

    # Connect the click event
    fig.canvas.mpl_connect('button_press_event', on_click)

    plt.show()

def select_files():
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        plot_data(filenames)
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
import tkinter as tk
from tkinter import filedialog
import matplotlib.pyplot as plt
import numpy as np

def plot_data(filenames):
    # Create a new figure
    fig, ax = plt.subplots(figsize=(12, 6))

    colors = ['blue', 'red']
    for i, filename in enumerate(filenames):
        # Load data from the file
        data = np.loadtxt(filename)
        
        # Extract x and y data (assuming first column is x and second is y)
        x = data[:, 0]
        y = data[:, 1]
        
        # Plot the data
        ax.plot(y, color=colors[i], label=f'Data from {filename}')
        
        # Set up the x-axis
        if i == 0:
            # Bottom x-axis
            ax.set_xlabel(f'X-axis for {filename}', color=colors[i])
            ax.tick_params(axis='x', colors=colors[i])
            ax.set_xlim(0, len(y) - 1)
            ax.set_xticks(np.linspace(0, len(y) - 1, 5))
            ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
        else:
            # Top x-axis
            ax2 = ax.twiny()
            ax2.set_xlim(ax.get_xlim())
            ax2.set_xlabel(f'X-axis for {filename}', color=colors[i])
            ax2.tick_params(axis='x', colors=colors[i])
            ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
            ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

    # Customize the plot
    ax.set_ylabel('Y-axis')
    ax.set_title('Overlaid Data from Two .dat Files')
    ax.grid(True)
    ax.legend()

    # Add annotations
    ax.annotate('Peak', xy=(50, y[50]), xytext=(70, y[50]+0.1),
                arrowprops=dict(facecolor='black', shrink=0.05))
    
    ax.text(100, y[100]+0.05, 'Interesting region', fontsize=10, ha='center')

    # Adjust layout and show the plot
    plt.tight_layout()
    plt.show()

def select_files():
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        plot_data(filenames)
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
import tkinter as tk
from tkinter import filedialog, simpledialog, colorchooser
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

def plot_data(filenames):
    # Create a new figure and Tkinter window
    root = tk.Tk()
    root.title("Interactive Plot")
    fig, ax = plt.subplots(figsize=(12, 6))

    colors = ['blue', 'red']
    for i, filename in enumerate(filenames):
        # Load data from the file
        data = np.loadtxt(filename)
        
        # Extract x and y data (assuming first column is x and second is y)
        x = data[:, 0]
        y = data[:, 1]
        
        # Plot the data
        ax.plot(y, color=colors[i], label=f'Data from {filename}')
        
        # Set up the x-axis
        if i == 0:
            # Bottom x-axis
            ax.set_xlabel(f'X-axis for {filename}', color=colors[i])
            ax.tick_params(axis='x', colors=colors[i])
            ax.set_xlim(0, len(y) - 1)
            ax.set_xticks(np.linspace(0, len(y) - 1, 5))
            ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
        else:
            # Top x-axis
            ax2 = ax.twiny()
            ax2.set_xlim(ax.get_xlim())
            ax2.set_xlabel(f'X-axis for {filename}', color=colors[i])
            ax2.tick_params(axis='x', colors=colors[i])
            ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
            ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

    # Customize the plot
    ax.set_ylabel('Y-axis')
    ax.set_title('Overlaid Data from Two .dat Files')
    ax.grid(True)
    ax.legend()

    # Embed the plot in Tkinter window
    canvas = FigureCanvasTkAgg(fig, master=root)
    canvas.draw()
    canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

    # Variables for annotation
    arrow_start = None
    annotations = []

    def on_click(event):
        nonlocal arrow_start
        if event.button == 1 and event.dblclick:  # Double left click
            if arrow_start is None:
                arrow_start = (event.xdata, event.ydata)
            else:
                end = (event.xdata, event.ydata)
                color = colorchooser.askcolor(title="Choose arrow color")[1]
                if color:
                    arrow = ax.annotate('', xy=end, xytext=arrow_start,
                                        arrowprops=dict(arrowstyle='->', color=color))
                    annotations.append(arrow)
                    canvas.draw()
                arrow_start = None
        elif event.button == 3:  # Right click
            text = simpledialog.askstring("Input", "Enter annotation text:")
            if text:
                txt = ax.text(event.xdata, event.ydata, text, fontsize=10, ha='center')
                annotations.append(txt)
                canvas.draw()

    # Connect the click event
    canvas.mpl_connect('button_press_event', on_click)

    def clear_annotations():
        for ann in annotations:
            ann.remove()
        annotations.clear()
        canvas.draw()

    clear_button = tk.Button(root, text="Clear Annotations", command=clear_annotations)
    clear_button.pack()

    plt.tight_layout()
    tk.mainloop()

def select_files():
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        plot_data(filenames)
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
import tkinter as tk
from tkinter import filedialog, simpledialog, colorchooser
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

def plot_data(filenames):
    # Create a new figure and Tkinter window
    root = tk.Tk()
    root.title("Interactive Plot")
    fig, ax = plt.subplots(figsize=(12, 6))

    colors = ['blue', 'red']
    for i, filename in enumerate(filenames):
        # Load data from the file
        data = np.loadtxt(filename)
        
        # Extract x and y data (assuming first column is x and second is y)
        x = data[:, 0]
        y = data[:, 1]
        
        # Plot the data
        ax.plot(y, color=colors[i], label=f'Data from {filename}')
        
        # Set up the x-axis
        if i == 0:
            # Bottom x-axis
            ax.set_xlabel(f'X-axis for {filename}', color=colors[i])
            ax.tick_params(axis='x', colors=colors[i])
            ax.set_xlim(0, len(y) - 1)
            ax.set_xticks(np.linspace(0, len(y) - 1, 5))
            ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
        else:
            # Top x-axis
            ax2 = ax.twiny()
            ax2.set_xlim(ax.get_xlim())
            ax2.set_xlabel(f'X-axis for {filename}', color=colors[i])
            ax2.tick_params(axis='x', colors=colors[i])
            ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
            ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

    # Customize the plot
    ax.set_ylabel('Y-axis')
    ax.set_title('Overlaid Data from Two .dat Files')
    ax.grid(True)
    ax.legend()

    # Embed the plot in Tkinter window
    canvas = FigureCanvasTkAgg(fig, master=root)
    canvas.draw()
    canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

    # Variables for annotation
    arrow_start = None
    annotations = []

    def on_click(event):
        nonlocal arrow_start
        if event.button == 1 and event.dblclick:  # Double left click
            if arrow_start is None:
                arrow_start = (event.xdata, event.ydata)
            else:
                end = (event.xdata, event.ydata)
                color = colorchooser.askcolor(title="Choose arrow color")[1]
                if color:
                    arrow = ax.annotate('', xy=end, xytext=arrow_start,
                                        arrowprops=dict(arrowstyle='->', color=color))
                    annotations.append(arrow)
                    canvas.draw()
                arrow_start = None
        elif event.button == 3:  # Right click
            text = simpledialog.askstring("Input", "Enter annotation text:")
            if text:
                txt = ax.text(event.xdata, event.ydata, text, fontsize=10, ha='center')
                annotations.append(txt)
                canvas.draw()

    # Connect the click event
    canvas.mpl_connect('button_press_event', on_click)

    def clear_annotations():
        for ann in annotations:
            ann.remove()
        annotations.clear()
        canvas.draw()

    clear_button = tk.Button(root, text="Clear Annotations", command=clear_annotations)
    clear_button.pack()

    plt.tight_layout()
    tk.mainloop()

def select_files():
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        plot_data(filenames)
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
import tkinter as tk
from tkinter import filedialog, simpledialog, colorchooser
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

def plot_data(filenames):
    # Create a new figure and Tkinter window
    root = tk.Tk()
    root.title("Interactive Plot")
    fig, ax = plt.subplots(figsize=(12, 6))

    colors = ['blue', 'red']
    all_y_data = []
    for i, filename in enumerate(filenames):
        # Load data from the file
        data = np.loadtxt(filename)
        
        # Extract x and y data (assuming first column is x and second is y)
        x = data[:, 0]
        y = data[:, 1]
        all_y_data.append(y)
        
        # Plot the data
        ax.plot(range(len(y)), y, color=colors[i], label=f'Data from {filename}')
        
        # Set up the x-axis
        if i == 0:
            # Bottom x-axis
            ax.set_xlabel(f'X-axis for {filename}', color=colors[i])
            ax.tick_params(axis='x', colors=colors[i])
            ax.set_xlim(0, len(y) - 1)
            ax.set_xticks(np.linspace(0, len(y) - 1, 5))
            ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
        else:
            # Top x-axis
            ax2 = ax.twiny()
            ax2.set_xlim(ax.get_xlim())
            ax2.set_xlabel(f'X-axis for {filename}', color=colors[i])
            ax2.tick_params(axis='x', colors=colors[i])
            ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
            ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

    # Customize the plot
    ax.set_ylabel('Y-axis')
    ax.set_title('Overlaid Data from Two .dat Files')
    ax.grid(True)
    ax.legend()

    # Embed the plot in Tkinter window
    canvas = FigureCanvasTkAgg(fig, master=root)
    canvas.draw()
    canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

    # Variables for annotation
    arrow_start = None
    annotations = []

    def on_click(event):
        nonlocal arrow_start
        if event.inaxes != ax:
            return
        
        x_index = int(round(event.xdata))
        if x_index < 0 or x_index >= len(all_y_data[0]):
            return

        y_value = max(dataset[x_index] for dataset in all_y_data)
        
        if event.button == 1 and event.dblclick:  # Double left click
            if arrow_start is None:
                arrow_start = (x_index, y_value)
            else:
                end = (x_index, y_value)
                color = colorchooser.askcolor(title="Choose arrow color")[1]
                if color:
                    arrow = ax.annotate('', xy=end, xytext=arrow_start,
                                        arrowprops=dict(arrowstyle='->', color=color))
                    annotations.append(arrow)
                    canvas.draw()
                arrow_start = None
        elif event.button == 3:  # Right click
            text = simpledialog.askstring("Input", "Enter annotation text:")
            if text:
                txt = ax.text(x_index, y_value, text, fontsize=10, ha='center', va='bottom')
                annotations.append(txt)
                canvas.draw()

    # Connect the click event
    canvas.mpl_connect('button_press_event', on_click)

    def clear_annotations():
        for ann in annotations:
            ann.remove()
        annotations.clear()
        canvas.draw()

    clear_button = tk.Button(root, text="Clear Annotations", command=clear_annotations)
    clear_button.pack()

    plt.tight_layout()
    tk.mainloop()

def select_files():
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        plot_data(filenames)
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
import tkinter as tk
from tkinter import filedialog, simpledialog, colorchooser
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

def plot_data(filenames):
    # Create a new figure and Tkinter window
    root = tk.Tk()
    root.title("Interactive Plot")
    fig, ax = plt.subplots(figsize=(12, 6))

    colors = ['blue', 'red']
    all_y_data = []
    for i, filename in enumerate(filenames):
        # Load data from the file
        data = np.loadtxt(filename)
        
        # Extract x and y data (assuming first column is x and second is y)
        x = data[:, 0]
        y = data[:, 1]
        all_y_data.append(y)
        
        # Plot the data
        ax.plot(range(len(y)), y, color=colors[i], label=f'Data from {filename}')
        
        # Set up the x-axis
        if i == 0:
            # Bottom x-axis
            ax.set_xlabel(f'X-axis for {filename}', color=colors[i])
            ax.tick_params(axis='x', colors=colors[i])
            ax.set_xlim(0, len(y) - 1)
            ax.set_xticks(np.linspace(0, len(y) - 1, 5))
            ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
        else:
            # Top x-axis
            ax2 = ax.twiny()
            ax2.set_xlim(ax.get_xlim())
            ax2.set_xlabel(f'X-axis for {filename}', color=colors[i])
            ax2.tick_params(axis='x', colors=colors[i])
            ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
            ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

    # Customize the plot
    ax.set_ylabel('Y-axis')
    ax.set_title('Overlaid Data from Two .dat Files')
    ax.grid(True)
    ax.legend()

    # Embed the plot in Tkinter window
    canvas = FigureCanvasTkAgg(fig, master=root)
    canvas.draw()
    canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

    # Variables for annotation
    arrow_start = None
    annotations = []

    def on_click(event):
        nonlocal arrow_start
        if event.inaxes != ax:
            return
        
        x_index = int(round(event.xdata))
        if x_index < 0 or x_index >= len(all_y_data[0]):
            return

        y_value = max(dataset[x_index] for dataset in all_y_data)
        
        if event.button == 1:  # Left click
            if arrow_start is None:
                arrow_start = (x_index, y_value)
                print("Arrow start set:", arrow_start)
            else:
                end = (x_index, y_value)
                print("Arrow end set:", end)
                color = colorchooser.askcolor(title="Choose arrow color")[1]
                if color:
                    arrow = ax.annotate('', xy=end, xytext=arrow_start,
                                        arrowprops=dict(arrowstyle='->', color=color))
                    annotations.append(arrow)
                    canvas.draw()
                arrow_start = None
        elif event.button == 3:  # Right click
            text = simpledialog.askstring("Input", "Enter annotation text:")
            if text:
                txt = ax.text(x_index, y_value, text, fontsize=10, ha='center', va='bottom')
                annotations.append(txt)
                canvas.draw()

    # Connect the click event
    canvas.mpl_connect('button_press_event', on_click)

    def clear_annotations():
        for ann in annotations:
            ann.remove()
        annotations.clear()
        canvas.draw()

    clear_button = tk.Button(root, text="Clear Annotations", command=clear_annotations)
    clear_button.pack()

    plt.tight_layout()
    tk.mainloop()

def select_files():
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        plot_data(filenames)
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
import tkinter as tk
from tkinter import filedialog, simpledialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import numpy as np

class PlottingGUI:
    def __init__(self, master):
        self.master = master
        self.master.title("Interactive Plotting GUI")
        self.fig, self.ax = plt.subplots(figsize=(10, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.master)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.toolbar = NavigationToolbar2Tk(self.canvas, self.master)
        self.toolbar.update()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.button_frame = tk.Frame(self.master)
        self.button_frame.pack(side=tk.BOTTOM)

        self.add_arrow_button = tk.Button(self.button_frame, text="Add Arrow", command=self.add_arrow_mode)
        self.add_arrow_button.pack(side=tk.LEFT)

        self.add_text_button = tk.Button(self.button_frame, text="Add Text", command=self.add_text_mode)
        self.add_text_button.pack(side=tk.LEFT)

        self.mode = None
        self.start_point = None

    def plot_data(self, filenames):
        colors = ['blue', 'red']
        for i, filename in enumerate(filenames):
            data = np.loadtxt(filename)
            x, y = data[:, 0], data[:, 1]
            self.ax.plot(y, color=colors[i], label=f'Data from {filename}')
            
            if i == 0:
                self.ax.set_xlabel(f'X-axis for {filename}', color=colors[i])
                self.ax.tick_params(axis='x', colors=colors[i])
                self.ax.set_xlim(0, len(y) - 1)
                self.ax.set_xticks(np.linspace(0, len(y) - 1, 5))
                self.ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
            else:
                ax2 = self.ax.twiny()
                ax2.set_xlim(self.ax.get_xlim())
                ax2.set_xlabel(f'X-axis for {filename}', color=colors[i])
                ax2.tick_params(axis='x', colors=colors[i])
                ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
                ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

        self.ax.set_ylabel('Y-axis')
        self.ax.set_title('Overlaid Data from Two .dat Files')
        self.ax.grid(True)
        self.ax.legend()
        self.canvas.draw()

    def add_arrow_mode(self):
        self.mode = 'arrow'
        self.canvas.mpl_connect('button_press_event', self.on_click)

    def add_text_mode(self):
        self.mode = 'text'
        self.canvas.mpl_connect('button_press_event', self.on_click)

    def on_click(self, event):
        if event.inaxes != self.ax:
            return

        if self.mode == 'arrow':
            if self.start_point is None:
                self.start_point = (event.xdata, event.ydata)
            else:
                end_point = (event.xdata, event.ydata)
                self.ax.annotate('', xy=end_point, xytext=self.start_point,
                                 arrowprops=dict(arrowstyle='->'))
                self.start_point = None
                self.canvas.draw()
        elif self.mode == 'text':
            text = simpledialog.askstring("Input", "Enter text:")
            if text:
                self.ax.text(event.xdata, event.ydata, text)
                self.canvas.draw()

def select_files():
    root = tk.Tk()
    root.withdraw()
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        main_window = tk.Tk()
        app = PlottingGUI(main_window)
        app.plot_data(filenames)
        main_window.mainloop()
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
import tkinter as tk
from tkinter import filedialog, simpledialog
import matplotlib.pyplot as plt
import numpy as np

def plot_data(filenames, annotations):
    # Create a new figure
    fig, ax = plt.subplots(figsize=(12, 6))

    colors = ['blue', 'red']
    for i, filename in enumerate(filenames):
        # Load data from the file
        data = np.loadtxt(filename)
        
        # Extract x and y data (assuming first column is x and second is y)
        x = data[:, 0]
        y = data[:, 1]
        
        # Plot the data
        ax.plot(y, color=colors[i], label=f'Data from {filename}')
        
        # Set up the x-axis
        if i == 0:
            # Bottom x-axis
            ax.set_xlabel(f'X-axis for {filename}', color=colors[i])
            ax.tick_params(axis='x', colors=colors[i])
            ax.set_xlim(0, len(y) - 1)
            ax.set_xticks(np.linspace(0, len(y) - 1, 5))
            ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
        else:
            # Top x-axis
            ax2 = ax.twiny()
            ax2.set_xlim(ax.get_xlim())
            ax2.set_xlabel(f'X-axis for {filename}', color=colors[i])
            ax2.tick_params(axis='x', colors=colors[i])
            ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
            ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

    # Add annotations
    for point, label in annotations:
        ax.annotate(label, xy=(point, y[point]), xytext=(point, max(y) + 0.1),
                    arrowprops=dict(facecolor='black', shrink=0.05),
                    horizontalalignment='center', verticalalignment='bottom')

    # Customize the plot
    ax.set_ylabel('Y-axis')
    ax.set_title('Overlaid Data from Two .dat Files')
    ax.grid(True)
    ax.legend()

    # Adjust layout and show the plot
    plt.tight_layout()
    plt.show()

def select_files():
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        annotations = []
        for i in range(3):
            point = simpledialog.askinteger("Input", f"Enter x-coordinate for annotation {i+1}:")
            label = simpledialog.askstring("Input", f"Enter label for annotation {i+1}:")
            if point is not None and label:
                annotations.append((point, label))
        root.destroy()
        plot_data(filenames, annotations)
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import GaussianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
from ipywidgets import ToggleButtons

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max):
        plot_data = all_plots[scan_index - 1]
        ax.clear()

        # Get the full data
        data_to_plot = plot_data['data_values'].copy()
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            # Ensure the averaging kernel is 2D and has odd dimensions
            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)

            # Define the range for averaging
            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]
            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]

            # Apply averaging only to the specified range
            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]
            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')
            
            # Replace the averaged part in the original data
            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data

        k_indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)
        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]

            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            if not use_canny:
                kdc = kdc / np.max(kdc)

            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')
                ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Filter data for fitting
                fit_e_indices = np.where((energy_values >= fit_e_min) & (energy_values <= fit_e_max))[0]
                fit_energy_values = energy_values[fit_e_indices]
                fit_kdc = kdc[fit_e_indices]

                if fit_type == 'Maxima':
                    peaks, _ = find_peaks(fit_kdc)
                    peak_heights = fit_kdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                    largest_peaks = peaks[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(fit_kdc))
                    for j, peak in enumerate(largest_peaks):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_energy_values[peak], min=fit_energy_values.min(), max=fit_energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)
                elif fit_type == 'Minima':
                    valleys, _ = find_peaks(-fit_kdc)
                    valley_depths = np.max(fit_kdc) - fit_kdc[valleys]
                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]
                    largest_valleys = valleys[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(fit_kdc))
                    for j, valley in enumerate(largest_valleys):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_energy_values[valley], min=fit_energy_values.min(), max=fit_energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(fit_kdc, params, x=fit_energy_values)
                fit = result.best_fit
                
                # Interpolate the fit back to the full energy range
                full_fit = np.interp(energy_values, fit_energy_values, fit)
                offset_fit = full_fit + i * vertical_offset

                # Extract sigma values and their uncertainties for label
                sigmas = []
                sigma_errors = []
                for j in range(num_peaks):
                    sigma = abs(result.params[f'g{j+1}_sigma'].value)
                    sigma_error = result.params[f'g{j+1}_sigma'].stderr
                    sigmas.append(sigma)
                    sigma_errors.append(sigma_error)
                
                sigma_label = ', '.join([fr'$\sigma_{j+1}$={sigma:.3f} $\pm$ {error:.3f}' for j, (sigma, error) in enumerate(zip(sigmas, sigma_errors))])
                ax.plot(energy_values, offset_fit, '--', label=fr'Fit $k_\parallel$={actual_k:.2f}, {sigma_label}', color=f'C{i}')

                if fit_type == 'Maxima':
                    fit_peaks, _ = find_peaks(full_fit)
                    ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                    for j, peak in enumerate(fit_peaks):
                        peak_energy = energy_values[peak]
                        peak_intensity = offset_fit[peak]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_energy = peak_energy - sigma
                        right_sigma_energy = peak_energy + sigma
                        left_sigma_intensity = np.interp(left_sigma_energy, energy_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_energy, energy_values, offset_fit)
                        ax.plot(left_sigma_energy, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_energy, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_energy, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_energy, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')
                elif fit_type == 'Minima':
                    fit_valleys, _ = find_peaks(-full_fit)
                    ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)
                    for j, valley in enumerate(fit_valleys):
                        valley_energy = energy_values[valley]
                        valley_intensity = offset_fit[valley]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_energy = valley_energy - sigma
                        right_sigma_energy = valley_energy + sigma
                        left_sigma_intensity = np.interp(left_sigma_energy, energy_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_energy, energy_values, offset_fit)
                        ax.plot(left_sigma_energy, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_energy, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_energy, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_energy, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')
        ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)
        plt.tight_layout()
        fig.canvas.draw_idle()
        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'fit_type': interactive_plot.children[16].value,
            'fit_e_min': interactive_plot.children[17].value,
            'fit_e_max': interactive_plot.children[18].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def export_data(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'fit_type': interactive_plot.children[16].value,
            'fit_e_min': interactive_plot.children[17].value,
            'fit_e_max': interactive_plot.children[18].value
        }

        plot_data = all_plots[current_values['scan_index'] - 1]
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']
        data_values = plot_data['data_values']

        # Convert xarray DataArrays to numpy arrays if necessary
        if isinstance(k_parallel, xr.DataArray):
            k_parallel = k_parallel.values
        if isinstance(energy_values, xr.DataArray):
            energy_values = energy_values.values
        if isinstance(data_values, xr.DataArray):
            data_values = data_values.values

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]
        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]
        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if current_values['use_canny']:
            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])
            data_to_plot = edges.astype(float)

        if current_values['enable_averaging']:
            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')

        k_indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)

        # Get the current axes
        ax = plt.gca()

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            if isinstance(actual_k, np.ndarray):
                actual_k = actual_k.item()
            kdc = data_to_plot[:, k_index]

            # Apply normalization
            if not current_values['use_canny']:
                kdc = kdc / np.max(kdc)

            # Export original/processed data
            np.savetxt(f"data_k_{actual_k:.2f}.dat", np.column_stack((energy_values, kdc)), header="Energy (eV)\tIntensity (arb. units)")

            if current_values['show_fit']:
                # Find the fit line in the plot
                fit_line = None
                for line in ax.lines:
                    if line.get_label().startswith(f"Fit $k_\parallel$={actual_k:.2f}"):
                        fit_line = line
                        break

                if fit_line is not None:
                    # Get the x and y data of the fit line
                    fit_x_data, fit_y_data = fit_line.get_data()
                    
                    # Remove the vertical offset
                    fit_y_data -= i * current_values['vertical_offset']
                    
                    # Export the fit data
                    np.savetxt(f"gaussian_fit_k_{actual_k:.2f}.dat", np.column_stack((fit_x_data, fit_y_data)), header="Energy (eV)\tFitted Intensity (arb. units)")
                else:
                    print(f"No fit found for k = {actual_k:.2f}")

        print("Data export completed.")


    interactive_plot = interactive(
        plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),
        use_canny=Checkbox(value=False, description='Use Canny Filter'),
        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),
        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),
        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),
        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min (Å⁻¹)', continuous_update=True),
        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max (Å⁻¹)', continuous_update=True),
        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),
        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),
        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)
    export_button = Button(description="Export Data")
    export_button.on_click(export_data)

    output = VBox([interactive_plot, HBox([save_button, export_button])])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import tkinter as tk
from tkinter import filedialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import numpy as np

class InteractivePlot:
    def __init__(self, master, filenames):
        self.master = master
        self.filenames = filenames
        self.fig, self.ax = plt.subplots(figsize=(12, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, master=self.master)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.toolbar = NavigationToolbar2Tk(self.canvas, self.master)
        self.toolbar.update()
        self.canvas.get_tk_widget().pack(side=tk.TOP, fill=tk.BOTH, expand=1)

        self.annotations = []
        self.dragging = None
        self.offset = (0, 0)

        self.canvas.mpl_connect('button_press_event', self.on_press)
        self.canvas.mpl_connect('button_release_event', self.on_release)
        self.canvas.mpl_connect('motion_notify_event', self.on_motion)

        self.plot_data()

    def plot_data(self):
        colors = ['blue', 'red']
        for i, filename in enumerate(self.filenames):
            data = np.loadtxt(filename)
            x, y = data[:, 0], data[:, 1]
            self.ax.plot(y, color=colors[i], label=f'Data from {filename}')

            if i == 0:
                self.ax.set_xlabel(f'X-axis for {filename}', color=colors[i])
                self.ax.tick_params(axis='x', colors=colors[i])
                self.ax.set_xlim(0, len(y) - 1)
                self.ax.set_xticks(np.linspace(0, len(y) - 1, 5))
                self.ax.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])
            else:
                ax2 = self.ax.twiny()
                ax2.set_xlim(self.ax.get_xlim())
                ax2.set_xlabel(f'X-axis for {filename}', color=colors[i])
                ax2.tick_params(axis='x', colors=colors[i])
                ax2.set_xticks(np.linspace(0, len(y) - 1, 5))
                ax2.set_xticklabels([f'{x[0]:.2f}', f'{x[len(x)//4]:.2f}', f'{x[len(x)//2]:.2f}', f'{x[3*len(x)//4]:.2f}', f'{x[-1]:.2f}'])

        self.ax.set_ylabel('Y-axis')
        self.ax.set_title('Overlaid Data from Two .dat Files')
        self.ax.grid(True)
        self.ax.legend()
        self.fig.tight_layout()

    def on_press(self, event):
        if event.inaxes != self.ax:
            return
        for ann in self.annotations:
            if ann.contains(event)[0]:
                self.dragging = ann
                self.offset = (ann.xy[0] - event.xdata, ann.xy[1] - event.ydata)
                return
        self.add_annotation(event.xdata, event.ydata)

    def on_release(self, event):
        self.dragging = None

    def on_motion(self, event):
        if self.dragging is None or event.inaxes != self.ax:
            return
        self.dragging.xy = (event.xdata + self.offset[0], event.ydata + self.offset[1])
        self.canvas.draw()

    def add_annotation(self, x, y):
        ann = self.ax.annotate(f'({x:.2f}, {y:.2f})', xy=(x, y), xytext=(10, 10),
                               textcoords='offset points', ha='left', va='bottom',
                               bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.5),
                               arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
        self.annotations.append(ann)
        self.canvas.draw()

def select_files():
    root = tk.Tk()
    root.withdraw()
    filenames = filedialog.askopenfilenames(filetypes=[("DAT files", "*.dat")])
    if filenames and len(filenames) == 2:
        root.destroy()
        main_window = tk.Tk()
        main_window.title("Interactive Plot")
        InteractivePlot(main_window, filenames)
        main_window.mainloop()
    else:
        print("Please select exactly two .dat files.")
        root.destroy()

if __name__ == "__main__":
    select_files()
