# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
import warnings

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

#warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    fig, ax = plt.subplots(figsize=(10, 8))
    plt.close(fig)  # Prevent the empty figure from displaying

    out = Output()

    @out.capture(clear_output=True)
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        # Clear the previous plot
        ax.clear()
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        fig.canvas.draw_idle()
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    
    return interactive_plot, out

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot, out = kspace(data_files, work_function)
display(interactive_plot, out)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap
import warnings
from matplotlib.cbook import MatplotlibDeprecationWarning

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress the specific warning
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def kspace(data_files, work_function):
    # ... rest of your code ...
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        plt.show()

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def kspace(data_files, work_function):
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        hbar = 6.582119569e-16  # eV*s
        m_e = 9.1093837015e-31  # kg
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 1024))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    min_energy = float('inf')
    max_energy = float('-inf')
    min_k = float('inf')
    max_k = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))
        min_energy = min(min_energy, np.min(energy_values))
        max_energy = max(max_energy, np.max(energy_values))
        min_k = min(min_k, np.min(k_parallel))
        max_k = max(max_k, np.max(k_parallel))

    output = Output()

    @output.capture(clear_output=True)
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}')
        ax.set_ylim(min_energy, max_energy)  # Set consistent y-axis limits
        ax.set_xlim(min_k, max_k)  # Set consistent x-axis limits
        plt.show()

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))

    # Display the output and interactive plot
    display(output, interactive_plot)

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

kspace(data_files, work_function)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    min_energy = float('inf')
    max_energy = float('-inf')
    min_k = float('inf')
    max_k = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))
        min_energy = min(min_energy, np.min(energy_values))
        max_energy = max(max_energy, np.max(energy_values))
        min_k = min(min_k, np.min(k_parallel))
        max_k = max(max_k, np.max(k_parallel))

    # Create a single figure and axis
    fig, ax = plt.subplots(figsize=(10, 8))
    im = ax.pcolormesh([], [], [])
    cbar = fig.colorbar(im, ax=ax, label='Intensity')
    
    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        # Update the plot
        im.set_array(plot_data['data_values'].ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        im.set_extent([min_k, max_k, min_energy, max_energy])
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}')
        ax.set_ylim(min_energy, max_energy)  # Set consistent y-axis limits
        ax.set_xlim(min_k, max_k)  # Set consistent x-axis limits
        
        # Update colorbar
        cbar.update_normal(im)
        
        fig.canvas.draw_idle()

    # Create the interactive widget
    interactive_plot = interactive(update_plot,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))

    # Initial plot
    update_plot(1, 0, int(max_data_value), 1.0, 0.0, 1.0)

    # Display the figure and interactive plot
    display(fig, interactive_plot)

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

kspace(data_files, work_function)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    min_energy = float('inf')
    max_energy = float('-inf')
    min_k = float('inf')
    max_k = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))
        min_energy = min(min_energy, np.min(energy_values))
        max_energy = max(max_energy, np.max(energy_values))
        min_k = min(min_k, np.min(k_parallel))
        max_k = max(max_k, np.max(k_parallel))

    # Create a single figure and axis
    fig, ax = plt.subplots(figsize=(10, 8))
    initial_plot = all_plots[0]
    im = ax.pcolormesh(initial_plot['k_parallel'], initial_plot['energy_values'], initial_plot['data_values'], shading='auto')
    cbar = fig.colorbar(im, ax=ax, label='Intensity')
    
    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        # Update the plot
        im.set_array(plot_data['data_values'].ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        im.set_extent([min_k, max_k, min_energy, max_energy])
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}')
        ax.set_ylim(min_energy, max_energy)  # Set consistent y-axis limits
        ax.set_xlim(min_k, max_k)  # Set consistent x-axis limits
        
        # Update colorbar
        cbar.update_normal(im)
        
        fig.canvas.draw_idle()

    # Create the interactive widget
    interactive_plot = interactive(update_plot,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))

    # Initial plot
    update_plot(1, 0, int(max_data_value), 1.0, 0.0, 1.0)

    # Display the figure and interactive plot
    display(fig, interactive_plot)

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

kspace(data_files, work_function)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    min_energy = float('inf')
    max_energy = float('-inf')
    min_k = float('inf')
    max_k = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))
        min_energy = min(min_energy, np.min(energy_values))
        max_energy = max(max_energy, np.max(energy_values))
        min_k = min(min_k, np.min(k_parallel))
        max_k = max(max_k, np.max(k_parallel))

    # Create a single figure and axis
    fig, ax = plt.subplots(figsize=(10, 8))
    initial_plot = all_plots[0]
    im = ax.pcolormesh(initial_plot['k_parallel'], initial_plot['energy_values'], initial_plot['data_values'], shading='auto')
    cbar = fig.colorbar(im, ax=ax, label='Intensity')
    
    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        # Update the plot
        im.remove()
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}')
        ax.set_ylim(min_energy, max_energy)  # Set consistent y-axis limits
        ax.set_xlim(min_k, max_k)  # Set consistent x-axis limits
        
        # Update colorbar
        cbar.update_normal(im)
        
        fig.canvas.draw_idle()

    # Create the interactive widget
    interactive_plot = interactive(update_plot,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))

    # Initial plot
    update_plot(1, 0, int(max_data_value), 1.0, 0.0, 1.0)

    # Display the figure and interactive plot
    display(fig, interactive_plot)

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

kspace(data_files, work_function)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    min_energy = float('inf')
    max_energy = float('-inf')
    min_k = float('inf')
    max_k = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))
        min_energy = min(min_energy, np.min(energy_values))
        max_energy = max(max_energy, np.max(energy_values))
        min_k = min(min_k, np.min(k_parallel))
        max_k = max(max_k, np.max(k_parallel))

    # Create a single figure and axis
    fig, ax = plt.subplots(figsize=(10, 8))
    im = None
    cbar = None
    
    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        nonlocal im, cbar
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        # Update the plot
        if im is not None:
            im.remove()
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}')
        ax.set_ylim(min_energy, max_energy)  # Set consistent y-axis limits
        ax.set_xlim(min_k, max_k)  # Set consistent x-axis limits
        
        # Update colorbar
        if cbar is None:
            cbar = fig.colorbar(im, ax=ax, label='Intensity')
        else:
            cbar.update_normal(im)
        
        fig.canvas.draw_idle()

    # Create the interactive widget
    interactive_plot = interactive(update_plot,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))

    # Initial plot
    update_plot(1, 0, int(max_data_value), 1.0, 0.0, 1.0)

    # Display the figure and interactive plot
    display(fig, interactive_plot)

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

kspace(data_files, work_function)
import numpy as np
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout
from IPython.display import display

def load_and_process_data(file_path):
    # Load your data here
    # For this example, I'll create dummy data
    data = np.random.rand(100, 100)
    energy = np.linspace(-2, 2, 100)
    angle = np.linspace(-15, 15, 100)
    return data, energy, angle

def calculate_k_parallel(energy, angle, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg
    
    # Calculate k_parallel
    E_kin = work_function + energy
    k_parallel = np.sqrt(2 * m_e * E_kin) * np.sin(np.radians(angle)) / hbar
    return k_parallel

def update_plot(scan_index, vmin, vmax, work_function):
    # Load and process data for the current scan
    data, energy, angle = load_and_process_data(f"scan_{scan_index}.pxt")
    
    # Calculate k_parallel
    k_parallel = calculate_k_parallel(energy[:, np.newaxis], angle[np.newaxis, :], work_function)
    
    # Clear previous plot
    plt.clf()
    
    # Create new plot
    plt.pcolormesh(k_parallel, energy, data, vmin=vmin, vmax=vmax, shading='auto')
    plt.colorbar(label='Intensity')
    plt.xlabel(r'$k_\parallel$ (Å$^{-1}$)')
    plt.ylabel('Energy (eV)')
    plt.title(f'ARPES Data - Scan {scan_index}')
    
    plt.tight_layout()
    plt.show()

# Create interactive widget
interactive_plot = interactive(
    update_plot,
    scan_index=IntSlider(min=1, max=10, step=1, description='Scan:'),
    vmin=FloatSlider(min=0, max=1, step=0.01, description='Min:'),
    vmax=FloatSlider(min=0, max=1, step=0.01, value=1, description='Max:'),
    work_function=FloatSlider(min=4, max=5, step=0.1, value=4.5, description='Work Function (eV):'),
    continuous_update=False
)

# Display the interactive plot
display(interactive_plot)
import numpy as np
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider
from IPython.display import display

def load_and_process_data(file_path):
    # Replace this with your actual data loading code
    data = np.random.rand(100, 100)
    energy = np.linspace(-2, 2, 100)
    angle = np.linspace(-15, 15, 100)
    print(f"Loaded data shape: {data.shape}")
    return data, energy, angle

def calculate_k_parallel(energy, angle, work_function):
    # Your k_parallel calculation here
    k_parallel = np.outer(np.sqrt(work_function + energy), np.sin(np.radians(angle)))
    print(f"Calculated k_parallel shape: {k_parallel.shape}")
    return k_parallel

fig, ax = plt.subplots(figsize=(10, 8))
im = ax.imshow(np.zeros((100, 100)), aspect='auto', origin='lower')
plt.colorbar(im, label='Intensity')
ax.set_xlabel(r'$k_\parallel$ (Å$^{-1}$)')
ax.set_ylabel('Energy (eV)')
ax.set_title('ARPES Data')

def update_plot(scan_index, vmin, vmax, work_function):
    print(f"Updating plot with: scan_index={scan_index}, vmin={vmin}, vmax={vmax}, work_function={work_function}")
    
    data, energy, angle = load_and_process_data(f"scan_{scan_index}.pxt")
    k_parallel = calculate_k_parallel(energy, angle, work_function)
    
    im.set_data(data)
    im.set_extent([k_parallel.min(), k_parallel.max(), energy.min(), energy.max()])
    im.set_clim(vmin, vmax)
    
    ax.set_title(f'ARPES Data - Scan {scan_index}')
    fig.canvas.draw_idle()

interactive_plot = interactive(
    update_plot,
    scan_index=IntSlider(min=1, max=10, step=1, description='Scan:'),
    vmin=FloatSlider(min=0, max=1, step=0.01, description='Min:'),
    vmax=FloatSlider(min=0, max=1, step=0.01, value=1, description='Max:'),
    work_function=FloatSlider(min=4, max=5, step=0.1, value=4.5, description='Work Function (eV):'),
    continuous_update=False
)

display(interactive_plot)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    min_energy = float('inf')
    max_energy = float('-inf')
    min_k = float('inf')
    max_k = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))
        min_energy = min(min_energy, np.min(energy_values))
        max_energy = max(max_energy, np.max(energy_values))
        min_k = min(min_k, np.min(k_parallel))
        max_k = max(max_k, np.max(k_parallel))

    # Create a single figure and axis
    fig, ax = plt.subplots(figsize=(10, 8))
    im = ax.pcolormesh([], [], [])
    cbar = fig.colorbar(im, ax=ax, label='Intensity')
    
    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        # Update the plot
        im.set_array(plot_data['data_values'].ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        im.set_extent([min_k, max_k, min_energy, max_energy])
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}')
        ax.set_ylim(min_energy, max_energy)  # Set consistent y-axis limits
        ax.set_xlim(min_k, max_k)  # Set consistent x-axis limits
        
        # Update colorbar
        cbar.update_normal(im)
        
        fig.canvas.draw_idle()

    # Create the interactive widget
    interactive_plot = interactive(update_plot,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))

    # Initial plot
    update_plot(1, 0, int(max_data_value), 1.0, 0.0, 1.0)

    # Display the figure and interactive plot
    display(fig, interactive_plot)

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

kspace(data_files, work_function)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    min_energy = float('inf')
    max_energy = float('-inf')
    min_k = float('inf')
    max_k = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))
        min_energy = min(min_energy, np.min(energy_values))
        max_energy = max(max_energy, np.max(energy_values))
        min_k = min(min_k, np.min(k_parallel))
        max_k = max(max_k, np.max(k_parallel))

    # Create a single figure and axis
    fig, ax = plt.subplots(figsize=(10, 8))
    initial_plot = all_plots[0]
    im = ax.pcolormesh(initial_plot['k_parallel'], initial_plot['energy_values'], initial_plot['data_values'], shading='auto')
    cbar = fig.colorbar(im, ax=ax, label='Intensity')
    
    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        # Update the plot
        im.set_array(plot_data['data_values'].ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        im.set_extent([min_k, max_k, min_energy, max_energy])
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}')
        ax.set_ylim(min_energy, max_energy)  # Set consistent y-axis limits
        ax.set_xlim(min_k, max_k)  # Set consistent x-axis limits
        
        # Update colorbar
        cbar.update_normal(im)
        
        fig.canvas.draw_idle()

    # Create the interactive widget
    interactive_plot = interactive(update_plot,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))

    # Initial plot
    update_plot(1, 0, int(max_data_value), 1.0, 0.0, 1.0)

    # Display the figure and interactive plot
    display(fig, interactive_plot)

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

kspace(data_files, work_function)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    min_energy = float('inf')
    max_energy = float('-inf')
    min_k = float('inf')
    max_k = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))
        min_energy = min(min_energy, np.min(energy_values))
        max_energy = max(max_energy, np.max(energy_values))
        min_k = min(min_k, np.min(k_parallel))
        max_k = max(max_k, np.max(k_parallel))

    # Create a single figure and axis
    fig, ax = plt.subplots(figsize=(10, 8))
    initial_plot = all_plots[0]
    im = ax.pcolormesh(initial_plot['k_parallel'], initial_plot['energy_values'], initial_plot['data_values'], shading='auto')
    cbar = fig.colorbar(im, ax=ax, label='Intensity')
    
    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        # Update the plot
        im.remove()
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}')
        ax.set_ylim(min_energy, max_energy)  # Set consistent y-axis limits
        ax.set_xlim(min_k, max_k)  # Set consistent x-axis limits
        
        # Update colorbar
        cbar.update_normal(im)
        
        fig.canvas.draw_idle()

    # Create the interactive widget
    interactive_plot = interactive(update_plot,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))

    # Initial plot
    update_plot(1, 0, int(max_data_value), 1.0, 0.0, 1.0)

    # Display the figure and interactive plot
    display(fig, interactive_plot)

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

kspace(data_files, work_function)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    min_energy = float('inf')
    max_energy = float('-inf')
    min_k = float('inf')
    max_k = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))
        min_energy = min(min_energy, np.min(energy_values))
        max_energy = max(max_energy, np.max(energy_values))
        min_k = min(min_k, np.min(k_parallel))
        max_k = max(max_k, np.max(k_parallel))

    # Create a single figure and axis
    fig, ax = plt.subplots(figsize=(10, 8))
    im = None
    cbar = None
    
    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        nonlocal im, cbar
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        # Update the plot
        if im is not None:
            im.remove()
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}')
        ax.set_ylim(min_energy, max_energy)  # Set consistent y-axis limits
        ax.set_xlim(min_k, max_k)  # Set consistent x-axis limits
        
        # Update colorbar
        if cbar is None:
            cbar = fig.colorbar(im, ax=ax, label='Intensity')
        else:
            cbar.update_normal(im)
        
        fig.canvas.draw_idle()

    # Create the interactive widget
    interactive_plot = interactive(update_plot,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))

    # Initial plot
    update_plot(1, 0, int(max_data_value), 1.0, 0.0, 1.0)

    # Display the figure and interactive plot
    display(fig, interactive_plot)

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

kspace(data_files, work_function)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        plt.show()

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        fig.colorbar(im, ax=ax, label='Intensity')
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        plt.show()

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'Arial'

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        plt.show()

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        plt.show()

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap
from matplotlib import font_manager

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']

# Set Times New Roman for mathtext (numbers)
plt.rcParams['mathtext.fontset'] = 'custom'
plt.rcParams['mathtext.rm'] = 'Times New Roman'
plt.rcParams['mathtext.it'] = 'Times New Roman:italic'
plt.rcParams['mathtext.bf'] = 'Times New Roman:bold'

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size and use Times New Roman for numbers
        ax.tick_params(axis='both', which='major', labelsize=10)
        for label in ax.get_xticklabels() + ax.get_yticklabels():
            label.set_fontname('Times New Roman')
        
        plt.tight_layout()
        plt.show()

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
for fontpath in font_manager.findSystemFonts(fontpaths=None, fontext='ttf'):
    if 'FiraSans'.lower() in fontpath.lower():
        print(fontpath)
for fontpath in font_manager.findSystemFonts(fontpaths=None, fontext='ttf'):
    if 'Montserrat'.lower() in fontpath.lower():
        print(fontpath)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap
from matplotlib import font_manager

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']

# Set Times New Roman for mathtext (numbers)
plt.rcParams['mathtext.fontset'] = 'custom'
plt.rcParams['mathtext.rm'] = 'Times New Roman'
plt.rcParams['mathtext.it'] = 'Times New Roman:italic'
plt.rcParams['mathtext.bf'] = 'Times New Roman:bold'

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size and use Times New Roman for numbers
        ax.tick_params(axis='both', which='major', labelsize=10)
        for label in ax.get_xticklabels() + ax.get_yticklabels():
            label.set_fontname('Montserrat')
        
        plt.tight_layout()
        plt.show()

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        plt.show()

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        plt.show()

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, HBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display

# Your existing imports and setup code here...

def load_data_files(folder_path):
    # Your existing load_data_files function here...

def kspace(data_files, work_function):
    # Your existing constants and data loading code here...

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig

    def save_plot(b):
        # Get current slider values
        current_values = {k: v.value for k, v in interactive_plot.widgets.items() if isinstance(v, (IntSlider, FloatSlider))}
        
        # Generate the plot with current values
        fig = plot_arpes(**current_values)
        
        # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = kspace(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))



    # Your existing constants and data loading code here...

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig

    def save_plot(b):
        # Get current slider values
        current_values = {k: v.value for k, v in interactive_plot.widgets.items() if isinstance(v, (IntSlider, FloatSlider))}
        
        # Generate the plot with current values
        fig = plot_arpes(**current_values)
        
        # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = kspace(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, HBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))



    # Your existing constants and data loading code here...

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig

    def save_plot(b):
        # Get current slider values
        current_values = {k: v.value for k, v in interactive_plot.widgets.items() if isinstance(v, (IntSlider, FloatSlider))}
        
        # Generate the plot with current values
        fig = plot_arpes(**current_values)
        
        # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = kspace(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, HBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))



    # Your existing constants and data loading code here...

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig

    def save_plot(b):
    # Get current slider values
        current_values = {k: v.value for k, v in interactive_plot.widgets.items() if isinstance(v, (IntSlider, FloatSlider))}
    
    # Remove any unexpected keys
        expected_keys = ['scan_index', 'vmin', 'vmax', 'scale', 'cmap_start', 'cmap_end']
        current_values = {k: v for k, v in current_values.items() if k in expected_keys}
    
    # Generate the plot with current values
        fig = plot_arpes(**current_values)
    
    # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")
    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = kspace(data_files, work_function)
display(interactive_plot_with_save)
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.0": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, HBox\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\n\ndef kspace(data_files, work_function):\n    \n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    max_data_value = 0\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']  # Photon energy from the attributes\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        max_data_value = max(max_data_value, np.max(data.values))\n\n\n\n    # Your existing constants and data loading code here...\n\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], \n                           shading='auto', cmap=cmap, norm=norm)\n        cbar = fig.colorbar(im, ax=ax)\n        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n        cbar.ax.tick_params(labelsize=10)\n        \n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits\n        \n        # Adjust tick label font size\n        ax.tick_params(axis='both', which='major', labelsize=10)\n        \n        plt.tight_layout()\n        return fig\n\n\n\n\n\n\nB\n    def save_plot(b):\n    # Get current slider values\n        current_values = {k: v.value for k, v in interactive_plot.widgets.items() if isinstance(v, (IntSlider, FloatSlider))}\n    \n    # Remove any unexpected keys\n        expected_keys = ['scan_index', 'vmin', 'vmax', 'scale', 'cmap_start', 'cmap_end']\n        current_values = {k: v for k, v in current_values.items() if k in expected_keys}\n    \n    # Generate the plot with current values\n        fig = plot_arpes(**current_values)\n    \n    # Save the plot\n        filename = f\"ARPES_plot_scan_{current_values['scan_index']}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        plt.close(fig)\n        print(f\"Plot saved as {filename}\")\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),\n                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    # Combine the interactive plot and the save button\n    output = VBox([interactive_plot, save_button])\n    return output\n\n# Usage\nfolder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\ndata_files = load_data_files(folder_path)\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\ninteractive_plot_with_save = kspace(data_files, work_function)\ndisplay(interactive_plot_with_save)", 3382)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.0.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.0.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.1": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, HBox\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\n\ndef kspace(data_files, work_function):\n    \n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    max_data_value = 0\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']  # Photon energy from the attributes\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        max_data_value = max(max_data_value, np.max(data.values))\n\n\n\n    # Your existing constants and data loading code here...\n\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], \n                           shading='auto', cmap=cmap, norm=norm)\n        cbar = fig.colorbar(im, ax=ax)\n        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n        cbar.ax.tick_params(labelsize=10)\n        \n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits\n        \n        # Adjust tick label font size\n        ax.tick_params(axis='both', which='major', labelsize=10)\n        \n        plt.tight_layout()\n        return fig\n\n\n\n\n\n\nBRUH\n    def save_plot(b):\n    # Get current slider values\n        current_values = {k: v.value for k, v in interactive_plot.widgets.items() if isinstance(v, (IntSlider, FloatSlider))}\n    \n    # Remove any unexpected keys\n        expected_keys = ['scan_index', 'vmin', 'vmax', 'scale', 'cmap_start', 'cmap_end']\n        current_values = {k: v for k, v in current_values.items() if k in expected_keys}\n    \n    # Generate the plot with current values\n        fig = plot_arpes(**current_values)\n    \n    # Save the plot\n        filename = f\"ARPES_plot_scan_{current_values['scan_index']}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        plt.close(fig)\n        print(f\"Plot saved as {filename}\")\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),\n                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    # Combine the interactive plot and the save button\n    output = VBox([interactive_plot, save_button])\n    return output\n\n# Usage\nfolder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'\ndata_files = load_data_files(folder_path)\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\ninteractive_plot_with_save = kspace(data_files, work_function)\ndisplay(interactive_plot_with_save)", 3385)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.1.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.1.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.2": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, HBox\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()  # Sort files alphabetically\n    return [os.path.join(folder_path, f) for f in data_files]\n\ndef kspace(data_files, work_function):\n    \n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    max_data_value = 0\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']  # Photon energy from the attributes\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values  # Negative energy values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        max_data_value = max(max_data_value, np.max(data.values))\n\n\n\n    # Your existing constants and data loading code here...\n\n    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):\n        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index\n\n        fig, ax = plt.subplots(figsize=(10, 8))\n    \n        # Create a custom normalization\n        norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n        # Create a colormap with the specified range\n        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n    \n        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], \n                           shading='auto', cmap=cmap, norm=norm)\n        cbar = fig.colorbar(im, ax=ax)\n        cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n        cbar.ax.tick_params(labelsize=10)\n        \n        ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits\n        \n        # Adjust tick label font size\n        ax.tick_params(axis='both', which='major', labelsize=10)\n        \n        plt.tight_layout()\n        return fig\n\n\n    def save_plot(b):\n        # Get current slider values directly from the widgets\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'vmin': interactive_plot.children[1].value,\n            'vmax': interactive_plot.children[2].value,\n            'scale': interactive_plot.children[3].value,\n            'cmap_start': interactive_plot.children[4].value,\n            'cmap_end': interactive_plot.children[5].value\n        }\n        \n        # Generate the plot with current values\n        fig = plot_arpes(**current_values)\n        \n        # Save the plot\n        filename = f\"ARPES_plot_scan_{current_values['scan_index']}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        plt.close(fig)\n        print(f\"Plot saved as {filename}\")\n\n    # Create the interactive widget\n    interactive_plot = interactive(plot_arpes,\n                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),\n                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),\n                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),\n                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),\n                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),\n                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    # Combine the interactive plot and the save button\n    output = VBox([interactive_plot, save_button])\n    r\n\n\n", 5421)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.2.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.2.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, HBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))



    # Your existing constants and data loading code here...

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig


    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'vmin': interactive_plot.children[1].value,
            'vmax': interactive_plot.children[2].value,
            'scale': interactive_plot.children[3].value,
            'cmap_start': interactive_plot.children[4].value,
            'cmap_end': interactive_plot.children[5].value
        }
        
        # Generate the plot with current values
        fig = plot_arpes(**current_values)
        
        # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output


import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, HBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))



    # Your existing constants and data loading code here...

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig


    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'vmin': interactive_plot.children[1].value,
            'vmax': interactive_plot.children[2].value,
            'scale': interactive_plot.children[3].value,
            'cmap_start': interactive_plot.children[4].value,
            'cmap_end': interactive_plot.children[5].value
        }
        
        # Generate the plot with current values
        fig = plot_arpes(**current_values)
        
        # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = kspace(data_files, work_function)
display(interactive_plot_with_save)
