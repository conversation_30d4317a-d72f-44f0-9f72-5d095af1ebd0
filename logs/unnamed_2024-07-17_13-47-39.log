# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]
        ax.clear()
        
        data_to_plot = plot_data['data_values'].copy()
        
        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)
        
        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel)

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Normalize the curve if edge detection is off
            if not use_canny:
                kdc = kdc / np.max(kdc)

            # Apply vertical offset
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(energy_values, offset_kdc, label=f'k_parallel = {actual_k:.2f}')

            # Draw x'd line at the "zero level" of each curve
            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset

                ax.plot(energy_values, offset_fit, '--', label=f'Fit k_parallel = {actual_k:.2f}', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)

        # Ensure the entire curve is visible, including negative values
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),
        use_canny=Checkbox(value=False, description='Use Canny Filter'),
        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
