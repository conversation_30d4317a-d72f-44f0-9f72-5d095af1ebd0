# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import GaussianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
from ipywidgets import ToggleButtons

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_curves(scan_index, n, vertical_offset, show_curves, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type, fit_e_min, fit_e_max, color_mode, marker_type, division_point, curve_type, show_baseline, show_division_line, font_size):
        plot_data = all_plots[scan_index - 1]
        ax.clear()

        # Get the full data
        data_to_plot = plot_data['data_values'].copy()
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]
            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]
            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]
            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')
            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data

        if curve_type == 'EDC':
            indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)
            x_values = energy_values
            y_label = 'Intensity (arb. units)'
            x_label = r'$E-E_f$ (eV)'
            division_min, division_max = e_min, e_max
        else:  # MDC
            indices = np.linspace(0, len(valid_e_indices) - 1, n, dtype=int)
            x_values = k_parallel
            y_label = 'Intensity (arb. units)'
            x_label = r'$k_\parallel$ (Å⁻¹)'
            division_min, division_max = k_min, k_max

        # Ensure division_point is within the current bounds
        division_point = max(division_min, min(division_max, division_point))

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, index in enumerate(indices):
            if curve_type == 'EDC':
                curve = data_to_plot[:, index]
                actual_value = k_parallel[index]
                label = fr'$k_\parallel$ = {actual_value:.2f}'
            else:  # MDC
                curve = data_to_plot[index, :]
                actual_value = energy_values[index]
                label = fr'$E-E_f$ = {actual_value:.2f}'

            if isinstance(curve, xr.DataArray):
                curve = curve.values
            if isinstance(x_values, xr.DataArray):
                x_values = x_values.values

            if not use_canny:
                max_index = np.argmax(curve)
                min_index = np.argmin(curve)
                curve = curve / np.max(curve)

            offset_curve = curve + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_curve))
            min_intensity = min(min_intensity, np.min(offset_curve))

            if show_curves:
                if color_mode == 'Color':
                    ax.plot(x_values, offset_curve, label=label)
                else:  # Grayscale
                    ax.plot(x_values, offset_curve, color='black')
            
                if marker_type != 'None' and not use_canny:
                    if marker_type in ['Max', 'Dual Max']:
                        ax.plot(x_values[max_index], offset_curve[max_index], 'bo', markersize=10, fillstyle='none')
                
                    if marker_type in ['Min', 'Dual Min']:
                        ax.plot(x_values[min_index], offset_curve[min_index], 'ro', markersize=10, fillstyle='none')
                
                    if marker_type in ['Dual Max', 'Dual Min']:
                        left_index = np.argmax(curve[x_values <= division_point]) if 'Max' in marker_type else np.argmin(curve[x_values <= division_point])
                        right_index = len(x_values) - 1 - np.argmax(curve[x_values > division_point][::-1]) if 'Max' in marker_type else len(x_values) - 1 - np.argmin(curve[x_values > division_point][::-1])
                    
                        ax.plot(x_values[left_index], offset_curve[left_index], 'ro', markersize=10, fillstyle='none')
                        ax.plot(x_values[right_index], offset_curve[right_index], 'bo', markersize=10, fillstyle='none')

            if show_baseline:
                ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Filter data for fitting
                fit_indices = np.where((x_values >= fit_e_min) & (x_values <= fit_e_max))[0]
                fit_x_values = x_values[fit_indices]
                fit_curve = curve[fit_indices]

                if fit_type == 'Maxima':
                    peaks, _ = find_peaks(fit_curve)
                    peak_heights = fit_curve[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                    largest_peaks = peaks[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(fit_curve))

                    for j, peak in enumerate(largest_peaks):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_x_values[peak], min=fit_x_values.min(), max=fit_x_values.max())
                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                elif fit_type == 'Minima':
                    valleys, _ = find_peaks(-fit_curve)
                    valley_depths = np.max(fit_curve) - fit_curve[valleys]
                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]
                    largest_valleys = valleys[sorted_indices]

                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(fit_curve))

                    for j, valley in enumerate(largest_valleys):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_x_values[valley], min=fit_x_values.min(), max=fit_x_values.max())
                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(fit_curve, params, x=fit_x_values)
                fit = result.best_fit

                # Interpolate the fit back to the full x range
                full_fit = np.interp(x_values, fit_x_values, fit)
                offset_fit = full_fit + i * vertical_offset

                # Extract sigma values and their uncertainties for label
                sigmas = []
                sigma_errors = []
                for j in range(num_peaks):
                    sigma = abs(result.params[f'g{j+1}_sigma'].value)
                    sigma_error = result.params[f'g{j+1}_sigma'].stderr
                    sigmas.append(sigma)
                    sigma_errors.append(sigma_error)

                sigma_label = ', '.join([fr'$\sigma_{j+1}$={sigma:.3f} $\pm$ {error:.3f}' for j, (sigma, error) in enumerate(zip(sigmas, sigma_errors))])

                if color_mode == 'Color':
                    ax.plot(x_values, offset_fit, '--', label=fr'Fit {curve_type}={actual_value:.2f}, {sigma_label}', color=f'C{i}')
                else:  # Grayscale
                    ax.plot(x_values, offset_fit, '--', color='black')

                if fit_type == 'Maxima':
                    fit_peaks, _ = find_peaks(full_fit)
                    ax.plot(x_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)

                    for j, peak in enumerate(fit_peaks):
                        peak_x = x_values[peak]
                        peak_intensity = offset_fit[peak]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_x = peak_x - sigma
                        right_sigma_x = peak_x + sigma
                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)

                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=font_size, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=font_size, color='black')

                elif fit_type == 'Minima':
                    fit_valleys, _ = find_peaks(-full_fit)
                    ax.plot(x_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

                    for j, valley in enumerate(fit_valleys):
                        valley_x = x_values[valley]
                        valley_intensity = offset_fit[valley]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_x = valley_x - sigma
                        right_sigma_x = valley_x + sigma
                        left_sigma_intensity = np.interp(left_sigma_x, x_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_x, x_values, offset_fit)

                        ax.plot(left_sigma_x, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_x, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_x, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=font_size, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_x, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=font_size, color='black')

        # Add a vertical line at the division point
        if marker_type in ['Dual Max', 'Dual Min'] and show_division_line:
            ax.axvline(x=division_point, color='green', linestyle='--', alpha=0.5)

        ax.set_xlabel(x_label, fontsize=font_size, fontweight='bold')
        ax.set_ylabel(y_label, fontsize=font_size, fontweight='bold')
        ax.set_title(f'{curve_type}s - File: {plot_data["file_name"]}', fontsize=font_size+2, fontweight='bold')
    
        # Only show legend in Color mode
        if color_mode == 'Color':
            ax.legend(fontsize=font_size-2)
    
        ax.tick_params(axis='both', which='major', labelsize=font_size-2)
        ax.set_xlim(e_min if curve_type == 'EDC' else k_min, e_max if curve_type == 'EDC' else k_max)
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity
    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_curves': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'avg_e_min': interactive_plot.children[16].value,
            'avg_e_max': interactive_plot.children[17].value,
            'avg_k_min': interactive_plot.children[18].value,
            'avg_k_max': interactive_plot.children[19].value,
            'fit_type': interactive_plot.children[20].value,
            'fit_e_min': interactive_plot.children[21].value,
            'fit_e_max': interactive_plot.children[22].value,
            'color_mode': interactive_plot.children[23].value,
            'show_max_marker': interactive_plot.children[24].value,
            'show_second_max_marker': interactive_plot.children[25].value,
            'division_point': interactive_plot.children[26].value,
            'curve_type': interactive_plot.children[27].value,
            'font_size': interactive_plot.children[28].value  # Add this line for the font size slider
        }
        plot_curves(**current_values)
        filename = f"{current_values['curve_type']}_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}_font_{current_values['font_size']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def export_data(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_curves': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'avg_e_min': interactive_plot.children[16].value,
            'avg_e_max': interactive_plot.children[17].value,
            'avg_k_min': interactive_plot.children[18].value,
            'avg_k_max': interactive_plot.children[19].value,
            'fit_type': interactive_plot.children[20].value,
            'fit_e_min': interactive_plot.children[21].value,
            'fit_e_max': interactive_plot.children[22].value,
            'color_mode': interactive_plot.children[23].value,
            'show_max_marker': interactive_plot.children[24].value,
            'show_second_max_marker': interactive_plot.children[25].value,
            'division_point': interactive_plot.children[26].value,
            'curve_type': interactive_plot.children[27].value,
            'font_size': interactive_plot.children[28].value  # Add this line for font size
        }
        
        plot_data = all_plots[current_values['scan_index'] - 1]
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']
        data_values = plot_data['data_values']

        # Convert xarray DataArrays to numpy arrays if necessary
        if isinstance(k_parallel, xr.DataArray):
            k_parallel = k_parallel.values
        if isinstance(energy_values, xr.DataArray):
            energy_values = energy_values.values
        if isinstance(data_values, xr.DataArray):
            data_values = data_values.values

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]
        valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]
        data_to_plot = data_values[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if current_values['use_canny']:
            edges = canny(data_to_plot, sigma=current_values['sigma'], low_threshold=current_values['low_threshold'], high_threshold=current_values['high_threshold'])
            data_to_plot = edges.astype(float)

        if current_values['enable_averaging']:
            kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')

        if current_values['curve_type'] == 'EDC':
            indices = np.linspace(0, len(valid_k_indices) - 1, current_values['n'], dtype=int)
            x_values = energy_values
            y_label = 'Intensity (arb. units)'
            x_label = 'Energy (eV)'
        else:  # MDC
            indices = np.linspace(0, len(valid_e_indices) - 1, current_values['n'], dtype=int)
            x_values = k_parallel
            y_label = 'Intensity (arb. units)'
            x_label = 'k_parallel (Å⁻¹)'

        # Get the current axes
        ax = plt.gca()

        for i, index in enumerate(indices):
            if current_values['curve_type'] == 'EDC':
                curve = data_to_plot[:, index]
                actual_value = k_parallel[index]
            else:  # MDC
                curve = data_to_plot[index, :]
                actual_value = energy_values[index]

            if isinstance(actual_value, np.ndarray):
                actual_value = actual_value.item()

            # Apply normalization if not using Canny filter
            if not current_values['use_canny']:
                curve = curve / np.max(curve)

            # Export original/processed data
            np.savetxt(f"{current_values['curve_type']}_{actual_value:.2f}.dat", np.column_stack((x_values, curve)), header=f"{x_label}\t{y_label}")

            if current_values['show_fit']:
                # Find the fit line in the plot
                fit_line = None
                for line in ax.lines:
                    if line.get_label().startswith(f"Fit {current_values['curve_type']}={actual_value:.2f}"):
                        fit_line = line
                        break

                if fit_line is not None:
                    # Get the x and y data of the fit line
                    fit_x_data, fit_y_data = fit_line.get_data()
                    # Remove the vertical offset
                    fit_y_data -= i * current_values['vertical_offset']
                    # Export the fit data
                    np.savetxt(f"gaussian_fit_{current_values['curve_type']}_{actual_value:.2f}.dat", np.column_stack((fit_x_data, fit_y_data)), header=f"{x_label}\tFitted {y_label}")
                else:
                    print(f"No fit found for {current_values['curve_type']} = {actual_value:.2f}")

        print("Data export completed.")

    class DynamicFloatSlider(FloatSlider):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.observe(self._update_bounds, names=['value'])

        def _update_bounds(self, change):
            self.min = min(self.min, change['new'])
            self.max = max(self.max, change['new'])

    def update_division_point(*args):
        curve_type = interactive_plot.children[-1].value
        if curve_type == 'EDC':
            e_min = interactive_plot.children[8].value
            e_max = interactive_plot.children[9].value
            interactive_plot.children[-2].min = e_min
            interactive_plot.children[-2].max = e_max
        else:  # MDC
            k_min = interactive_plot.children[6].value
            k_max = interactive_plot.children[7].value
            interactive_plot.children[-2].min = k_min
            interactive_plot.children[-2].max = k_max

    def update_font_size(change):
        plt.rcParams.update({'font.size': change.new})
    
        param_map = {
            'Scan Index': 'scan_index',
            'Number of Curves': 'n',
            'Vertical Offset': 'vertical_offset',
            'E min': 'e_min',
            'E max': 'e_max',
            'K min': 'k_min',
            'K max': 'k_max',
            'Use Canny': 'use_canny',
            'Canny Sigma': 'canny_sigma',
            'Show Fit': 'show_fit',
            'Division Point': 'division_point',
            'Curve Type': 'curve_type'
        }
    
        kwargs = {}
        for i in range(len(interactive_plot.children)-1):
            widget = interactive_plot.children[i]
            if widget.description in param_map:
                kwargs[param_map[widget.description]] = widget.value
            else:
                kwargs[widget.description.lower().replace(' ', '_')] = widget.value
    
        plot_curves(**kwargs)
    
        # Update the plot
        fig = plt.gcf()
        fig.canvas.draw()
        fig.canvas.flush_events()    
    interactive_plot = interactive(
        plot_curves,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of Curves', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
        show_curves=Checkbox(value=True, description='Show Curves'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),
        use_canny=Checkbox(value=False, description='Use Canny Filter'),
        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),
        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),
        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),
        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min (Å⁻¹)', continuous_update=True),
        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max (Å⁻¹)', continuous_update=True),
        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),
        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),
        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True),
        color_mode=ToggleButtons(options=['Color', 'Grayscale'], description='Color Mode'),
        marker_type=ToggleButtons(options=['None', 'Max', 'Min', 'Dual Max', 'Dual Min'], description='Marker Type'),
        division_point=DynamicFloatSlider(value=(global_e_min + global_e_max) / 2, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Division Point', continuous_update=True),
        curve_type=ToggleButtons(options=['EDC', 'MDC'], description='Curve Type'),
        show_baseline=Checkbox(value=True, description='Show Baseline'),
        show_division_line=Checkbox(value=True, description='Show Division Line'),
        font_size=IntSlider(value=10, min=6, max=20, step=1, description='Font Size', continuous_update=False)
    )
    # Observe changes in curve_type, e_min, e_max, k_min, and k_max
    interactive_plot.children[-1].observe(update_division_point, names='value')
    interactive_plot.children[6].observe(update_division_point, names='value')
    interactive_plot.children[7].observe(update_division_point, names='value')
    interactive_plot.children[8].observe(update_division_point, names='value')
    interactive_plot.children[9].observe(update_division_point, names='value')
    interactive_plot.children[-2].observe(update_font_size, names='value')

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    export_button = Button(description="Export Data")
    export_button.on_click(export_data)

    output = VBox([interactive_plot, HBox([save_button, export_button])])
    return output
# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
