# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, HBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))



    # Your existing constants and data loading code here...

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig


    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'vmin': interactive_plot.children[1].value,
            'vmax': interactive_plot.children[2].value,
            'scale': interactive_plot.children[3].value,
            'cmap_start': interactive_plot.children[4].value,
            'cmap_end': interactive_plot.children[5].value
        }
        
        # Generate the plot with current values
        fig = plot_arpes(**current_values)
        
        # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = kspace(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, HBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))



    # Your existing constants and data loading code here...
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Sort the data
        sort_indices = np.lexsort((plot_data['energy_values'], plot_data['k_parallel']))
        k_parallel_sorted = plot_data['k_parallel'][sort_indices]
        energy_values_sorted = plot_data['energy_values'][sort_indices]
        data_values_sorted = plot_data['data_values'][sort_indices]

        # Reshape the sorted data
        unique_k = np.unique(k_parallel_sorted)
        unique_e = np.unique(energy_values_sorted)
        K, E = np.meshgrid(unique_k, unique_e)
        Z = griddata((k_parallel_sorted, energy_values_sorted), data_values_sorted, (K, E), method='nearest')

        fig, ax = plt.subplots(figsize=(10, 8))

        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)

        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)

        im = ax.pcolormesh(K, E, Z, shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(energy_values_sorted.min(), energy_values_sorted.max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig


    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'vmin': interactive_plot.children[1].value,
            'vmax': interactive_plot.children[2].value,
            'scale': interactive_plot.children[3].value,
            'cmap_start': interactive_plot.children[4].value,
            'cmap_end': interactive_plot.children[5].value
        }
        
        # Generate the plot with current values
        fig = plot_arpes(**current_values)
        
        # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = kspace(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, HBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))



    # Your existing constants and data loading code here...
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Sort the data
        sort_indices = np.lexsort((plot_data['energy_values'], plot_data['k_parallel']))
        k_parallel_sorted = plot_data['k_parallel'][sort_indices]
        energy_values_sorted = plot_data['energy_values'][sort_indices]
        data_values_sorted = plot_data['data_values'][sort_indices]

        # Reshape the sorted data
        unique_k = np.unique(k_parallel_sorted)
        unique_e = np.unique(energy_values_sorted)
        K, E = np.meshgrid(unique_k, unique_e)
        Z = griddata((k_parallel_sorted, energy_values_sorted), data_values_sorted, (K, E), method='nearest')

        fig, ax = plt.subplots(figsize=(10, 8))

        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)

        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)

        im = ax.pcolormesh(K, E, Z, shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(energy_values_sorted.min(), energy_values_sorted.max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig



    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'vmin': interactive_plot.children[1].value,
            'vmax': interactive_plot.children[2].value,
            'scale': interactive_plot.children[3].value,
            'cmap_start': interactive_plot.children[4].value,
            'cmap_end': interactive_plot.children[5].value
        }
        
        # Generate the plot with current values
        fig = plot_arpes(**current_values)
        
        # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = kspace(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, HBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))



    # Your existing constants and data loading code here...
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        # Sort the data
        sort_indices = np.lexsort((plot_data['energy_values'], plot_data['k_parallel']))
        k_parallel_sorted = plot_data['k_parallel'][sort_indices]
        energy_values_sorted = plot_data['energy_values'][sort_indices]
        data_values_sorted = plot_data['data_values'][sort_indices]

        # Reshape the sorted data
        unique_k = np.unique(k_parallel_sorted)
        unique_e = np.unique(energy_values_sorted)
        K, E = np.meshgrid(unique_k, unique_e)
        Z = griddata((k_parallel_sorted, energy_values_sorted), data_values_sorted, (K, E), method='nearest')

        fig, ax = plt.subplots(figsize=(10, 8))

        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)

        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)

        im = ax.pcolormesh(K, E, Z, shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(energy_values_sorted.min(), energy_values_sorted.max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig


    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'vmin': interactive_plot.children[1].value,
            'vmax': interactive_plot.children[2].value,
            'scale': interactive_plot.children[3].value,
            'cmap_start': interactive_plot.children[4].value,
            'cmap_end': interactive_plot.children[5].value
        }
        
        # Generate the plot with current values
        fig = plot_arpes(**current_values)
        
        # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = kspace(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, HBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))



    # Your existing constants and data loading code here...

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig


    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'vmin': interactive_plot.children[1].value,
            'vmax': interactive_plot.children[2].value,
            'scale': interactive_plot.children[3].value,
            'cmap_start': interactive_plot.children[4].value,
            'cmap_end': interactive_plot.children[5].value
        }
        
        # Generate the plot with current values
        fig = plot_arpes(**current_values)
        
        # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = kspace(data_files, work_function)
display(interactive_plot_with_save)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        plt.show()

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    return interactive_plot

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot = kspace(data_files, work_function)
interactive_plot
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, HBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        max_data_value = max(max_data_value, np.max(data.values))



    # Your existing constants and data loading code here...

    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index

        fig, ax = plt.subplots(figsize=(10, 8))
    
        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                           shading='auto', cmap=cmap, norm=norm)
        cbar = fig.colorbar(im, ax=ax)
        cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        cbar.ax.tick_params(labelsize=10)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_title(f'ARPES Data in Momentum Space - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
        
        # Adjust tick label font size
        ax.tick_params(axis='both', which='major', labelsize=10)
        
        plt.tight_layout()
        return fig


    def save_plot(b):
        # Get current slider values directly from the widgets
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'vmin': interactive_plot.children[1].value,
            'vmax': interactive_plot.children[2].value,
            'scale': interactive_plot.children[3].value,
            'cmap_start': interactive_plot.children[4].value,
            'cmap_end': interactive_plot.children[5].value
        }
        
        # Generate the plot with current values
        fig = plot_arpes(**current_values)
        
        # Save the plot
        filename = f"ARPES_plot_scan_{current_values['scan_index']}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        plt.close(fig)
        print(f"Plot saved as {filename}")

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    # Combine the interactive plot and the save button
    output = VBox([interactive_plot, save_button])
    return output

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot_with_save = kspace(data_files, work_function)
display(interactive_plot_with_save)
