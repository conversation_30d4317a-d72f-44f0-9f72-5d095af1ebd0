# IPython log file

from arpes.io import load_data
#from arpes.endstations.plugin import *
import numpy as np
#if not hasattr(np, 'complex'):
    #np.complex = complex
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')
from arpes.io import load_data
#from arpes.endstations.plugin import *
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = complex
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')
get_ipython().run_line_magic('pip', 'install numpy==1.18.5')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.88": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install numpy==1.\n", 21)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.88.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.88.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.89": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install numpy==1.20.\n", 24)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.89.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.89.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
get_ipython().run_line_magic('pip', 'install numpy==1.20.0')
from arpes.io import load_data
#from arpes.endstations.plugin import *
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = complex
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/Cr25PdTe2_1/Cr25_001_S008.pxt', location='BL4')
