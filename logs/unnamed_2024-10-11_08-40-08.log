# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, VBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings

warnings.filterwarnings("ignore", category=UserWarning)

def kspace(data_files, work_function):
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in data_files]

    # Extract energy, phi, and theta values
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values
    theta_values = np.array([data.attrs.get('theta', 0) for data in all_data])

    # Calculate k_x and k_y
    E_photon = all_data[0].attrs['hv']
    E_kin = E_photon - work_function - np.abs(energy_values)
    k_magnitude = np.sqrt(2 * m_e * E_kin * 1.602176634e-19) / hbar / 1e10  # in Å^-1

    phi_mesh, theta_mesh = np.meshgrid(np.radians(phi_values), np.radians(theta_values))
    k_x = k_magnitude[:, np.newaxis, np.newaxis] * np.sin(theta_mesh) * np.cos(phi_mesh)
    k_y = k_magnitude[:, np.newaxis, np.newaxis] * np.sin(theta_mesh) * np.sin(phi_mesh)

    # Create 3D intensity array
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        
        # Extract k_x, k_y, and intensity for the selected energy
        k_x_slice = k_x[energy_index]
        k_y_slice = k_y[energy_index]
        intensity_slice = intensity_3d[:, energy_index, :].T * intensity_multiplier

        # Adjust dimensions for pcolormesh
        X = k_x_slice[:-1, :-1]
        Y = k_y_slice[:-1, :-1]
        C = intensity_slice[:-1, :-1]

        # Create the constant energy map
        im = ax.pcolormesh(X, Y, C, shading='flat', cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        current_energy = energy_values[energy_index]
        ax.set_title(f'ARPES Constant Energy Map - E - E_F = {current_energy:.2f} eV', fontsize=14)
        ax.set_aspect('equal')
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

warnings.filterwarnings("ignore", category=UserWarning)

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values (assuming they're the same for all scans)
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.outer(np.sqrt(-2 * m_e * E_b) / hbar, np.sin(np.radians(phi_values))) / 10**10

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier, plot_type):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        if plot_type == 'phi':
            x_values = phi_values
            x_label = r'$\phi$ (degrees)'
        else:  # plot_type == 'k'
            x_values = k_parallel[energy_index]
            x_label = r'$k_\parallel$ ($\AA^{-1}$)'
        
        im = ax.pcolormesh(x_values, np.arange(len(all_data)), intensity_slice, 
                           shading='auto', cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(x_label, fontsize=12)
        if changing_param == 'hv':
            ax.set_ylabel('Photon Energy (eV)', fontsize=12)
            y_values = [data.attrs['hv'] for data in all_data]
        elif changing_param == 'polar':
            ax.set_ylabel('Polar Angle (degrees)', fontsize=12)
            y_values = [data.attrs.get('polar', 0) for data in all_data]
        else:
            ax.set_ylabel('Scan Number', fontsize=12)
            y_values = np.arange(len(all_data))
        
        ax.set_yticks(np.arange(len(all_data)))
        ax.set_yticklabels([f'{y:.2f}' for y in y_values])
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'ARPES Data - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    plot_type_slider = IntSlider(
        value=0,
        min=0,
        max=1,
        step=1,
        description='Plot Type',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    def plot_type_wrapper(energy_index, intensity_multiplier, plot_type):
        plot_type_str = 'phi' if plot_type == 0 else 'k'
        update_plot(energy_index, intensity_multiplier, plot_type_str)

    interactive_plot = interactive(plot_type_wrapper, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider,
                                   plot_type=plot_type_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values (assuming they're the same for all scans)
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.outer(np.sqrt(-2 * m_e * E_b) / hbar, np.sin(np.radians(phi_values))) / 10**10

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier, plot_type):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        if plot_type == 'phi':
            x_values = phi_values
            x_label = r'$\phi$ (degrees)'
        else:  # plot_type == 'k'
            x_values = k_parallel[energy_index]
            x_label = r'$k_\parallel$ ($\AA^{-1}$)'
        
        im = ax.pcolormesh(x_values, np.arange(len(all_data)), intensity_slice, 
                           shading='auto', cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(x_label, fontsize=12)
        if changing_param == 'hv':
            ax.set_ylabel('Photon Energy (eV)', fontsize=12)
            y_values = [data.attrs['hv'] for data in all_data]
        elif changing_param == 'polar':
            ax.set_ylabel('Polar Angle (degrees)', fontsize=12)
            y_values = [data.attrs.get('polar', 0) for data in all_data]
        else:
            ax.set_ylabel('Scan Number', fontsize=12)
            y_values = np.arange(len(all_data))
        
        ax.set_yticks(np.arange(len(all_data)))
        ax.set_yticklabels([f'{y:.2f}' for y in y_values])
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'ARPES Data - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    plot_type_slider = IntSlider(
        value=0,
        min=0,
        max=1,
        step=1,
        description='Plot Type',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    def plot_type_wrapper(energy_index, intensity_multiplier, plot_type):
        plot_type_str = 'phi' if plot_type == 0 else 'k'
        update_plot(energy_index, intensity_multiplier, plot_type_str)

    interactive_plot = interactive(plot_type_wrapper, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider,
                                   plot_type=plot_type_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values (assuming they're the same for all scans)
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.outer(np.sqrt(-2 * m_e * E_b) / hbar, np.sin(np.radians(phi_values))) / 10**10

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier, plot_type):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        if plot_type == 'phi':
            x_values = phi_values
            x_label = r'$\phi$ (degrees)'
        else:  # plot_type == 'k'
            x_values = k_parallel[energy_index]
            x_label = r'$k_\parallel$ ($\AA^{-1}$)'
        
        im = ax.pcolormesh(x_values, np.arange(len(all_data)), intensity_slice, 
                           shading='auto', cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(x_label, fontsize=12)
        if changing_param == 'hv':
            ax.set_ylabel('Photon Energy (eV)', fontsize=12)
            y_values = [data.attrs['hv'] for data in all_data]
        elif changing_param == 'polar':
            ax.set_ylabel('Polar Angle (degrees)', fontsize=12)
            y_values = [data.attrs.get('polar', 0) for data in all_data]
        else:
            ax.set_ylabel('Scan Number', fontsize=12)
            y_values = np.arange(len(all_data))
        
        ax.set_yticks(np.arange(len(all_data)))
        ax.set_yticklabels([f'{y:.2f}' for y in y_values])
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'ARPES Data - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    plot_type_slider = IntSlider(
        value=0,
        min=0,
        max=1,
        step=1,
        description='Plot Type',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    def plot_type_wrapper(energy_index, intensity_multiplier, plot_type):
        plot_type_str = 'phi' if plot_type == 0 else 'k'
        update_plot(energy_index, intensity_multiplier, plot_type_str)

    interactive_plot = interactive(plot_type_wrapper, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider,
                                   plot_type=plot_type_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.sqrt(-2 * m_e * E_b[:, np.newaxis]) / hbar * np.sin(np.radians(phi_values)) / 10**10

    # Calculate kx and ky
    kx = k_parallel * np.cos(np.radians(phi_values))
    ky = k_parallel * np.sin(np.radians(phi_values))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.concatenate([kx[energy_index] for _ in range(len(all_data))])
        ky_combined = np.concatenate([ky[energy_index] + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.sqrt(-2 * m_e * E_b[:, np.newaxis]) / hbar * np.sin(np.radians(phi_values)) / 10**10

    # Calculate kx and ky
    kx = k_parallel * np.cos(np.radians(all_data[0].polar.values))
    ky = k_parallel * np.sin(np.radians(all_data[0].polar.values))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.concatenate([kx[energy_index] for _ in range(len(all_data))])
        ky_combined = np.concatenate([ky[energy_index] + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.sqrt(-2 * m_e * E_b[:, np.newaxis]) / hbar * np.sin(np.radians(phi_values)) / 10**10

    # Calculate kx and ky
    kx = k_parallel * np.cos(np.radians(all_data[0].polar))
    ky = k_parallel * np.sin(np.radians(all_data[0].polar))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.concatenate([kx[energy_index] for _ in range(len(all_data))])
        ky_combined = np.concatenate([ky[energy_index] + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.sqrt(-2 * m_e * E_b[:, np.newaxis]) / hbar * np.sin(np.radians(phi_values)) / 10**10

    # Calculate kx and ky
    kx = k_parallel * np.cos(np.radians(all_data[0].azimuth))
    ky = k_parallel * np.sin(np.radians(all_data[0].azimuth))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.concatenate([kx[energy_index] for _ in range(len(all_data))])
        ky_combined = np.concatenate([ky[energy_index] + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.sqrt(-2 * m_e * E_b[:, np.newaxis]) / hbar * np.sin(np.radians(phi_values)) / 10**10

    # Calculate kx and ky
    kx = k_parallel * np.cos(np.radians(all_data[0].azimuth))*np.sin(np.radians(all_data[0].polar))
    ky = k_parallel * np.sin(np.radians(all_data[0].azimuth))*np.sin(np.radians(all_data[0].polar))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.concatenate([kx[energy_index] for _ in range(len(all_data))])
        ky_combined = np.concatenate([ky[energy_index] + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.sqrt(-2 * m_e * E_b[:, np.newaxis]) / hbar * np.sin(np.radians(phi_values)) / 10**10

    # Calculate kx and ky
    kx = k_parallel * np.cos(np.radians(all_data[0].azimuth))
    ky = k_parallel * np.sin(np.radians(all_data[0].azimuth))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.concatenate([kx[energy_index] for _ in range(len(all_data))])
        ky_combined = np.concatenate([ky[energy_index] + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.sqrt(-2 * m_e * E_b[:, np.newaxis]) / hbar * np.sin(np.radians(phi_values)) / 10**10

    # Calculate kx and ky
    kx = k_parallel * np.cos(np.radians(all_data[0].azimuth))
    ky = k_parallel * np.sin(np.radians(all_data[0].azimuth))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.concatenate([kx[energy_index] for _ in range(len(all_data))])
        ky_combined = np.concatenate([ky[energy_index] + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.sqrt(-2 * m_e * E_b[:, np.newaxis]) / hbar * np.sin(np.radians(phi_values)) / 10**10

    # Calculate kx and ky
    kx = k_parallel
    ky = k_parallel

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.concatenate([kx[energy_index] for _ in range(len(all_data))])
        ky_combined = np.concatenate([ky[energy_index] + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.sqrt(-2 * m_e * E_b[:, np.newaxis]) / hbar * np.sin(np.radians(phi_values)) / 10**10

    # Calculate kx and ky
    kx = k_parallel * np.cos(np.radians(all_data[0].polar.values))
    ky = k_parallel * np.sin(np.radians(all_data[0].polar.values))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.concatenate([kx[energy_index] for _ in range(len(all_data))])
        ky_combined = np.concatenate([ky[energy_index] + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.sqrt(-2 * m_e * E_b[:, np.newaxis]) / hbar * np.sin(np.radians(phi_values)) / 10**10

    # Calculate kx and ky
    kx = k_parallel * np.cos(np.radians(phi_values))
    ky = k_parallel * np.sin(np.radians(phi_values))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.concatenate([kx[energy_index] for _ in range(len(all_data))])
        ky_combined = np.concatenate([ky[energy_index] + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.sqrt(-2 * m_e * E_b[:, np.newaxis]) / hbar * np.sin(np.radians(phi_values)) / 10**10

    # Calculate kx and ky
    kx = k_parallel * np.cos(np.radians(all_data[0].polar))
    ky = k_parallel * np.sin(np.radians(all_data[0].polar))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.concatenate([kx[energy_index] for _ in range(len(all_data))])
        ky_combined = np.concatenate([ky[energy_index] + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.sqrt(-2 * m_e * E_b[:, np.newaxis]) / hbar * np.sin(np.radians(phi_values)) / 10**10

    # Calculate kx and ky
    kx = k_parallel * np.cos(np.radians(all_data[0].polar))
    ky = k_parallel * np.sin(np.radians(all_data[0].polar))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.concatenate([kx[energy_index] for _ in range(len(all_data))])
        ky_combined = np.concatenate([ky[energy_index] + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.sqrt(-2 * m_e * E_b[:, np.newaxis]) / hbar * np.sin(np.radians(phi_values)) / 10**10

    # Calculate kx and ky
    kx = k_parallel * np.cos(np.radians(all_data[0].polar))
    ky = k_parallel * np.sin(np.radians(all_data[0].polar))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.concatenate([kx[energy_index] for _ in range(len(all_data))])
        ky_combined = np.concatenate([ky[energy_index] + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.sqrt(-2 * m_e * E_b[:, np.newaxis]) / hbar * np.sin(np.radians(phi_values)) / 10**10

    # Calculate kx and ky
    kx = k_parallel * np.cos(np.radians(all_data[0].polar))
    ky = k_parallel * np.sin(np.radians(all_data[0].polar))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.concatenate([kx[energy_index] for _ in range(len(all_data))])
        ky_combined = np.concatenate([ky[energy_index] + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.sqrt(-2 * m_e * E_b[:, np.newaxis]) / hbar * np.sin(np.radians(phi_values)) / 10**10

    # Calculate kx and ky
    kx = k_parallel * np.cos(np.radians(all_data[0].azimuth))
    ky = k_parallel * np.sin(np.radians(all_data[0].azimuth))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.concatenate([kx[energy_index] for _ in range(len(all_data))])
        ky_combined = np.concatenate([ky[energy_index] + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
read_single_pxt('/home/<USER>/Documents/PtTe2prime/PtTe_001/PtTe_001_S015.pxt')
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values (assuming they're the same for all scans)
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.outer(np.sqrt(-2 * m_e * E_b) / hbar, np.sin(np.radians(phi_values))) / 10**10

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier, plot_type):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        if plot_type == 'phi':
            x_values = phi_values
            x_label = r'$\phi$ (degrees)'
        else:  # plot_type == 'k'
            x_values = k_parallel[energy_index]
            x_label = r'$k_\parallel$ ($\AA^{-1}$)'
        
        im = ax.pcolormesh(x_values, np.arange(len(all_data)), intensity_slice, 
                           shading='auto', cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(x_label, fontsize=12)
        if changing_param == 'hv':
            ax.set_ylabel('Photon Energy (eV)', fontsize=12)
            y_values = [data.attrs['hv'] for data in all_data]
        elif changing_param == 'polar':
            ax.set_ylabel('Polar Angle (degrees)', fontsize=12)
            y_values = [data.attrs.get('polar', 0) for data in all_data]
        else:
            ax.set_ylabel('Scan Number', fontsize=12)
            y_values = np.arange(len(all_data))
        
        ax.set_yticks(np.arange(len(all_data)))
        ax.set_yticklabels([f'{y:.2f}' for y in y_values])
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'ARPES Data - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    plot_type_slider = IntSlider(
        value=0,
        min=0,
        max=1,
        step=1,
        description='Plot Type',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    def plot_type_wrapper(energy_index, intensity_multiplier, plot_type):
        plot_type_str = 'phi' if plot_type == 0 else 'k'
        update_plot(energy_index, intensity_multiplier, plot_type_str)

    interactive_plot = interactive(plot_type_wrapper, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider,
                                   plot_type=plot_type_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values (assuming they're the same for all scans)
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.outer(np.sqrt(-2 * m_e * E_b) / hbar, np.sin(np.radians(phi_values))) / 10**10

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier, plot_type):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        if plot_type == 'phi':
            x_values = phi_values
            x_label = r'$\phi$ (degrees)'
        else:  # plot_type == 'k'
            x_values = k_parallel[energy_index]
            x_label = r'$k_\parallel$ ($\AA^{-1}$)'
        
        im = ax.pcolormesh(x_values, np.arange(len(all_data)), intensity_slice, 
                           shading='auto', cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(x_label, fontsize=12)
        if changing_param == 'hv':
            ax.set_ylabel('Photon Energy (eV)', fontsize=12)
            y_values = [data.attrs['hv'] for data in all_data]
        elif changing_param == 'polar':
            ax.set_ylabel('Polar Angle (degrees)', fontsize=12)
            y_values = [data.attrs.get('polar', 0) for data in all_data]
        else:
            ax.set_ylabel('Scan Number', fontsize=12)
            y_values = np.arange(len(all_data))
        
        ax.set_yticks(np.arange(len(all_data)))
        ax.set_yticklabels([f'{y:.2f}' for y in y_values])
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'ARPES Data - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    plot_type_slider = IntSlider(
        value=0,
        min=0,
        max=1,
        step=1,
        description='Plot Type',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    def plot_type_wrapper(energy_index, intensity_multiplier, plot_type):
        plot_type_str = 'phi' if plot_type == 0 else 'k'
        update_plot(energy_index, intensity_multiplier, plot_type_str)

    interactive_plot = interactive(plot_type_wrapper, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider,
                                   plot_type=plot_type_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values (assuming they're the same for all scans)
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.outer(np.sqrt(-2 * m_e * E_b) / hbar, np.sin(np.radians(phi_values))) / 10**10

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier, plot_type):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        if plot_type == 'phi':
            x_values = phi_values
            x_label = r'$\phi$ (degrees)'
        else:  # plot_type == 'k'
            x_values = k_parallel[energy_index]
            x_label = r'$k_\parallel$ ($\AA^{-1}$)'
        
        im = ax.pcolormesh(x_values, np.arange(len(all_data)), intensity_slice, 
                           shading='auto', cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(x_label, fontsize=12)
        if changing_param == 'hv':
            ax.set_ylabel('Photon Energy (eV)', fontsize=12)
            y_values = [data.attrs['hv'] for data in all_data]
        elif changing_param == 'polar':
            ax.set_ylabel('Polar Angle (degrees)', fontsize=12)
            y_values = [data.attrs.get('polar', 0) for data in all_data]
        else:
            ax.set_ylabel('Scan Number', fontsize=12)
            y_values = np.arange(len(all_data))
        
        ax.set_yticks(np.arange(len(all_data)))
        ax.set_yticklabels([f'{y:.2f}' for y in y_values])
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'ARPES Data - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    plot_type_slider = IntSlider(
        value=0,
        min=0,
        max=1,
        step=1,
        description='Plot Type',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    def plot_type_wrapper(energy_index, intensity_multiplier, plot_type):
        plot_type_str = 'phi' if plot_type == 0 else 'k'
        update_plot(energy_index, intensity_multiplier, plot_type_str)

    interactive_plot = interactive(plot_type_wrapper, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider,
                                   plot_type=plot_type_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values (assuming they're the same for all scans)
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.outer(np.sqrt(-2 * m_e * E_b) / hbar, np.sin(np.radians(phi_values))) / 10**10

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier, plot_type):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        if plot_type == 'phi':
            x_values = phi_values
            x_label = r'$\phi$ (degrees)'
        else:  # plot_type == 'k'
            x_values = k_parallel[energy_index]
            x_label = r'$k_\parallel$ ($\AA^{-1}$)'
        
        im = ax.pcolormesh(x_values, np.arange(len(all_data)), intensity_slice, 
                           shading='auto', cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(x_label, fontsize=12)
        if changing_param == 'hv':
            ax.set_ylabel('Photon Energy (eV)', fontsize=12)
            y_values = [data.attrs['hv'] for data in all_data]
        elif changing_param == 'polar':
            ax.set_ylabel('Polar Angle (degrees)', fontsize=12)
            y_values = [data.attrs.get('polar', 0) for data in all_data]
        else:
            ax.set_ylabel('Scan Number', fontsize=12)
            y_values = np.arange(len(all_data))
        
        ax.set_yticks(np.arange(len(all_data)))
        ax.set_yticklabels([f'{y:.2f}' for y in y_values])
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'ARPES Data - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    plot_type_slider = IntSlider(
        value=0,
        min=0,
        max=1,
        step=1,
        description='Plot Type',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    def plot_type_wrapper(energy_index, intensity_multiplier, plot_type):
        plot_type_str = 'phi' if plot_type == 0 else 'k'
        update_plot(energy_index, intensity_multiplier, plot_type_str)

    interactive_plot = interactive(plot_type_wrapper, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider,
                                   plot_type=plot_type_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values (assuming they're the same for all scans)
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.outer(np.sqrt(-2 * m_e * E_b) / hbar, np.sin(np.radians(phi_values))) / 10**10

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier, plot_type):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        if plot_type == 'phi':
            x_values = phi_values
            x_label = r'$\phi$ (degrees)'
        else:  # plot_type == 'k'
            x_values = k_parallel[energy_index]
            x_label = r'$k_\parallel$ ($\AA^{-1}$)'
        
        im = ax.pcolormesh(x_values, np.arange(len(all_data)), intensity_slice, 
                           shading='auto', cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(x_label, fontsize=12)
        if changing_param == 'hv':
            ax.set_ylabel('Photon Energy (eV)', fontsize=12)
            y_values = [data.attrs['hv'] for data in all_data]
        elif changing_param == 'polar':
            ax.set_ylabel('Polar Angle (degrees)', fontsize=12)
            y_values = [data.attrs.get('polar', 0) for data in all_data]
        else:
            ax.set_ylabel('Scan Number', fontsize=12)
            y_values = np.arange(len(all_data))
        
        ax.set_yticks(np.arange(len(all_data)))
        ax.set_yticklabels([f'{y:.2f}' for y in y_values])
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'ARPES Data - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    plot_type_slider = IntSlider(
        value=0,
        min=0,
        max=1,
        step=1,
        description='Plot Type',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    def plot_type_wrapper(energy_index, intensity_multiplier, plot_type):
        plot_type_str = 'phi' if plot_type == 0 else 'k'
        update_plot(energy_index, intensity_multiplier, plot_type_str)

    interactive_plot = interactive(plot_type_wrapper, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider,
                                   plot_type=plot_type_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Extract energy and phi values (assuming they're the same for all scans)
    energy_values = -all_data[0].eV.values
    phi_values = all_data[0].phi.values

    # Create intensity_3d directly from the data values
    intensity_3d = np.array([data.values for data in all_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Calculate k_parallel for all energies and phi values
    E_photon = all_data[0].attrs['hv']
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.outer(np.sqrt(-2 * m_e * E_b) / hbar, np.sin(np.radians(phi_values))) / 10**10

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier, plot_type):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        if plot_type == 'phi':
            x_values = phi_values
            x_label = r'$\phi$ (degrees)'
        else:  # plot_type == 'k'
            x_values = k_parallel[energy_index]
            x_label = r'$k_\parallel$ ($\AA^{-1}$)'
        
        im = ax.pcolormesh(x_values, np.arange(len(all_data)), intensity_slice, 
                           shading='auto', cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(x_label, fontsize=12)
        if changing_param == 'hv':
            ax.set_ylabel('Photon Energy (eV)', fontsize=12)
            y_values = [data.attrs['hv'] for data in all_data]
        elif changing_param == 'polar':
            ax.set_ylabel('Polar Angle (degrees)', fontsize=12)
            y_values = [data.attrs.get('polar', 0) for data in all_data]
        else:
            ax.set_ylabel('Scan Number', fontsize=12)
            y_values = np.arange(len(all_data))
        
        ax.set_yticks(np.arange(len(all_data)))
        ax.set_yticklabels([f'{y:.2f}' for y in y_values])
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'ARPES Data - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    plot_type_slider = IntSlider(
        value=0,
        min=0,
        max=1,
        step=1,
        description='Plot Type',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    def plot_type_wrapper(energy_index, intensity_multiplier, plot_type):
        plot_type_str = 'phi' if plot_type == 0 else 'k'
        update_plot(energy_index, intensity_multiplier, plot_type_str)

    interactive_plot = interactive(plot_type_wrapper, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider,
                                   plot_type=plot_type_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, VBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from arpes.utilities.conversion import convert_to_kspace
from arpes.utilities.normalize import normalize_to_spectrum

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Convert data to k-space
    k_data = [convert_to_kspace(data) for data in all_data]

    # Normalize intensity
    normalized_data = [normalize_to_spectrum(data) for data in k_data]

    # Extract energy, kx, and ky values
    energy_values = normalized_data[0].eV.values
    kx_values = normalized_data[0].kx.values
    ky_values = normalized_data[0].ky.values

    # Create intensity_3d
    intensity_3d = np.array([data.values for data in normalized_data])

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.tile(kx_values, len(all_data))
        ky_combined = np.repeat(ky_values + np.arange(len(all_data))[:, np.newaxis] * 0.05, len(kx_values))
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, VBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from arpes.utilities.conversion import convert_to_kspace
from arpes.utilities.normalize import normalize_to_spectrum

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param


def manual_convert_to_kspace(data, work_function):
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg
    
    # Extract necessary values
    energy_values = data.eV.values
    phi_values = data.phi.values
    E_photon = data.attrs['hv']
    
    # Calculate k_parallel
    E_b = (work_function + np.abs(energy_values) - E_photon) * 1.602176634e-19
    k_parallel = np.sqrt(-2 * m_e * E_b[:, np.newaxis]) / hbar * np.sin(np.radians(phi_values)) / 10**10
    
    # Calculate kx and ky
    azimuth = data.attrs.get('azimuth', 0)  # Use 0 if 'azimuth' is not present
    kx = k_parallel * np.cos(np.radians(azimuth))
    ky = k_parallel * np.sin(np.radians(azimuth))
    
    # Create new DataArray with k-space coordinates
    k_data = xr.DataArray(
        data.values,
        coords={'eV': energy_values, 'kx': kx, 'ky': ky},
        dims=['eV', 'kx', 'ky']
    )
    
    return k_data

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Load all scans
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Convert data to k-space
    k_data = [manual_convert_to_kspace(data, work_function) for data in all_data]

    # Normalize intensity
    max_intensity = max(np.max(data.values) for data in k_data)
    normalized_data = [data / max_intensity for data in k_data]

    # Extract energy, kx, and ky values
    energy_values = normalized_data[0].eV.values
    kx_values = normalized_data[0].kx.values[0]  # Take the first row as they're all the same
    ky_values = normalized_data[0].ky.values[0]  # Take the first row as they're all the same

    # Create intensity_3d
    intensity_3d = np.array([data.values for data in normalized_data])

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.tile(kx_values, len(all_data))
        ky_combined = np.repeat(ky_values + np.arange(len(all_data))[:, np.newaxis] * 0.05, len(kx_values))
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from arpes import *

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Load all scans using PyARPES
    all_data = [arpes.load.pxt(file_path) for file_path in sorted_files]

    # Convert to k-space using PyARPES
    k_converted_data = [arpes.conversion.convert_coordinates(data, 'kx', 'ky') for data in all_data]

    # Extract energy values
    energy_values = -k_converted_data[0].coords['eV'].values

    # Create intensity_3d directly from the k-converted data values
    intensity_3d = np.array([data.values for data in k_converted_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Extract kx and ky values
    kx_values = k_converted_data[0].coords['kx'].values
    ky_values = k_converted_data[0].coords['ky'].values

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.tile(kx_values, len(all_data))
        ky_combined = np.concatenate([ky_values + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
import arpes

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Load all scans using PyARPES
    all_data = [arpes.load.pxt(file_path) for file_path in sorted_files]

    # Convert to k-space using PyARPES
    k_converted_data = [arpes.conversion.convert_coordinates(data, 'kx', 'ky') for data in all_data]

    # Extract energy values
    energy_values = -k_converted_data[0].coords['eV'].values

    # Create intensity_3d directly from the k-converted data values
    intensity_3d = np.array([data.values for data in k_converted_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Extract kx and ky values
    kx_values = k_converted_data[0].coords['kx'].values
    ky_values = k_converted_data[0].coords['ky'].values

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.tile(kx_values, len(all_data))
        ky_combined = np.concatenate([ky_values + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
import arpes.load_pxt

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Load all scans using PyARPES
    all_data = [arpes.load.pxt(file_path) for file_path in sorted_files]

    # Convert to k-space using PyARPES
    k_converted_data = [arpes.conversion.convert_coordinates(data, 'kx', 'ky') for data in all_data]

    # Extract energy values
    energy_values = -k_converted_data[0].coords['eV'].values

    # Create intensity_3d directly from the k-converted data values
    intensity_3d = np.array([data.values for data in k_converted_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Extract kx and ky values
    kx_values = k_converted_data[0].coords['kx'].values
    ky_values = k_converted_data[0].coords['ky'].values

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.tile(kx_values, len(all_data))
        ky_combined = np.concatenate([ky_values + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from arpes import *

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Load all scans using PyARPES
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Convert to k-space using PyARPES
    k_converted_data = [arpes.conversion.convert_coordinates(data, 'kx', 'ky') for data in all_data]

    # Extract energy values
    energy_values = -k_converted_data[0].coords['eV'].values

    # Create intensity_3d directly from the k-converted data values
    intensity_3d = np.array([data.values for data in k_converted_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Extract kx and ky values
    kx_values = k_converted_data[0].coords['kx'].values
    ky_values = k_converted_data[0].coords['ky'].values

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.tile(kx_values, len(all_data))
        ky_combined = np.concatenate([ky_values + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from arpes import *
from arpes.conversion import *

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Load all scans using PyARPES
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Convert to k-space using PyARPES
    k_converted_data = [arpes.conversion.convert_coordinates(data, 'kx', 'ky') for data in all_data]

    # Extract energy values
    energy_values = -k_converted_data[0].coords['eV'].values

    # Create intensity_3d directly from the k-converted data values
    intensity_3d = np.array([data.values for data in k_converted_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Extract kx and ky values
    kx_values = k_converted_data[0].coords['kx'].values
    ky_values = k_converted_data[0].coords['ky'].values

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.tile(kx_values, len(all_data))
        ky_combined = np.concatenate([ky_values + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from arpes import *
from arpes.utilities.conversion import *

get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Load all scans using PyARPES
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Convert to k-space using PyARPES
    k_converted_data = [arpes.conversion.convert_coordinates(data, 'kx', 'ky') for data in all_data]

    # Extract energy values
    energy_values = -k_converted_data[0].coords['eV'].values

    # Create intensity_3d directly from the k-converted data values
    intensity_3d = np.array([data.values for data in k_converted_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Extract kx and ky values
    kx_values = k_converted_data[0].coords['kx'].values
    ky_values = k_converted_data[0].coords['ky'].values

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.tile(kx_values, len(all_data))
        ky_combined = np.concatenate([ky_values + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from arpes import *
from arpes.utilities.conversion import *
from arpes.utilities.conversion.forward import convert_coordinate_forward
convert_coordinates
get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Load all scans using PyARPES
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Convert to k-space using PyARPES
    k_converted_data = [arpes.conversion.convert_coordinate_forward(data, 'kx', 'ky') for data in all_data]

    # Extract energy values
    energy_values = -k_converted_data[0].coords['eV'].values

    # Create intensity_3d directly from the k-converted data values
    intensity_3d = np.array([data.values for data in k_converted_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Extract kx and ky values
    kx_values = k_converted_data[0].coords['kx'].values
    ky_values = k_converted_data[0].coords['ky'].values

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.tile(kx_values, len(all_data))
        ky_combined = np.concatenate([ky_values + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from arpes import *
from arpes.utilities.conversion import *
from arpes.utilities.conversion.forward import convert_coordinate_forward
get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Load all scans using PyARPES
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Convert to k-space using PyARPES
    k_converted_data = [convert_coordinates(data, 'kx', 'ky') for data in all_data]

    # Extract energy values
    energy_values = -k_converted_data[0].coords['eV'].values

    # Create intensity_3d directly from the k-converted data values
    intensity_3d = np.array([data.values for data in k_converted_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Extract kx and ky values
    kx_values = k_converted_data[0].coords['kx'].values
    ky_values = k_converted_data[0].coords['ky'].values

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.tile(kx_values, len(all_data))
        ky_combined = np.concatenate([ky_values + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from arpes import *
from arpes.utilities.conversion import *
from arpes.utilities.conversion.forward import convert_coordinate_forward
get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Load all scans using PyARPES
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Convert to k-space using PyARPES
    k_converted_data = [convert_to_kspace for data in all_data]

    # Extract energy values
    energy_values = -k_converted_data[0].coords['eV'].values

    # Create intensity_3d directly from the k-converted data values
    intensity_3d = np.array([data.values for data in k_converted_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Extract kx and ky values
    kx_values = k_converted_data[0].coords['kx'].values
    ky_values = k_converted_data[0].coords['ky'].values

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.tile(kx_values, len(all_data))
        ky_combined = np.concatenate([ky_values + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from arpes import *
from arpes.utilities.conversion import *
from arpes.utilities.conversion.forward import convert_coordinate_forward
get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Load all scans using PyARPES
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Convert to k-space using PyARPES
    k_converted_data = [convert_to_kspace for data in all_data]
    print(k_converted_data)
    # Extract energy values
    energy_values = -k_converted_data[0].coords['eV'].values

    # Create intensity_3d directly from the k-converted data values
    intensity_3d = np.array([data.values for data in k_converted_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Extract kx and ky values
    kx_values = k_converted_data[0].coords['kx'].values
    ky_values = k_converted_data[0].coords['ky'].values

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.tile(kx_values, len(all_data))
        ky_combined = np.concatenate([ky_values + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from arpes import *
from arpes.utilities.conversion import *
from arpes.utilities.conversion.forward import convert_coordinate_forward
get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Load all scans using PyARPES
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Convert to k-space using PyARPES
    k_converted_data = [convert_to_kspace for data in all_data]
    print(k_converted_data())
    # Extract energy values
    energy_values = -k_converted_data[0].coords['eV'].values

    # Create intensity_3d directly from the k-converted data values
    intensity_3d = np.array([data.values for data in k_converted_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Extract kx and ky values
    kx_values = k_converted_data[0].coords['kx'].values
    ky_values = k_converted_data[0].coords['ky'].values

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.tile(kx_values, len(all_data))
        ky_combined = np.concatenate([ky_values + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from arpes import *
from arpes.utilities.conversion import *
from arpes.utilities.conversion.forward import convert_coordinate_forward
get_ipython().run_line_magic('matplotlib', 'widget')

warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

def sort_files(data_files):
    # Load metadata from all files
    metadata = []
    for file in data_files:
        data = read_single_pxt(file)
        metadata.append({
            'file': file,
            'hv': data.attrs['hv'],
            'polar': data.attrs.get('polar', 0)  # Use 0 if 'polar' is not present
        })
    
    # Determine which parameter is changing
    hv_values = set(item['hv'] for item in metadata)
    polar_values = set(item['polar'] for item in metadata)
    
    if len(hv_values) > 1:
        # Sort by hv
        sorted_files = sorted(metadata, key=lambda x: x['hv'])
        changing_param = 'hv'
    elif len(polar_values) > 1:
        # Sort by polar angle
        sorted_files = sorted(metadata, key=lambda x: x['polar'])
        changing_param = 'polar'
    else:
        # If neither is changing, maintain original order
        sorted_files = metadata
        changing_param = None
    
    return [item['file'] for item in sorted_files], changing_param

def kspace_map(data_files, work_function):
    # Sort files and determine changing parameter
    sorted_files, changing_param = sort_files(data_files)
    
    # Load all scans using PyARPES
    all_data = [read_single_pxt(file_path) for file_path in sorted_files]

    # Convert to k-space using PyARPES
    k_converted_data = [convert_to_kspace(data) for data in all_data]
    print(k_converted_data)
    # Extract energy values
    energy_values = -k_converted_data[0].coords['eV'].values

    # Create intensity_3d directly from the k-converted data values
    intensity_3d = np.array([data.values for data in k_converted_data])

    # Normalize intensity by the maximum intensity across all scans
    max_intensity = np.max(intensity_3d)
    intensity_3d = intensity_3d / max_intensity

    # Extract kx and ky values
    kx_values = k_converted_data[0].coords['kx'].values
    ky_values = k_converted_data[0].coords['ky'].values

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=0, vmax=1)
    cmap = plt.get_cmap('viridis')

    def update_plot(energy_index, intensity_multiplier):
        ax.clear()
        intensity_slice = intensity_3d[:, energy_index, :] * intensity_multiplier
        
        # Combine data from all scans
        kx_combined = np.tile(kx_values, len(all_data))
        ky_combined = np.concatenate([ky_values + i * 0.05 for i in range(len(all_data))])  # Offset each scan
        intensity_combined = intensity_slice.flatten()
        
        # Create the constant energy map
        im = ax.tricontourf(kx_combined, ky_combined, intensity_combined, 
                            levels=100, cmap=cmap, norm=norm)
        
        if not hasattr(update_plot, 'colorbar'):
            update_plot.colorbar = fig.colorbar(im, ax=ax)
            update_plot.colorbar.set_label('Normalized Intensity', fontsize=12)
        else:
            update_plot.colorbar.update_normal(im)
        
        ax.set_xlabel(r'$k_x$ ($\AA^{-1}$)', fontsize=12)
        ax.set_ylabel(r'$k_y$ ($\AA^{-1}$)', fontsize=12)
        
        current_energy = energy_values[energy_index]
        ax.set_title(f'Constant Energy Map - Energy: {current_energy:.2f} eV', fontsize=14)
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_aspect('equal')
        plt.tight_layout()
        fig.canvas.draw_idle()

    energy_slider = IntSlider(
        value=0,
        min=0,
        max=len(energy_values) - 1,
        step=1,
        description='Energy Index',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    intensity_slider = FloatSlider(
        value=1.0,
        min=0.1,
        max=5.0,
        step=0.1,
        description='Intensity Multiplier',
        continuous_update=False,
        layout=Layout(width='50%')
    )

    interactive_plot = interactive(update_plot, 
                                   energy_index=energy_slider, 
                                   intensity_multiplier=intensity_slider)
    
    output = VBox([interactive_plot])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
    work_function = 4.5  # eV (adjust this value if needed)
    interactive_plot = kspace_map(data_files, work_function)
    display(interactive_plot)
else:
    print("No folder selected.")
read_single_pxt('/home/<USER>/Documents/PtTe2prime/PtTe_001/PtTe_001_S001.pxt')
convert_coordinates_to_kspace_forward(read_single_pxt('/home/<USER>/Documents/PtTe2prime/PtTe_001/PtTe_001_S001.pxt'))
convert_coordinates(read_single_pxt('/home/<USER>/Documents/PtTe2prime/PtTe_001/PtTe_001_S001.pxt'))
read_single_pxt('/home/<USER>/Documents/PtTe2prime/PtTe_001/PtTe_001_S001.pxt').spectrum
read_single_pxt('/home/<USER>/Documents/PtTe2prime/PtTe_001/PtTe_001_S001.pxt').S
read_single_pxt('/home/<USER>/Documents/PtTe2prime/PtTe_001/PtTe_001_S001.pxt')
load_pxt('/home/<USER>/Documents/PtTe2prime/PtTe_001/PtTe_001_S001.pxt')
get_ipython().run_line_magic('pinfo2', 'load_pxt')
from arpes.io import example_data
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.103": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\ne", 35)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.103.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.103.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.io import example_data
example_data
from arpes.io import example_data
example_data()
from arpes.io import example_data
example_data
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.104": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nexample_data.", 47)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.104.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.104.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.105": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nexample_data", 46)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.105.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.105.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.106": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nexample_dat", 45)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.106.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.106.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.io import example_data
example_data.load_example_data()
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.107": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nre", 35)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.107.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.107.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.108": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nread_single_pxt(e)", 51)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.108.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.108.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.io import example_data
read_single_pxt(example_data)
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.109": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\ne", 35)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.109.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.109.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.110": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nexample_data.", 47)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.110.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.110.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.111": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nexample_data.spe", 50)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.111.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.111.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.112": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nexample_data.spec", 51)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.112.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.112.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.113": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nexample_data.spect", 52)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.113.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.113.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.114": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nexample_data.spectru", 53)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.114.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.114.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.115": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nexample_data.spectrum", 55)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.115.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.115.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.io import example_data
example_data.spectrum
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.116": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nexample_data.m", 48)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.116.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.116.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.io import example_data
example_data.map
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.117": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nexample_data.map.", 51)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.117.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.117.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.118": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nexample_data.map.spec", 55)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.118.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.118.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.119": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nexample_data.map.spect", 56)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.119.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.119.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.120": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nexample_data.map.spectr", 57)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.120.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.120.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.121": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import example_data\nexample_data.map.spectrum", 59)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.121.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.121.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.io import example_data
example_data.map.spectrum
