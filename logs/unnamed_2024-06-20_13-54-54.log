# IPython log file

from arpes import *
from arpes.io import load_data
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.41": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import load_data\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', l)\n", 146)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.41.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.41.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.42": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import load_data\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location=I)\n", 155)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.42.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.42.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.43": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import load_data\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location=Igor)\n", 158)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.43.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.43.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes import *
from arpes.io import load_data
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location=Igor)
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.44": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import load_data\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='Igor)\n", 155)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.44.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.44.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.45": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes import *\nfrom arpes.io import load_data\ndata = load_data('/home/<USER>/Documents/September 20file=", 109, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.45.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.45.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.46": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import load_data\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='Igor')\n", 160)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.46.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.46.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.47": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes import *\nfrom arpes.io import load_data\ndata = load_data('/home/<USER>/Documents/September 2022 BeArithmeticError", 124, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.47.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.47.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes import *
from arpes.io import load_data
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='Igor')
from arpes import *
from arpes.io import load_data
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='igor')
from arpes import *
from arpes.io import load_data
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.48": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import load_data\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', '')\n", 146)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.48.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.48.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.49": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes import *\nfrom arpes.io import load_data\ndata = load_data('/home/<USER>/Documents/Sepfile=", 100, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.49.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.49.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.50": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes import *\nfrom arpes.io import load_data\ndata = load_data('/home/<USER>/Documents/SepBaseException", 108, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.50.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.50.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.51": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes import *\nfrom arpes.io import load_data\ndata = load_data('/home/<USER>/Documents/SepBlockingIOError", 110, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.51.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.51.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.52": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import load_data\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', 'BL2.')\n", 150)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.52.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.52.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes import *
from arpes.io import load_data
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', 'BL2.3')
from arpes import *
from arpes.io import load_data
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', 'igor')
from arpes import *
from arpes.io import load_data
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', '')
from arpes import *
from arpes.io import load_data
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', 'BL7')
from arpes import *
from arpes.io import load_data
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='ALS-BL7')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.53": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import load_data\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='ALS-BL7')\nl", 166)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.53.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.53.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes import *
from arpes.io import load_data
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='ALS-BL7')
get_ipython().run_line_magic('pinfo2', 'load_data')
from arpes import *
from arpes.io import load_data
#data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='ALS-BL7')
get_ipython().run_line_magic('pinfo2', 'load_data')
