# IPython log file

from arpes import *
from arpes.io import *
data = load_data(41))
from arpes import *
from arpes.io import *
data = load_data(41)
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.0": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data(41, l)\n", 65)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.0.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.0.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.1": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data(41, loccat)\n", 70)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.1.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.1.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.2": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data(41, loccati)\n", 71)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.2.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.2.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.3": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data(41, loccatio)\n", 72)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.3.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.3.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.4": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data(41, loccation)\n", 73)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.4.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.4.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.5": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data(41, loccation=B)\n", 75)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.5.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.5.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.6": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data(41, loccation=BL7')\n", 78)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.6.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.6.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.7": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data(41, loccation=BL7')\n", 77)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.7.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.7.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.8": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data(41, loccation='BL7')\n", 75)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.8.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.8.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.9": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes import *\nfrom arpes.ifile=", 37, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.9.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.9.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes import *
from arpes.io import *
data = load_data(41, loccation='BL7')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.10": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('', loccation='BL7')\n", 61)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.10.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.10.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.11": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes import file=", 23, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.11.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.11.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.12": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('', loccation='BL7')\n", 62)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.12.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.12.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.13": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes import *ArithmeticError", 34, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.13.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.13.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes import *
from arpes.io import *
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', loccation='BL7')
from arpes import *
from arpes.io import *
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', location='BL7')
from arpes import *
from arpes.io import *
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.14": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', i)\n", 138)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.14.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.14.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.15": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', igno)\n", 141)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.15.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.15.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.16": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', ignore)\n", 143)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.16.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.16.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.17": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', ignore_)\n", 144)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.17.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.17.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.18": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', ignore_m)\n", 145)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.18.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.18.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.19": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', ignore_mi)\n", 146)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.19.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.19.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.20": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', ignore_mis)\n", 147)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.20.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.20.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.21": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', ignore_miss)\n", 148)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.21.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.21.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.22": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', ignore_missi)\n", 149)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.22.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.22.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.23": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', ignore_missin)\n", 150)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.23.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.23.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.24": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', ignore_missing_)\n", 152)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.24.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.24.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.25": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', ignore_missing_s)\n", 153)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.25.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.25.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.26": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', ignore_missing_si)\n", 154)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.26.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.26.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.27": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', ignore_missing_sim)\n", 155)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.27.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.27.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.28": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', ignore_missing_simp)\n", 156)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.28.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.28.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.29": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', ignore_missing_simpl)\n", 157)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.29.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.29.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.30": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', ignore_missing_simple=T)\n", 160)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.30.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.30.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes import *
from arpes.io import *
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', ignore_missing_simple=True)
from arpes import *
from arpes.io import *
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.31": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("p", 1)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.31.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.31.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.32": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pxt", 3)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.32.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.32.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.33": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("l", 1)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.33.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.33.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.34": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("load_pxt", 8, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.34.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.34.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
load_pxt
get_ipython().run_line_magic('pinfo2', 'load_pxt')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.35": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("l", 1)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.35.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.35.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
load_data
get_ipython().run_line_magic('pinfo2', 'load_data')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.36": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("l", 1)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.36.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.36.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.37": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', p)\n", 138)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.37.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.37.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.38": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', pxt)\n", 140)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.38.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.38.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.39": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import *\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', pxt=T)\n", 142)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.39.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.39.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes import *
from arpes.io import *
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', pxt=True)
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.40": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes import *\nfrom arpes.io import l\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S133.pxt', pxt=True)\n", 42)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.40.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.40.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
