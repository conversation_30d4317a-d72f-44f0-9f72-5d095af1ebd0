# IPython log file

from arpes.io import load_data
from arpes.endstations.plugin.igor_plugin import IgorEndstation
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S134.pxt', location='Igor')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.80": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\ni\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S134.pxt', location='Igor')", 96)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.80.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.80.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.81": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\nimport n\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S134.pxt', location='Igor')", 103)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.81.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.81.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.82": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\nimport numpy a\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S134.pxt', location='Igor')", 109)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.82.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.82.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.83": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes.ioas", 15, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.83.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.83.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.84": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\nimport numpy as n\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S134.pxt', location='Igor')", 112)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.84.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.84.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.85": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("from arpes.io imnext", 20, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.85.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.85.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.86": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\nfrom arpes.endstations.plugin.igor_plugin import IgorEndstation\nimport numpy as np\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S134.pxt', location='Igor')", 113)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.86.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.86.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.io import load_data
from arpes.endstations.plugin.igor_plugin import IgorEndstation
import numpy as np
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PdTe2_1/PdTe2_041_S134.pxt', location='Igor')
from arpes.io import load_data
from arpes.endstations.plugin.igor_plugin import IgorEndstation
import numpy as np
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S009.pxt', location='Igor')
from arpes.io import load_data
from arpes.endstations.plugin.igor_plugin import IgorEndstation
import numpy
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S009.pxt', location='Igor')
from arpes.io import load_data
from arpes.endstations.plugin.igor_plugin import IgorEndstation
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = complex
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S009.pxt', location='Igor')
import os

file_path = '/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S009.pxt'
print(f"File exists: {os.path.exists(file_path)}")
print(f"File is readable: {os.access(file_path, os.R_OK)}")
from arpes.endstations.plugin.igor_plugin import IgorEndstation

endstation = IgorEndstation()
scan_desc = {'file': file_path}
try:
    data = endstation.load(scan_desc)
    print("Data loaded successfully")
except Exception as e:
    print(f"Error loading data: {str(e)}")
with open(file_path, 'rb') as f:
    header = f.read(100)  # Read first 100 bytes
print(f"File header: {header}")
data = load_data('/path/to/your/merlin/file.pxt', location='MERLIN')
from arpes.io import load_data
from arpes.endstations.plugin.igor_plugin import *
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = complex
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S009.pxt', location='MERLIN')
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.87": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("from arpes.io import load_data\nfrom arpes.endstations.plugin import *\nimport numpy as np\nif not hasattr(np, 'complex'):\n    np.complex = complex\ndata = load_data('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S009.pxt', location='MERLIN')", 60)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.87.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.87.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
from arpes.io import load_data
from arpes.endstations.plugin import *
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = complex
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S009.pxt', location='MERLIN')
from arpes.io import load_data
from arpes.endstations.plugin import *
import numpy as np
if not hasattr(np, 'complex'):
    np.complex = complex
data = load_data('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S009.pxt', location='BL4')
