# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['<PERSON>ja<PERSON><PERSON> Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, 
                 vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size,
                 enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Apply convolutions if enabled
        data_to_plot = plot_data['data_values'].copy()
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

        # Apply moving average if enabled
        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve2d(data_to_plot, averaging_kernel)

        # Calculate equally spaced energy indices within the specified range
        energy_values = plot_data['energy_values']
        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        e_indices = valid_indices[e_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, e_index in enumerate(e_indices):
            actual_e = energy_values[e_index]
            kdc = data_to_plot[e_index, :]
            k_parallel = plot_data['k_parallel'][0]

            # Filter k_parallel values within the specified range
            valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
            kdc = kdc[valid_k_indices]
            k_parallel = k_parallel[valid_k_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Apply vertical offset without normalization
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(k_parallel, offset_kdc, label=f'E = {actual_e:.2f} eV')

            # Draw x'd line at the "zero level" of each curve
            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=k_parallel[peak], min=k_parallel.min(), max=k_parallel.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=k_parallel)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset

                ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(k_parallel[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(k_parallel[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('k$_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'KDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(k_min, k_max)

        # Ensure the entire curve is visible, including negative values
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()
        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'vertical_enabled': interactive_plot.children[10].value,
            'horizontal_enabled': interactive_plot.children[11].value,
            'vertical_magnitude': interactive_plot.children[12].value,
            'horizontal_magnitude': interactive_plot.children[13].value,
            'kernel_size': interactive_plot.children[14].value,
            'enable_averaging': interactive_plot.children[15].value,
            'averaging_kernel_size': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"KDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of KDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=global_intensity_max*10, step=global_intensity_max/100, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show KDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, 
                 vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size,
                 enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Apply convolutions if enabled
        data_to_plot = plot_data['data_values'].copy()
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

        # Apply moving average if enabled
        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve2d(data_to_plot, averaging_kernel)

        # Calculate equally spaced energy indices within the specified range
        energy_values = plot_data['energy_values']
        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        e_indices = valid_indices[e_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, e_index in enumerate(e_indices):
            actual_e = energy_values[e_index]
            kdc = data_to_plot[e_index, :]
            k_parallel = plot_data['k_parallel'][0]

            # Filter k_parallel values within the specified range
            valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
            kdc = kdc[valid_k_indices]
            k_parallel = k_parallel[valid_k_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Apply vertical offset without normalization
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(k_parallel, offset_kdc, label=f'E = {actual_e:.2f} eV')

            # Draw x'd line at the "zero level" of each curve
            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=k_parallel[peak], min=k_parallel.min(), max=k_parallel.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=k_parallel)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset

                ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(k_parallel[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(k_parallel[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('k$_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'KDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(k_min, k_max)

        # Ensure the entire curve is visible, including negative values
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()
        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'vertical_enabled': interactive_plot.children[10].value,
            'horizontal_enabled': interactive_plot.children[11].value,
            'vertical_magnitude': interactive_plot.children[12].value,
            'horizontal_magnitude': interactive_plot.children[13].value,
            'kernel_size': interactive_plot.children[14].value,
            'enable_averaging': interactive_plot.children[15].value,
            'averaging_kernel_size': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"KDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of KDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=global_intensity_max*10, step=global_intensity_max/100, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show KDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, 
                 vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size,
                 enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Apply convolutions if enabled
        data_to_plot = plot_data['data_values'].copy()
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

        # Apply moving average if enabled
        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve2d(data_to_plot, averaging_kernel)

        # Calculate equally spaced energy indices within the specified range
        energy_values = plot_data['energy_values']
        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        e_indices = valid_indices[e_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, e_index in enumerate(e_indices):
            actual_e = energy_values[e_index]
            kdc = data_to_plot[e_index, :]
            k_parallel = plot_data['k_parallel'][0]

            # Filter k_parallel values within the specified range
            valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
            kdc = kdc[valid_k_indices]
            k_parallel = k_parallel[valid_k_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Apply vertical offset without normalization
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(k_parallel, offset_kdc, label=f'E = {actual_e:.2f} eV')

            # Draw x'd line at the "zero level" of each curve
            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=k_parallel[peak], min=k_parallel.min(), max=k_parallel.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=k_parallel)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset

                ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(k_parallel[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(k_parallel[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('k$_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'KDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(k_min, k_max)

        # Ensure the entire curve is visible, including negative values
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()
        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'vertical_enabled': interactive_plot.children[10].value,
            'horizontal_enabled': interactive_plot.children[11].value,
            'vertical_magnitude': interactive_plot.children[12].value,
            'horizontal_magnitude': interactive_plot.children[13].value,
            'kernel_size': interactive_plot.children[14].value,
            'enable_averaging': interactive_plot.children[15].value,
            'averaging_kernel_size': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"KDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of KDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=global_intensity_max*10, step=global_intensity_max/100, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show KDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
