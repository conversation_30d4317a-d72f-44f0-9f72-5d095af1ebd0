# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import GaussianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
from ipywidgets import ToggleButtons

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = (work_function + np.abs(data.eV) - E_photon)*1.602176634e-19
        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar)/10**10

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, fit_type):
        plot_data = all_plots[scan_index - 1]
        ax.clear()
        
        # Get the full data
        data_to_plot = plot_data['data_values'].copy()
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']
        
        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        
        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            # Ensure the averaging kernel is 2D and has odd dimensions
            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel, mode='reflect')

        k_indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]

            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            if not use_canny:
                kdc = kdc / np.max(kdc)

            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')
                ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                if fit_type == 'Maxima':
                    peaks, _ = find_peaks(kdc)
                    peak_heights = kdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                    largest_peaks = peaks[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))
                    for j, peak in enumerate(largest_peaks):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)
                elif fit_type == 'Minima':
                    valleys, _ = find_peaks(-kdc)
                    valley_depths = np.max(kdc) - kdc[valleys]
                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]
                    largest_valleys = valleys[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(kdc))
                    for j, valley in enumerate(largest_valleys):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=energy_values[valley], min=energy_values.min(), max=energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset

                # Extract sigma values and their uncertainties for label
                sigmas = []
                sigma_errors = []
                for j in range(num_peaks):
                    sigma = abs(result.params[f'g{j+1}_sigma'].value)
                    sigma_error = result.params[f'g{j+1}_sigma'].stderr
                    sigmas.append(sigma)
                    sigma_errors.append(sigma_error)

                sigma_label = ', '.join([fr'$\sigma_{j+1}$={sigma:.3f} $\pm$ {error:.3f}' for j, (sigma, error) in enumerate(zip(sigmas, sigma_errors))])
                ax.plot(energy_values, offset_fit, '--', label=fr'Fit $k_\parallel$={actual_k:.2f}, {sigma_label}', color=f'C{i}')

                if fit_type == 'Maxima':
                    fit_peaks, _ = find_peaks(fit)
                    ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                    for j, peak in enumerate(fit_peaks):
                        peak_energy = energy_values[peak]
                        peak_intensity = offset_fit[peak]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_energy = peak_energy - sigma
                        right_sigma_energy = peak_energy + sigma
                        left_sigma_intensity = np.interp(left_sigma_energy, energy_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_energy, energy_values, offset_fit)
                        ax.plot(left_sigma_energy, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_energy, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_energy, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_energy, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')
                elif fit_type == 'Minima':
                    fit_valleys, _ = find_peaks(-fit)
                    ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)
                    for j, valley in enumerate(fit_valleys):
                        valley_energy = energy_values[valley]
                        valley_intensity = offset_fit[valley]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_energy = valley_energy - sigma
                        right_sigma_energy = valley_energy + sigma
                        left_sigma_intensity = np.interp(left_sigma_energy, energy_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_energy, energy_values, offset_fit)
                        ax.plot(left_sigma_energy, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_energy, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_energy, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_energy, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')

        ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)
        plt.tight_layout()
        fig.canvas.draw_idle()
        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'fit_type': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def export_data(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'fit_type': interactive_plot.children[16].value
        }
        
        plot_data = all_plots[current_values['scan_index'] - 1]
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, current_values['n'], dtype=int)
        k_indices = valid_indices[k_indices]
        
        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']
            valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]
            
            if current_values['enable_averaging']:
                # Ensure the averaging kernel is 1D and has an odd size
                kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)
                averaging_kernel = np.ones(kernel_size) / kernel_size
                kdc = convolve(kdc, averaging_kernel, mode='reflect')
            
            # Export averaged data
            np.savetxt(f"averaged_data_k_{actual_k:.2f}.dat", np.column_stack((energy_values, kdc)), header="Energy (eV)\tIntensity (arb. units)")
            
            if current_values['show_fit']:
                # Perform fitting
                if current_values['fit_type'] == 'Maxima':
                    peaks, _ = find_peaks(kdc)
                    peak_heights = kdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-current_values['num_peaks']:]
                    largest_peaks = peaks[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))
                    for j, peak in enumerate(largest_peaks):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)
                elif current_values['fit_type'] == 'Minima':
                    valleys, _ = find_peaks(-kdc)
                    valley_depths = np.max(kdc) - kdc[valleys]
                    sorted_indices = np.argsort(valley_depths)[-current_values['num_peaks']:]
                    largest_valleys = valleys[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(kdc))
                    for j, valley in enumerate(largest_valleys):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=energy_values[valley], min=energy_values.min(), max=energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)
                
                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                
                # Export Gaussian fit
                np.savetxt(f"gaussian_fit_k_{actual_k:.2f}.dat", np.column_stack((energy_values, fit)), header="Energy (eV)\tFitted Intensity (arb. units)")
        
        print("Data export completed.")



    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),
        use_canny=Checkbox(value=False, description='Use Canny Filter'),
        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),
        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type')
)
    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    export_button = Button(description="Export Data")
    export_button.on_click(export_data)

    output = VBox([interactive_plot, HBox([save_button, export_button])])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import GaussianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
from ipywidgets import ToggleButtons

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type):
        plot_data = all_plots[scan_index - 1]
        ax.clear()

        # Get the full data
        data_to_plot = plot_data['data_values'].copy()
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            # Ensure the averaging kernel is 2D and has odd dimensions
            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)

            # Define the range for averaging
            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]
            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]

            # Apply averaging only to the specified range
            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]
            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')
            
            # Replace the averaged part in the original data
            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data

        k_indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)
        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]

            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            if not use_canny:
                kdc = kdc / np.max(kdc)

            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')
                ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                if fit_type == 'Maxima':
                    peaks, _ = find_peaks(kdc)
                    peak_heights = kdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                    largest_peaks = peaks[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))
                    for j, peak in enumerate(largest_peaks):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)
                elif fit_type == 'Minima':
                    valleys, _ = find_peaks(-kdc)
                    valley_depths = np.max(kdc) - kdc[valleys]
                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]
                    largest_valleys = valleys[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(kdc))
                    for j, valley in enumerate(largest_valleys):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=energy_values[valley], min=energy_values.min(), max=energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset

        ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)
        plt.tight_layout()
        fig.canvas.draw_idle()
        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'fit_type': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def export_data(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'fit_type': interactive_plot.children[16].value
        }
        
        plot_data = all_plots[current_values['scan_index'] - 1]
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, current_values['n'], dtype=int)
        k_indices = valid_indices[k_indices]
        
        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']
            valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]
            
            if current_values['enable_averaging']:
                # Ensure the averaging kernel is 1D and has an odd size
                kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)
                averaging_kernel = np.ones(kernel_size) / kernel_size
                kdc = convolve(kdc, averaging_kernel, mode='reflect')
            
            # Export averaged data
            np.savetxt(f"averaged_data_k_{actual_k:.2f}.dat", np.column_stack((energy_values, kdc)), header="Energy (eV)\tIntensity (arb. units)")
            
            if current_values['show_fit']:
                # Perform fitting
                if current_values['fit_type'] == 'Maxima':
                    peaks, _ = find_peaks(kdc)
                    peak_heights = kdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-current_values['num_peaks']:]
                    largest_peaks = peaks[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))
                    for j, peak in enumerate(largest_peaks):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)
                elif current_values['fit_type'] == 'Minima':
                    valleys, _ = find_peaks(-kdc)
                    valley_depths = np.max(kdc) - kdc[valleys]
                    sorted_indices = np.argsort(valley_depths)[-current_values['num_peaks']:]
                    largest_valleys = valleys[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(kdc))
                    for j, valley in enumerate(largest_valleys):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=energy_values[valley], min=energy_values.min(), max=energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)
                
                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                
                # Export Gaussian fit
                np.savetxt(f"gaussian_fit_k_{actual_k:.2f}.dat", np.column_stack((energy_values, fit)), header="Energy (eV)\tFitted Intensity (arb. units)")
        
        print("Data export completed.")


    interactive_plot = interactive(
        plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),
        use_canny=Checkbox(value=False, description='Use Canny Filter'),
        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),
        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),
        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),
        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min (Å⁻¹)', continuous_update=True),
        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max (Å⁻¹)', continuous_update=True),
        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type')
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)
    export_button = Button(description="Export Data")
    export_button.on_click(export_data)

    output = VBox([interactive_plot, HBox([save_button, export_button])])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import GaussianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
from ipywidgets import ToggleButtons

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, fit_type):
        plot_data = all_plots[scan_index - 1]
        ax.clear()

        # Get the full data
        data_to_plot = plot_data['data_values'].copy()
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            # Ensure the averaging kernel is 2D and has odd dimensions
            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)

            # Define the range for averaging
            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]
            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]

            # Apply averaging only to the specified range
            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]
            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')
            
            # Replace the averaged part in the original data
            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data

        k_indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)
        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]

            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            if not use_canny:
                kdc = kdc / np.max(kdc)

            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')
                ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                if fit_type == 'Maxima':
                    peaks, _ = find_peaks(kdc)
                    peak_heights = kdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                    largest_peaks = peaks[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))
                    for j, peak in enumerate(largest_peaks):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)
                elif fit_type == 'Minima':
                    valleys, _ = find_peaks(-kdc)
                    valley_depths = np.max(kdc) - kdc[valleys]
                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]
                    largest_valleys = valleys[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(kdc))
                    for j, valley in enumerate(largest_valleys):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=energy_values[valley], min=energy_values.min(), max=energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset

                # Extract sigma values and their uncertainties for label
                sigmas = []
                sigma_errors = []
                for j in range(num_peaks):
                    sigma = abs(result.params[f'g{j+1}_sigma'].value)
                    sigma_error = result.params[f'g{j+1}_sigma'].stderr
                    sigmas.append(sigma)
                    sigma_errors.append(sigma_error)

                sigma_label = ', '.join([fr'$\sigma_{j+1}$={sigma:.3f} $\pm$ {error:.3f}' for j, (sigma, error) in enumerate(zip(sigmas, sigma_errors))])
                ax.plot(energy_values, offset_fit, '--', label=fr'Fit $k_\parallel$={actual_k:.2f}, {sigma_label}', color=f'C{i}')

                if fit_type == 'Maxima':
                    fit_peaks, _ = find_peaks(fit)
                    ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                    for j, peak in enumerate(fit_peaks):
                        peak_energy = energy_values[peak]
                        peak_intensity = offset_fit[peak]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_energy = peak_energy - sigma
                        right_sigma_energy = peak_energy + sigma
                        left_sigma_intensity = np.interp(left_sigma_energy, energy_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_energy, energy_values, offset_fit)
                        ax.plot(left_sigma_energy, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_energy, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_energy, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_energy, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')
                elif fit_type == 'Minima':
                    fit_valleys, _ = find_peaks(-fit)
                    ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)
                    for j, valley in enumerate(fit_valleys):
                        valley_energy = energy_values[valley]
                        valley_intensity = offset_fit[valley]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_energy = valley_energy - sigma
                        right_sigma_energy = valley_energy + sigma
                        left_sigma_intensity = np.interp(left_sigma_energy, energy_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_energy, energy_values, offset_fit)
                        ax.plot(left_sigma_energy, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_energy, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_energy, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_energy, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')

        ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)
        plt.tight_layout()
        fig.canvas.draw_idle()
        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'fit_type': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def export_data(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'fit_type': interactive_plot.children[16].value
        }
        
        plot_data = all_plots[current_values['scan_index'] - 1]
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, current_values['n'], dtype=int)
        k_indices = valid_indices[k_indices]
        
        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']
            valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]
            
            if current_values['enable_averaging']:
                # Ensure the averaging kernel is 1D and has an odd size
                kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)
                averaging_kernel = np.ones(kernel_size) / kernel_size
                kdc = convolve(kdc, averaging_kernel, mode='reflect')
            
            # Export averaged data
            np.savetxt(f"averaged_data_k_{actual_k:.2f}.dat", np.column_stack((energy_values, kdc)), header="Energy (eV)\tIntensity (arb. units)")
            
            if current_values['show_fit']:
                # Perform fitting
                if current_values['fit_type'] == 'Maxima':
                    peaks, _ = find_peaks(kdc)
                    peak_heights = kdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-current_values['num_peaks']:]
                    largest_peaks = peaks[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))
                    for j, peak in enumerate(largest_peaks):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)
                elif current_values['fit_type'] == 'Minima':
                    valleys, _ = find_peaks(-kdc)
                    valley_depths = np.max(kdc) - kdc[valleys]
                    sorted_indices = np.argsort(valley_depths)[-current_values['num_peaks']:]
                    largest_valleys = valleys[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(kdc))
                    for j, valley in enumerate(largest_valleys):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=energy_values[valley], min=energy_values.min(), max=energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)
                
                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                
                # Export Gaussian fit
                np.savetxt(f"gaussian_fit_k_{actual_k:.2f}.dat", np.column_stack((energy_values, fit)), header="Energy (eV)\tFitted Intensity (arb. units)")
        
        print("Data export completed.")


    interactive_plot = interactive(
        plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),
        use_canny=Checkbox(value=False, description='Use Canny Filter'),
        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),
        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),
        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),
        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min (Å⁻¹)', continuous_update=True),
        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max (Å⁻¹)', continuous_update=True),
        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type')
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)
    export_button = Button(description="Export Data")
    export_button.on_click(export_data)

    output = VBox([interactive_plot, HBox([save_button, export_button])])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, ToggleButtons, FloatText
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import GaussianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve
from ipywidgets import ToggleButtons, jslink

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def edc_plot(data_files, work_function):
    # Constants
    hbar = 1.054571817e-34  # J*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']
        
        # Calculate kinetic energy and momentum
        E_b = (work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
        k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10
        
        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, 
                 k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, 
                 enable_averaging, averaging_kernel_size, avg_e_min, avg_e_max, avg_k_min, avg_k_max, 
                 fit_type, fit_k_min, fit_k_max, fit_e_min, fit_e_max):
        plot_data = all_plots[scan_index - 1]
        ax.clear()

        # Get the full data
        data_to_plot = plot_data['data_values'].copy()
        k_parallel = plot_data['k_parallel'][0]
        energy_values = plot_data['energy_values']

        # Apply filters only to the selected range
        valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        data_to_plot = data_to_plot[np.ix_(valid_e_indices, valid_k_indices)]
        k_parallel = k_parallel[valid_k_indices]
        energy_values = energy_values[valid_e_indices]

        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            # Ensure the averaging kernel is 2D and has odd dimensions
            kernel_size = max(3, averaging_kernel_size // 2 * 2 + 1)
            averaging_kernel = np.ones((kernel_size, kernel_size)) / (kernel_size ** 2)
            
            # Define the range for averaging
            avg_k_indices = np.where((k_parallel >= avg_k_min) & (k_parallel <= avg_k_max))[0]
            avg_e_indices = np.where((energy_values >= avg_e_min) & (energy_values <= avg_e_max))[0]
            
            # Apply averaging only to the specified range
            data_to_average = data_to_plot[np.ix_(avg_e_indices, avg_k_indices)]
            averaged_data = convolve(data_to_average, averaging_kernel, mode='reflect')
            
            # Replace the averaged part in the original data
            data_to_plot[np.ix_(avg_e_indices, avg_k_indices)] = averaged_data

        k_indices = np.linspace(0, len(valid_k_indices) - 1, n, dtype=int)
        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            if not use_canny:
                kdc = kdc / np.max(kdc)
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')
                ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Update the fitting range based on the new sliders
                valid_fit_k_indices = np.where((k_parallel >= fit_k_min) & (k_parallel <= fit_k_max))[0]
                valid_fit_e_indices = np.where((energy_values >= fit_e_min) & (energy_values <= fit_e_max))[0]
                
                # Perform fitting only on the specified range
                fit_data = data_to_plot[np.ix_(valid_fit_e_indices, valid_fit_k_indices)]
                fit_energy_values = energy_values[valid_fit_e_indices]
                
                if fit_type == 'Maxima':
                    peaks, _ = find_peaks(fit_data[:, k_index])
                    peak_heights = fit_data[peaks, k_index]
                    sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                    largest_peaks = peaks[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(fit_data[:, k_index]))
                    for j, peak in enumerate(largest_peaks):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_energy_values[peak], min=fit_energy_values.min(), max=fit_energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)
                elif fit_type == 'Minima':
                    valleys, _ = find_peaks(-fit_data[:, k_index])
                    valley_depths = np.max(fit_data[:, k_index]) - fit_data[valleys, k_index]
                    sorted_indices = np.argsort(valley_depths)[-num_peaks:]
                    largest_valleys = valleys[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(fit_data[:, k_index]))
                    for j, valley in enumerate(largest_valleys):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=fit_energy_values[valley], min=fit_energy_values.min(), max=fit_energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(fit_data[:, k_index], params, x=fit_energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset

                # Extract sigma values and their uncertainties for label
                sigmas = []
                sigma_errors = []
                for j in range(num_peaks):
                    sigma = abs(result.params[f'g{j+1}_sigma'].value)
                    sigma_error = result.params[f'g{j+1}_sigma'].stderr
                    sigmas.append(sigma)
                    sigma_errors.append(sigma_error)

                sigma_label = ', '.join([fr'$\sigma_{j+1}$={sigma:.3f} $\pm$ {error:.3f}' for j, (sigma, error) in enumerate(zip(sigmas, sigma_errors))])
                ax.plot(fit_energy_values, offset_fit, '--', label=fr'Fit $k_\parallel$={actual_k:.2f}, {sigma_label}', color=f'C{i}')

                if fit_type == 'Maxima':
                    fit_peaks, _ = find_peaks(fit)
                    ax.plot(fit_energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                    for j, peak in enumerate(fit_peaks):
                        peak_energy = fit_energy_values[peak]
                        peak_intensity = offset_fit[peak]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_energy = peak_energy - sigma
                        right_sigma_energy = peak_energy + sigma
                        left_sigma_intensity = np.interp(left_sigma_energy, fit_energy_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_energy, fit_energy_values, offset_fit)
                        ax.plot(left_sigma_energy, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_energy, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_energy, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_energy, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')
                elif fit_type == 'Minima':
                    fit_valleys, _ = find_peaks(-fit)
                    ax.plot(fit_energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)
                    for j, valley in enumerate(fit_valleys):
                        valley_energy = fit_energy_values[valley]
                        valley_intensity = offset_fit[valley]
                        sigma = sigmas[j]
                        sigma_error = sigma_errors[j]
                        left_sigma_energy = valley_energy - sigma
                        right_sigma_energy = valley_energy + sigma
                        left_sigma_intensity = np.interp(left_sigma_energy, fit_energy_values, offset_fit)
                        right_sigma_intensity = np.interp(right_sigma_energy, fit_energy_values, offset_fit)
                        ax.plot(left_sigma_energy, left_sigma_intensity, 'yo', markersize=4)
                        ax.plot(right_sigma_energy, right_sigma_intensity, 'yo', markersize=4)
                        ax.annotate(fr'$-\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (left_sigma_energy, left_sigma_intensity), xytext=(5, -15), textcoords='offset points', fontsize=8, color='black')
                        ax.annotate(fr'$+\sigma_{j+1}$={sigma:.3f}$\pm${sigma_error:.3f}', (right_sigma_energy, right_sigma_intensity), xytext=(5, 5), textcoords='offset points', fontsize=8, color='black')

        ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)
        ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)
        plt.tight_layout()
        fig.canvas.draw_idle()
        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'avg_e_min': interactive_plot.children[16].value,
            'avg_e_max': interactive_plot.children[17].value,
            'avg_k_min': interactive_plot.children[18].value,
            'avg_k_max': interactive_plot.children[19].value,
            'fit_type': interactive_plot.children[20].value,
            'fit_k_min': interactive_plot.children[21].value,
            'fit_k_max': interactive_plot.children[22].value,
            'fit_e_min': interactive_plot.children[23].value,
            'fit_e_max': interactive_plot.children[24].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def export_data(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'use_canny': interactive_plot.children[10].value,
            'sigma': interactive_plot.children[11].value,
            'low_threshold': interactive_plot.children[12].value,
            'high_threshold': interactive_plot.children[13].value,
            'enable_averaging': interactive_plot.children[14].value,
            'averaging_kernel_size': interactive_plot.children[15].value,
            'avg_e_min': interactive_plot.children[16].value,
            'avg_e_max': interactive_plot.children[17].value,
            'avg_k_min': interactive_plot.children[18].value,
            'avg_k_max': interactive_plot.children[19].value,
            'fit_type': interactive_plot.children[20].value,
            'fit_k_min': interactive_plot.children[21].value,
            'fit_k_max': interactive_plot.children[22].value,
            'fit_e_min': interactive_plot.children[23].value,
            'fit_e_max': interactive_plot.children[24].value
        }
        
        plot_data = all_plots[current_values['scan_index'] - 1]
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= current_values['k_min']) & (k_parallel <= current_values['k_max']))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, current_values['n'], dtype=int)
        k_indices = valid_indices[k_indices]
        
        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = plot_data['data_values'][:, k_index]
            energy_values = plot_data['energy_values']
            valid_e_indices = np.where((energy_values >= current_values['e_min']) & (energy_values <= current_values['e_max']))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]
            
            if current_values['enable_averaging']:
                kernel_size = max(3, current_values['averaging_kernel_size'] // 2 * 2 + 1)
                averaging_kernel = np.ones(kernel_size) / kernel_size
                kdc = convolve(kdc, averaging_kernel, mode='reflect')
            
            np.savetxt(f"averaged_data_k_{actual_k:.2f}.dat", np.column_stack((energy_values, kdc)), header="Energy (eV)\tIntensity (arb. units)")
            
            if current_values['show_fit']:
                if current_values['fit_type'] == 'Maxima':
                    peaks, _ = find_peaks(kdc)
                    peak_heights = kdc[peaks]
                    sorted_indices = np.argsort(peak_heights)[-current_values['num_peaks']:]
                    largest_peaks = peaks[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))
                    for j, peak in enumerate(largest_peaks):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)
                elif current_values['fit_type'] == 'Minima':
                    valleys, _ = find_peaks(-kdc)
                    valley_depths = np.max(kdc) - kdc[valleys]
                    sorted_indices = np.argsort(valley_depths)[-current_values['num_peaks']:]
                    largest_valleys = valleys[sorted_indices]
                    model = LinearModel(prefix='bkg_')
                    params = model.make_params(bkg_slope=0, bkg_intercept=np.max(kdc))
                    for j, valley in enumerate(largest_valleys):
                        gaussian = GaussianModel(prefix=f'g{j+1}_')
                        model += gaussian
                        params.update(gaussian.make_params())
                        params[f'g{j+1}_center'].set(value=energy_values[valley], min=energy_values.min(), max=energy_values.max())
                        params[f'g{j+1}_amplitude'].set(value=-valley_depths[sorted_indices[j]])
                        params[f'g{j+1}_sigma'].set(value=0.1, min=0)
                
                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                np.savetxt(f"gaussian_fit_k_{actual_k:.2f}.dat", np.column_stack((energy_values, fit)), header="Energy (eV)\tFitted Intensity (arb. units)")
        
        print("Data export completed.")

    interactive_plot = interactive(
        plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='E_max (eV)', continuous_update=True),
        use_canny=Checkbox(value=False, description='Use Canny Filter'),
        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=2, min=1, max=20, step=1, description='Averaging Kernel Size', continuous_update=False),
        avg_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_min (eV)', continuous_update=True),
        avg_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Average E_max (eV)', continuous_update=True),
        avg_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_min (Å⁻¹)', continuous_update=True),
        avg_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Average k_max (Å⁻¹)', continuous_update=True),
        fit_type=ToggleButtons(options=['Maxima', 'Minima'], description='Fit Type'),
        fit_k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Fit k_min (Å⁻¹)', continuous_update=True),
        fit_k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/1000, description='Fit k_max (Å⁻¹)', continuous_update=True),
        fit_e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_min (eV)', continuous_update=True),
        fit_e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/1000, description='Fit E_max (eV)', continuous_update=True)
    )

    # Create text boxes for the fit range values
    fit_k_min_text = widgets.FloatText(value=global_k_min, description='Fit k_min (Å⁻¹):', style={'description_width': 'initial'})
    fit_k_max_text = widgets.FloatText(value=global_k_max, description='Fit k_max (Å⁻¹):', style={'description_width': 'initial'})
    fit_e_min_text = widgets.FloatText(value=global_e_min, description='Fit E_min (eV):', style={'description_width': 'initial'})
    fit_e_max_text = widgets.FloatText(value=global_e_max, description='Fit E_max (eV):', style={'description_width': 'initial'})

    # Link the sliders and text boxes
    widgets.jslink((interactive_plot.children[-4], 'value'), (fit_k_min_text, 'value'))
    widgets.jslink((interactive_plot.children[-3], 'value'), (fit_k_max_text, 'value'))
    widgets.jslink((interactive_plot.children[-2], 'value'), (fit_e_min_text, 'value'))
    widgets.jslink((interactive_plot.children[-1], 'value'), (fit_e_max_text, 'value'))

    # Create a layout for the new fit range controls
    fit_range_controls = widgets.VBox([
        widgets.HBox([fit_k_min_text, fit_k_max_text]),
        widgets.HBox([fit_e_min_text, fit_e_max_text])
    ])

    save_button = widgets.Button(description="Save Plot")
    save_button.on_click(save_plot)
    export_button = widgets.Button(description="Export Data")
    export_button.on_click(export_data)

    output = widgets.VBox([interactive_plot, fit_range_controls, widgets.HBox([save_button, export_button])])
    return output

    # Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
