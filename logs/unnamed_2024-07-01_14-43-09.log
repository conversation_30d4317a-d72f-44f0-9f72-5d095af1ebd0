# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['<PERSON>ja<PERSON><PERSON> Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, 
                 vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size,
                 enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Apply convolutions if enabled
        data_to_plot = plot_data['data_values'].copy()
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

        # Apply moving average if enabled
        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve2d(data_to_plot, averaging_kernel)

        # Calculate equally spaced energy indices within the specified range
        energy_values = plot_data['energy_values']
        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        e_indices = valid_indices[e_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, e_index in enumerate(e_indices):
            actual_e = energy_values[e_index]
            kdc = data_to_plot[e_index, :]
            k_parallel = plot_data['k_parallel'][0]

            # Filter k_parallel values within the specified range
            valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
            kdc = kdc[valid_k_indices]
            k_parallel = k_parallel[valid_k_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Apply vertical offset without normalization
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(k_parallel, offset_kdc, label=f'E = {actual_e:.2f} eV')

            # Draw x'd line at the "zero level" of each curve
            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=k_parallel[peak], min=k_parallel.min(), max=k_parallel.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=k_parallel)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset

                ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(k_parallel[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(k_parallel[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('k$_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'KDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(k_min, k_max)

        # Ensure the entire curve is visible, including negative values
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()
        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'vertical_enabled': interactive_plot.children[10].value,
            'horizontal_enabled': interactive_plot.children[11].value,
            'vertical_magnitude': interactive_plot.children[12].value,
            'horizontal_magnitude': interactive_plot.children[13].value,
            'kernel_size': interactive_plot.children[14].value,
            'enable_averaging': interactive_plot.children[15].value,
            'averaging_kernel_size': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"KDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of KDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=global_intensity_max*10, step=global_intensity_max/100, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show KDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size, enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Apply convolutions if enabled
        data_to_plot = plot_data['data_values'].copy()
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

        # Apply moving average if enabled
        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve2d(data_to_plot, averaging_kernel)

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Apply vertical offset without normalization
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(energy_values, offset_kdc, label=f'k_parallel = {actual_k:.2f} Å⁻¹')

            # Draw x'd line at the "zero level" of each curve
            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k_parallel = {actual_k:.2f} Å⁻¹', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)

        # Ensure the entire curve is visible, including negative values
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'vertical_enabled': interactive_plot.children[10].value,
            'horizontal_enabled': interactive_plot.children[11].value,
            'vertical_magnitude': interactive_plot.children[12].value,
            'horizontal_magnitude': interactive_plot.children[13].value,
            'kernel_size': interactive_plot.children[14].value,
            'enable_averaging': interactive_plot.children[15].value,
            'averaging_kernel_size': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=global_intensity_max*10, step=global_intensity_max/100, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size, enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Apply convolutions if enabled
        data_to_plot = plot_data['data_values'].copy()
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

        # Apply moving average if enabled
        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve2d(data_to_plot, averaging_kernel)

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Apply vertical offset without normalization
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(energy_values, offset_kdc, label=f'k_parallel = {actual_k:.2f}')

            # Draw x'd line at the "zero level" of each curve
            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k_parallel = {actual_k:.2f} Å⁻¹', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)

        # Ensure the entire curve is visible, including negative values
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'vertical_enabled': interactive_plot.children[10].value,
            'horizontal_enabled': interactive_plot.children[11].value,
            'vertical_magnitude': interactive_plot.children[12].value,
            'horizontal_magnitude': interactive_plot.children[13].value,
            'kernel_size': interactive_plot.children[14].value,
            'enable_averaging': interactive_plot.children[15].value,
            'averaging_kernel_size': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=global_intensity_max*10, step=global_intensity_max/100, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size, enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Apply convolutions if enabled
        data_to_plot = plot_data['data_values'].copy()
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

        # Apply moving average if enabled
        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve2d(data_to_plot, averaging_kernel)

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Apply vertical offset without normalization
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(energy_values, offset_kdc, label=f'k_parallel = {actual_k:.2f}')

            # Draw x'd line at the "zero level" of each curve
            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k_parallel = {actual_k:.2f} Å⁻¹', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)

        # Ensure the entire curve is visible, including negative values
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'vertical_enabled': interactive_plot.children[10].value,
            'horizontal_enabled': interactive_plot.children[11].value,
            'vertical_magnitude': interactive_plot.children[12].value,
            'horizontal_magnitude': interactive_plot.children[13].value,
            'kernel_size': interactive_plot.children[14].value,
            'enable_averaging': interactive_plot.children[15].value,
            'averaging_kernel_size': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=global_intensity_max*10, step=global_intensity_max/100, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size, enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Apply convolutions if enabled
        data_to_plot = plot_data['data_values'].copy()
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

        # Apply moving average if enabled
        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve2d(data_to_plot, averaging_kernel)

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Apply vertical offset without normalization
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(energy_values, offset_kdc, label=f'k_parallel = {actual_k:.2f}')

            # Draw x'd line at the "zero level" of each curve
            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k_parallel = {actual_k:.2f} Å⁻¹', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)

        # Ensure the entire curve is visible, including negative values
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'vertical_enabled': interactive_plot.children[10].value,
            'horizontal_enabled': interactive_plot.children[11].value,
            'vertical_magnitude': interactive_plot.children[12].value,
            'horizontal_magnitude': interactive_plot.children[13].value,
            'kernel_size': interactive_plot.children[14].value,
            'enable_averaging': interactive_plot.children[15].value,
            'averaging_kernel_size': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=global_intensity_max*10, step=global_intensity_max/100, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size, enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Apply convolutions if enabled
        data_to_plot = plot_data['data_values'].copy()
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

        # Apply moving average if enabled
        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve2d(data_to_plot, averaging_kernel)

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Apply vertical offset without normalization
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(energy_values, offset_kdc, label=f'k_parallel = {actual_k:.2f}')

            # Draw x'd line at the "zero level" of each curve
            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k_parallel = {actual_k:.2f}', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)

        # Ensure the entire curve is visible, including negative values
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'vertical_enabled': interactive_plot.children[10].value,
            'horizontal_enabled': interactive_plot.children[11].value,
            'vertical_magnitude': interactive_plot.children[12].value,
            'horizontal_magnitude': interactive_plot.children[13].value,
            'kernel_size': interactive_plot.children[14].value,
            'enable_averaging': interactive_plot.children[15].value,
            'averaging_kernel_size': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=global_intensity_max*10, step=global_intensity_max/100, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size, enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Apply convolutions if enabled
        data_to_plot = plot_data['data_values'].copy()
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

        # Apply moving average if enabled
        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve2d(data_to_plot, averaging_kernel)

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Normalize the curve if edge detection is off
            if not (vertical_enabled or horizontal_enabled):
                kdc = kdc / np.max(kdc)

            # Apply vertical offset
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(energy_values, offset_kdc, label=f'k_parallel = {actual_k:.2f} Å⁻¹')

            # Draw x'd line at the "zero level" of each curve
            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k_parallel = {actual_k:.2f} Å⁻¹', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)

        # Ensure the entire curve is visible, including negative values
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'vertical_enabled': interactive_plot.children[10].value,
            'horizontal_enabled': interactive_plot.children[11].value,
            'vertical_magnitude': interactive_plot.children[12].value,
            'horizontal_magnitude': interactive_plot.children[13].value,
            'kernel_size': interactive_plot.children[14].value,
            'enable_averaging': interactive_plot.children[15].value,
            'averaging_kernel_size': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=global_intensity_max*10, step=global_intensity_max/100, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.0": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox\nfrom matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm\nfrom IPython.display import display, clear_output\nimport tkinter as tk\nfrom tkinter import filedialog\nimport warnings\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nfrom matplotlib import MatplotlibDeprecationWarning\nfrom lmfit.models import LorentzianModel, LinearModel\nfrom scipy.signal import find_peaks\n\n%matplotlib widget\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",\n    \"axes.labelcolor\": \"black\",\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()\n    return [os.path.join(folder_path, f) for f in data_files]\n\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef convolve2d(image, kernel):\n    output = np.zeros_like(image)\n    pad = kernel.shape[0] // 2\n    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')\n    for i in range(image.shape[0]):\n        for j in range(image.shape[1]):\n            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)\n    return output\n\ndef edc_plot(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    global_k_min = float('inf')\n    global_k_max = float('-inf')\n    global_e_min = float('inf')\n    global_e_max = float('-inf')\n    global_intensity_max = float('-inf')\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        # Update global ranges\n        global_k_min = min(global_k_min, np.min(k_parallel))\n        global_k_max = max(global_k_max, np.max(k_parallel))\n        global_e_min = min(global_e_min, np.min(energy_values))\n        global_e_max = max(global_e_max, np.max(energy_values))\n        global_intensity_max = max(global_intensity_max, np.max(data.values))\n\n    global_k_range = global_k_max - global_k_min\n    global_e_range = global_e_max - global_e_min\n\n    fig, ax = plt.subplots(figsize=(10, 8))\n\n    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size, enable_averaging, averaging_kernel_size):\n        plot_data = all_plots[scan_index - 1]\n\n        # Clear the current figure\n        ax.clear()\n\n        # Apply convolutions if enabled\n        data_to_plot = plot_data['data_values'].copy()\n        if vertical_enabled or horizontal_enabled:\n            if vertical_enabled:\n                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude\n                if kernel_size > 3:\n                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)\n                data_to_plot = convolve2d(data_to_plot, vertical_kernel)\n            if horizontal_enabled:\n                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude\n                if kernel_size > 3:\n                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)\n                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)\n\n        # Apply moving average if enabled\n        if enable_averaging:\n            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)\n            data_to_plot = convolve2d(data_to_plot, averaging_kernel)\n\n        # Calculate equally spaced k_parallel indices within the specified range\n        k_parallel = plot_data['k_parallel'][0]\n        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]\n        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)\n        k_indices = valid_indices[k_indices]\n\n        max_intensity = float('-inf')\n        min_intensity = float('inf')\n\n        for i, k_index in enumerate(k_indices):\n            actual_k = k_parallel[k_index]\n            kdc = data_to_plot[:, k_index]\n            energy_values = plot_data['energy_values']\n\n            # Filter energy values within the specified range\n            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]\n            kdc = kdc[valid_e_indices]\n            energy_values = energy_values[valid_e_indices]\n\n            # Convert to numpy arrays if they are xarray.DataArray\n            if isinstance(kdc, xr.DataArray):\n                kdc = kdc.values\n            if isinstance(energy_values, xr.DataArray):\n                energy_values = energy_values.values\n\n            # Normalize the curve if edge detection is off\n            if not (vertical_enabled or horizontal_enabled):\n                kdc = kdc / np.max(kdc)\n\n            # Apply vertical offset\n            offset_kdc = kdc + i * vertical_offset\n            max_intensity = max(max_intensity, np.max(offset_kdc))\n            min_intensity = min(min_intensity, np.min(offset_kdc))\n\n            if show_edc:\n                ax.plot(energy_values, offset_kdc, label=f'k_parallel = {actual_k:.2f} Å⁻¹')\n\n            # Draw x'd line at the \"zero level\" of each curve\n            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)\n\n            if show_fit:\n                # Fit KDC with multiple Lorentzian peaks + Linear background\n                peaks, _ = find_peaks(kdc)\n                peak_heights = kdc[peaks]\n                sorted_indices = np.argsort(peak_heights)[-num_peaks:]\n                largest_peaks = peaks[sorted_indices]\n\n                model = LinearModel(prefix='bkg_')\n                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))\n\n                for j, peak in enumerate(largest_peaks):\n                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')\n                    model += lorentzian\n                    params.update(lorentzian.make_params())\n                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())\n                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)\n\n                result = model.fit(kdc, params, x=energy_values)\n                fit = result.best_fit\n                offset_fit = fit + i * vertical_offset\n                ax.plot(energy_values, offset_fit, '--', label=f'Fit k_parallel = {actual_k:.2f} \\a', color=f'C{i}')\n\n                # Find local maxima and minima on the fitted curve\n                fit_peaks, _ = find_peaks(fit)\n                fit_valleys, _ = find_peaks(-fit)\n\n                # Plot local maxima and minima\n                ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)\n                ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)\n\n        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')\n        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')\n        ax.set_title(f'EDCs - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.legend()\n        ax.tick_params(axis='both', which='major', labelsize=10)\n\n        # Set axis limits based on sliders\n        ax.set_xlim(e_min, e_max)\n\n        # Ensure the entire curve is visible, including negative values\n        y_range = max_intensity - min_intensity\n        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)\n\n        plt.tight_layout()\n        fig.canvas.draw_idle()\n\n        return max_intensity - min_intensity\n\n    def save_plot(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_edc': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'vertical_enabled': interactive_plot.children[10].value,\n            'horizontal_enabled': interactive_plot.children[11].value,\n            'vertical_magnitude': interactive_plot.children[12].value,\n            'horizontal_magnitude': interactive_plot.children[13].value,\n            'kernel_size': interactive_plot.children[14].value,\n            'enable_averaging': interactive_plot.children[15].value,\n            'averaging_kernel_size': interactive_plot.children[16].value\n        }\n        plot_edc(**current_values)\n        filename = f\"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        print(f\"Plot saved as {filename}\")\n\n    interactive_plot = interactive(plot_edc,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),\n        vertical_offset=FloatSlider(value=0.5, min=0.0, max=global_intensity_max*10, step=global_intensity_max/100, description='Vertical Offset', continuous_update=True),\n        show_edc=Checkbox(value=True, description='Show EDCs'),\n        show_fit=Checkbox(value=False, description='Show Fits'),\n        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),\n        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),\n        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),\n        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),\n        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),\n        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),\n        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),\n        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),\n        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),\n        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),\n        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),\n        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)\n    )\n\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    output = VBox([interactive_plot, save_button])\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)\n    interactive_plot_with_save = edc_plot(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")", 7771)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.0.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.0.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.1": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets \\aa", 100, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.1.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.1.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.2": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets \\annuity", 105, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.2.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.2.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size, enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Apply convolutions if enabled
        data_to_plot = plot_data['data_values'].copy()
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

        # Apply moving average if enabled
        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve2d(data_to_plot, averaging_kernel)

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Normalize the curve if edge detection is off
            if not (vertical_enabled or horizontal_enabled):
                kdc = kdc / np.max(kdc)

            # Apply vertical offset
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(energy_values, offset_kdc, label=f'k_parallel = {actual_k:.2f} $\angstrom^{-1}$')

            # Draw x'd line at the "zero level" of each curve
            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k_parallel = {actual_k:.2f} $\angstrom^{-1}$', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)

        # Ensure the entire curve is visible, including negative values
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'vertical_enabled': interactive_plot.children[10].value,
            'horizontal_enabled': interactive_plot.children[11].value,
            'vertical_magnitude': interactive_plot.children[12].value,
            'horizontal_magnitude': interactive_plot.children[13].value,
            'kernel_size': interactive_plot.children[14].value,
            'enable_averaging': interactive_plot.children[15].value,
            'averaging_kernel_size': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=global_intensity_max*10, step=global_intensity_max/100, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size, enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Apply convolutions if enabled
        data_to_plot = plot_data['data_values'].copy()
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

        # Apply moving average if enabled
        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve2d(data_to_plot, averaging_kernel)

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Normalize the curve if edge detection is off
            if not (vertical_enabled or horizontal_enabled):
                kdc = kdc / np.max(kdc)

            # Apply vertical offset
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(energy_values, offset_kdc, label=f'k_parallel = {actual_k:.2f}')

            # Draw x'd line at the "zero level" of each curve
            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k_parallel = {actual_k:.2f}', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)

        # Ensure the entire curve is visible, including negative values
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'vertical_enabled': interactive_plot.children[10].value,
            'horizontal_enabled': interactive_plot.children[11].value,
            'vertical_magnitude': interactive_plot.children[12].value,
            'horizontal_magnitude': interactive_plot.children[13].value,
            'kernel_size': interactive_plot.children[14].value,
            'enable_averaging': interactive_plot.children[15].value,
            'averaging_kernel_size': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=global_intensity_max*10, step=global_intensity_max/100, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.3": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox\nfrom matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm\nfrom IPython.display import display, clear_output\nimport tkinter as tk\nfrom tkinter import filedialog\nimport warnings\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nfrom matplotlib import MatplotlibDeprecationWarning\nfrom lmfit.models import LorentzianModel, LinearModel\nfrom scipy.signal import find_peaks\n\n%matplotlib widget\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",\n    \"axes.labelcolor\": \"black\",\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()\n    return [os.path.join(folder_path, f) for f in data_files]\n\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef convolve2d(image, kernel):\n    output = np.zeros_like(image)\n    pad = kernel.shape[0] // 2\n    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')\n    for i in range(image.shape[0]):\n        for j in range(image.shape[1]):\n            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)\n    return output\n\ndef edc_plot(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    global_k_min = float('inf')\n    global_k_max = float('-inf')\n    global_e_min = float('inf')\n    global_e_max = float('-inf')\n    global_intensity_max = float('-inf')\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        # Update global ranges\n        global_k_min = min(global_k_min, np.min(k_parallel))\n        global_k_max = max(global_k_max, np.max(k_parallel))\n        global_e_min = min(global_e_min, np.min(energy_values))\n        global_e_max = max(global_e_max, np.max(energy_values))\n        global_intensity_max = max(global_intensity_max, np.max(data.values))\n\n    global_k_range = global_k_max - global_k_min\n    global_e_range = global_e_max - global_e_min\n\n    fig, ax = plt.subplots(figsize=(10, 8))\n\n    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size, enable_averaging, averaging_kernel_size):\n        plot_data = all_plots[scan_index - 1]\n\n        # Clear the current figure\n        ax.clear()\n\n        # Apply convolutions if enabled\n        data_to_plot = plot_data['data_values'].copy()\n        if vertical_enabled or horizontal_enabled:\n            if vertical_enabled:\n                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude\n                if kernel_size > 3:\n                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)\n                data_to_plot = convolve2d(data_to_plot, vertical_kernel)\n            if horizontal_enabled:\n                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude\n                if kernel_size > 3:\n                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)\n                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)\n\n        # Apply moving average if enabled\n        if enable_averaging:\n            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)\n            data_to_plot = convolve2d(data_to_plot, averaging_kernel)\n\n        # Calculate equally spaced k_parallel indices within the specified range\n        k_parallel = plot_data['k_parallel'][0]\n        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]\n        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)\n        k_indices = valid_indices[k_indices]\n\n        max_intensity = float('-inf')\n        min_intensity = float('inf')\n\n        for i, k_index in enumerate(k_indices):\n            actual_k = k_parallel[k_index]\n            kdc = data_to_plot[:, k_index]\n            energy_values = plot_data['energy_values']\n\n            # Filter energy values within the specified range\n            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]\n            kdc = kdc[valid_e_indices]\n            energy_values = energy_values[valid_e_indices]\n\n            # Convert to numpy arrays if they are xarray.DataArray\n            if isinstance(kdc, xr.DataArray):\n                kdc = kdc.values\n            if isinstance(energy_values, xr.DataArray):\n                energy_values = energy_values.values\n\n            # Normalize the curve if edge detection is off\n            if not (vertical_enabled or horizontal_enabled):\n                kdc = kdc / np.max(kdc)\n\n            # Apply vertical offset\n            offset_kdc = kdc + i * vertical_offset\n            max_intensity = max(max_intensity, np.max(offset_kdc))\n            min_intensity = min(min_intensity, np.min(offset_kdc))\n\n            if show_edc:\n                ax.plot(energy_values, offset_kdc, label=f'k_parallel = {actual_k:.2f}')\n\n            # Draw x'd line at the \"zero level\" of each curve\n            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)\n\n            if show_fit:\n                # Fit KDC with multiple Lorentzian peaks + Linear background\n                peaks, _ = find_peaks(kdc)\n                peak_heights = kdc[peaks]\n                sorted_indices = np.argsort(peak_heights)[-num_peaks:]\n                largest_peaks = peaks[sorted_indices]\n\n                model = LinearModel(prefix='bkg_')\n                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))\n\n                for j, peak in enumerate(largest_peaks):\n                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')\n                    model += lorentzian\n                    params.update(lorentzian.make_params())\n                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())\n                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)\n\n                result = model.fit(kdc, params, x=energy_values)\n                fit = result.best_fit\n                offset_fit = fit + i * vertical_offset\n                ax.plot(energy_values, offset_fit, '--', label=f'Fit k_parallel = {actual_k:.2f}', color=f'C{i}')\n\n                # Find local maxima and minima on the fitted curve\n                fit_peaks, _ = find_peaks(fit)\n                fit_valleys, _ = find_peaks(-fit)\n\n                # Plot local maxima and minima\n                ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)\n                ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)\n\n        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')\n        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')\n        ax.set_title(f'EDCs - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.legend()\n        ax.tick_params(axis='both', which='major', labelsize=10)\n\n        # Set axis limits based on sliders\n        ax.set_xlim(e_min, e_max)\n\n        # Ensure the entire curve is visible, including negative values\n        y_range = max_intensity - min_intensity\n        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)\n\n        plt.tight_layout()\n        fig.canvas.draw_idle()\n\n        return max_intensity - min_intensity\n\n    def save_plot(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_edc': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'vertical_enabled': interactive_plot.children[10].value,\n            'horizontal_enabled': interactive_plot.children[11].value,\n            'vertical_magnitude': interactive_plot.children[12].value,\n            'horizontal_magnitude': interactive_plot.children[13].value,\n            'kernel_size': interactive_plot.children[14].value,\n            'enable_averaging': interactive_plot.children[15].value,\n            'averaging_kernel_size': interactive_plot.children[16].value\n        }\n        plot_edc(**current_values)\n        filename = f\"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        print(f\"Plot saved as {filename}\")\n\n    interactive_plot = interactive(plot_edc,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),\n        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=g, description='Vertical Offset', continuous_update=True),\n        show_edc=Checkbox(value=True, description='Show EDCs'),\n        show_fit=Checkbox(value=False, description='Show Fits'),\n        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),\n        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),\n        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),\n        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),\n        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),\n        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),\n        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),\n        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),\n        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),\n        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),\n        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),\n        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)\n    )\n\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    output = VBox([interactive_plot, save_button])\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)\n    interactive_plot_with_save = edc_plot(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")", 10671)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.3.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.3.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.4": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox\nfrom matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm\nfrom IPython.display import display, clear_output\nimport tkinter as tk\nfrom tkinter import filedialog\nimport warnings\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nfrom matplotlib import MatplotlibDeprecationWarning\nfrom lmfit.models import LorentzianModel, LinearModel\nfrom scipy.signal import find_peaks\n\n%matplotlib widget\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",\n    \"axes.labelcolor\": \"black\",\n})\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()\n    return [os.path.join(folder_path, f) for f in data_files]\n\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef convolve2d(image, kernel):\n    output = np.zeros_like(image)\n    pad = kernel.shape[0] // 2\n    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')\n    for i in range(image.shape[0]):\n        for j in range(image.shape[1]):\n            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)\n    return output\n\ndef edc_plot(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    global_k_min = float('inf')\n    global_k_max = float('-inf')\n    global_e_min = float('inf')\n    global_e_max = float('-inf')\n    global_intensity_max = float('-inf')\n\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']\n\n        # Calculate kinetic energy and momentum\n        E_b = work_function + np.abs(data.eV) - E_photon\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n        # Get the energy values\n        energy_values = -data.eV.values\n\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': os.path.basename(file_path)\n        })\n\n        # Update global ranges\n        global_k_min = min(global_k_min, np.min(k_parallel))\n        global_k_max = max(global_k_max, np.max(k_parallel))\n        global_e_min = min(global_e_min, np.min(energy_values))\n        global_e_max = max(global_e_max, np.max(energy_values))\n        global_intensity_max = max(global_intensity_max, np.max(data.values))\n\n    global_k_range = global_k_max - global_k_min\n    global_e_range = global_e_max - global_e_min\n\n    fig, ax = plt.subplots(figsize=(10, 8))\n\n    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size, enable_averaging, averaging_kernel_size):\n        plot_data = all_plots[scan_index - 1]\n\n        # Clear the current figure\n        ax.clear()\n\n        # Apply convolutions if enabled\n        data_to_plot = plot_data['data_values'].copy()\n        if vertical_enabled or horizontal_enabled:\n            if vertical_enabled:\n                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude\n                if kernel_size > 3:\n                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)\n                data_to_plot = convolve2d(data_to_plot, vertical_kernel)\n            if horizontal_enabled:\n                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude\n                if kernel_size > 3:\n                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)\n                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)\n\n        # Apply moving average if enabled\n        if enable_averaging:\n            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)\n            data_to_plot = convolve2d(data_to_plot, averaging_kernel)\n\n        # Calculate equally spaced k_parallel indices within the specified range\n        k_parallel = plot_data['k_parallel'][0]\n        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]\n        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)\n        k_indices = valid_indices[k_indices]\n\n        max_intensity = float('-inf')\n        min_intensity = float('inf')\n\n        for i, k_index in enumerate(k_indices):\n            actual_k = k_parallel[k_index]\n            kdc = data_to_plot[:, k_index]\n            energy_values = plot_data['energy_values']\n\n            # Filter energy values within the specified range\n            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]\n            kdc = kdc[valid_e_indices]\n            energy_values = energy_values[valid_e_indices]\n\n            # Convert to numpy arrays if they are xarray.DataArray\n            if isinstance(kdc, xr.DataArray):\n                kdc = kdc.values\n            if isinstance(energy_values, xr.DataArray):\n                energy_values = energy_values.values\n\n            # Normalize the curve if edge detection is off\n            if not (vertical_enabled or horizontal_enabled):\n                kdc = kdc / np.max(kdc)\n\n            # Apply vertical offset\n            offset_kdc = kdc + i * vertical_offset\n            max_intensity = max(max_intensity, np.max(offset_kdc))\n            min_intensity = min(min_intensity, np.min(offset_kdc))\n\n            if show_edc:\n                ax.plot(energy_values, offset_kdc, label=f'k_parallel = {actual_k:.2f}')\n\n            # Draw x'd line at the \"zero level\" of each curve\n            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)\n\n            if show_fit:\n                # Fit KDC with multiple Lorentzian peaks + Linear background\n                peaks, _ = find_peaks(kdc)\n                peak_heights = kdc[peaks]\n                sorted_indices = np.argsort(peak_heights)[-num_peaks:]\n                largest_peaks = peaks[sorted_indices]\n\n                model = LinearModel(prefix='bkg_')\n                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))\n\n                for j, peak in enumerate(largest_peaks):\n                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')\n                    model += lorentzian\n                    params.update(lorentzian.make_params())\n                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())\n                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)\n                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)\n\n                result = model.fit(kdc, params, x=energy_values)\n                fit = result.best_fit\n                offset_fit = fit + i * vertical_offset\n                ax.plot(energy_values, offset_fit, '--', label=f'Fit k_parallel = {actual_k:.2f}', color=f'C{i}')\n\n                # Find local maxima and minima on the fitted curve\n                fit_peaks, _ = find_peaks(fit)\n                fit_valleys, _ = find_peaks(-fit)\n\n                # Plot local maxima and minima\n                ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)\n                ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)\n\n        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')\n        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')\n        ax.set_title(f'EDCs - File: {plot_data[\"file_name\"]}', fontsize=14, fontweight='bold')\n        ax.legend()\n        ax.tick_params(axis='both', which='major', labelsize=10)\n\n        # Set axis limits based on sliders\n        ax.set_xlim(e_min, e_max)\n\n        # Ensure the entire curve is visible, including negative values\n        y_range = max_intensity - min_intensity\n        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)\n\n        plt.tight_layout()\n        fig.canvas.draw_idle()\n\n        return max_intensity - min_intensity\n\n    def save_plot(b):\n        current_values = {\n            'scan_index': interactive_plot.children[0].value,\n            'n': interactive_plot.children[1].value,\n            'vertical_offset': interactive_plot.children[2].value,\n            'show_edc': interactive_plot.children[3].value,\n            'show_fit': interactive_plot.children[4].value,\n            'num_peaks': interactive_plot.children[5].value,\n            'k_min': interactive_plot.children[6].value,\n            'k_max': interactive_plot.children[7].value,\n            'e_min': interactive_plot.children[8].value,\n            'e_max': interactive_plot.children[9].value,\n            'vertical_enabled': interactive_plot.children[10].value,\n            'horizontal_enabled': interactive_plot.children[11].value,\n            'vertical_magnitude': interactive_plot.children[12].value,\n            'horizontal_magnitude': interactive_plot.children[13].value,\n            'kernel_size': interactive_plot.children[14].value,\n            'enable_averaging': interactive_plot.children[15].value,\n            'averaging_kernel_size': interactive_plot.children[16].value\n        }\n        plot_edc(**current_values)\n        filename = f\"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png\"\n        fig.savefig(filename, dpi=2000, bbox_inches='tight')\n        print(f\"Plot saved as {filename}\")\n\n    interactive_plot = interactive(plot_edc,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),\n        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),\n        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0., description='Vertical Offset', continuous_update=True),\n        show_edc=Checkbox(value=True, description='Show EDCs'),\n        show_fit=Checkbox(value=False, description='Show Fits'),\n        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),\n        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),\n        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),\n        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),\n        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),\n        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),\n        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),\n        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),\n        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),\n        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),\n        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),\n        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)\n    )\n\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(save_plot)\n\n    output = VBox([interactive_plot, save_button])\n    return output\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)\n    interactive_plot_with_save = edc_plot(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")", 10672)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.4.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.4.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size, enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Apply convolutions if enabled
        data_to_plot = plot_data['data_values'].copy()
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

        # Apply moving average if enabled
        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve2d(data_to_plot, averaging_kernel)

        # Calculate equally spaced k_parallel indices within the specified range
        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            # Filter energy values within the specified range
            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            # Normalize the curve if edge detection is off
            if not (vertical_enabled or horizontal_enabled):
                kdc = kdc / np.max(kdc)

            # Apply vertical offset
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(energy_values, offset_kdc, label=f'k_parallel = {actual_k:.2f}')

            # Draw x'd line at the "zero level" of each curve
            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                ax.plot(energy_values, offset_fit, '--', label=f'Fit k_parallel = {actual_k:.2f}', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('Energy (eV)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(e_min, e_max)

        # Ensure the entire curve is visible, including negative values
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()

        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'vertical_enabled': interactive_plot.children[10].value,
            'horizontal_enabled': interactive_plot.children[11].value,
            'vertical_magnitude': interactive_plot.children[12].value,
            'horizontal_magnitude': interactive_plot.children[13].value,
            'kernel_size': interactive_plot.children[14].value,
            'enable_averaging': interactive_plot.children[15].value,
            'averaging_kernel_size': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show EDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, 
                 vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size,
                 enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Apply convolutions if enabled
        data_to_plot = plot_data['data_values'].copy()
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

        # Apply moving average if enabled
        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve2d(data_to_plot, averaging_kernel)

        # Calculate equally spaced energy indices within the specified range
        energy_values = plot_data['energy_values']
        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        e_indices = valid_indices[e_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, e_index in enumerate(e_indices):
            actual_e = energy_values[e_index]
            kdc = data_to_plot[e_index, :]
            k_parallel = plot_data['k_parallel'][0]

            # Filter k_parallel values within the specified range
            valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
            kdc = kdc[valid_k_indices]
            k_parallel = k_parallel[valid_k_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Apply vertical offset without normalization
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(k_parallel, offset_kdc, label=f'E = {actual_e:.2f} eV')

            # Draw x'd line at the "zero level" of each curve
            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=k_parallel[peak], min=k_parallel.min(), max=k_parallel.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=k_parallel)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset

                ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(k_parallel[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(k_parallel[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('k$_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'KDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(k_min, k_max)

        # Ensure the entire curve is visible, including negative values
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()
        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'vertical_enabled': interactive_plot.children[10].value,
            'horizontal_enabled': interactive_plot.children[11].value,
            'vertical_magnitude': interactive_plot.children[12].value,
            'horizontal_magnitude': interactive_plot.children[13].value,
            'kernel_size': interactive_plot.children[14].value,
            'enable_averaging': interactive_plot.children[15].value,
            'averaging_kernel_size': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"KDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=20, step=1, description='Number of KDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=global_intensity_max*10, step=global_intensity_max/100, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show KDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def edc_plot(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    global_k_min = float('inf')
    global_k_max = float('-inf')
    global_e_min = float('inf')
    global_e_max = float('-inf')
    global_intensity_max = float('-inf')

    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': os.path.basename(file_path)
        })

        # Update global ranges
        global_k_min = min(global_k_min, np.min(k_parallel))
        global_k_max = max(global_k_max, np.max(k_parallel))
        global_e_min = min(global_e_min, np.min(energy_values))
        global_e_max = max(global_e_max, np.max(energy_values))
        global_intensity_max = max(global_intensity_max, np.max(data.values))

    global_k_range = global_k_max - global_k_min
    global_e_range = global_e_max - global_e_min

    fig, ax = plt.subplots(figsize=(10, 8))

    def plot_edc(scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, 
                 vertical_enabled, horizontal_enabled, vertical_magnitude, horizontal_magnitude, kernel_size,
                 enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]

        # Clear the current figure
        ax.clear()

        # Apply convolutions if enabled
        data_to_plot = plot_data['data_values'].copy()
        if vertical_enabled or horizontal_enabled:
            if vertical_enabled:
                vertical_kernel = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]]) * vertical_magnitude
                if kernel_size > 3:
                    vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, vertical_kernel)
            if horizontal_enabled:
                horizontal_kernel = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]]) * horizontal_magnitude
                if kernel_size > 3:
                    horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
                data_to_plot = convolve2d(data_to_plot, horizontal_kernel)

        # Apply moving average if enabled
        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve2d(data_to_plot, averaging_kernel)

        # Calculate equally spaced energy indices within the specified range
        energy_values = plot_data['energy_values']
        valid_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
        e_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        e_indices = valid_indices[e_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, e_index in enumerate(e_indices):
            actual_e = energy_values[e_index]
            kdc = data_to_plot[e_index, :]
            k_parallel = plot_data['k_parallel'][0]

            # Filter k_parallel values within the specified range
            valid_k_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
            kdc = kdc[valid_k_indices]
            k_parallel = k_parallel[valid_k_indices]

            # Convert to numpy arrays if they are xarray.DataArray
            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(k_parallel, xr.DataArray):
                k_parallel = k_parallel.values

            # Apply vertical offset without normalization
            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                ax.plot(k_parallel, offset_kdc, label=f'E = {actual_e:.2f} eV')

            # Draw x'd line at the "zero level" of each curve
            ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                # Fit KDC with multiple Lorentzian peaks + Linear background
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=k_parallel[peak], min=k_parallel.min(), max=k_parallel.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=k_parallel)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset

                ax.plot(k_parallel, offset_fit, '--', label=f'Fit E = {actual_e:.2f} eV', color=f'C{i}')

                # Find local maxima and minima on the fitted curve
                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)

                # Plot local maxima and minima
                ax.plot(k_parallel[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                ax.plot(k_parallel[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        ax.set_xlabel('k$_\parallel$ (Å$^{-1}$)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        ax.set_title(f'KDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        ax.legend()
        ax.tick_params(axis='both', which='major', labelsize=10)

        # Set axis limits based on sliders
        ax.set_xlim(k_min, k_max)

        # Ensure the entire curve is visible, including negative values
        y_range = max_intensity - min_intensity
        ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        fig.canvas.draw_idle()
        return max_intensity - min_intensity

    def save_plot(b):
        current_values = {
            'scan_index': interactive_plot.children[0].value,
            'n': interactive_plot.children[1].value,
            'vertical_offset': interactive_plot.children[2].value,
            'show_edc': interactive_plot.children[3].value,
            'show_fit': interactive_plot.children[4].value,
            'num_peaks': interactive_plot.children[5].value,
            'k_min': interactive_plot.children[6].value,
            'k_max': interactive_plot.children[7].value,
            'e_min': interactive_plot.children[8].value,
            'e_max': interactive_plot.children[9].value,
            'vertical_enabled': interactive_plot.children[10].value,
            'horizontal_enabled': interactive_plot.children[11].value,
            'vertical_magnitude': interactive_plot.children[12].value,
            'horizontal_magnitude': interactive_plot.children[13].value,
            'kernel_size': interactive_plot.children[14].value,
            'enable_averaging': interactive_plot.children[15].value,
            'averaging_kernel_size': interactive_plot.children[16].value
        }
        plot_edc(**current_values)
        filename = f"KDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    interactive_plot = interactive(plot_edc,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
        n=IntSlider(value=5, min=1, max=40, step=1, description='Number of KDCs', continuous_update=True),
        vertical_offset=FloatSlider(value=0.5, min=0.0, max=global_intensity_max*10, step=global_intensity_max/100, description='Vertical Offset', continuous_update=True),
        show_edc=Checkbox(value=True, description='Show KDCs'),
        show_fit=Checkbox(value=False, description='Show Fits'),
        num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
        k_min=FloatSlider(value=global_k_min, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_min (Å⁻¹)', continuous_update=True),
        k_max=FloatSlider(value=global_k_max, min=global_k_min, max=global_k_max, step=global_k_range/100, description='k_max (Å⁻¹)', continuous_update=True),
        e_min=FloatSlider(value=global_e_min, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_min (eV)', continuous_update=True),
        e_max=FloatSlider(value=global_e_max, min=global_e_min, max=global_e_max, step=global_e_range/100, description='E_max (eV)', continuous_update=True),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Kernel Size', continuous_update=False),
        enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
        averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
    )

    save_button = Button(description="Save Plot")
    save_button.on_click(save_plot)

    output = VBox([interactive_plot, save_button])
    return output

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5 # eV (adjust this value if you know the correct work function for your sample)
    interactive_plot_with_save = edc_plot(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
