# IPython log file

import numpy as np
import xarray as xr
import matplotlib.pyplot as plt

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_kinetic = E_photon - work_function - np.abs(data.eV)
k_parallel = np.sqrt(2 * m_e * E_kinetic) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Create the plot
plt.figure(figsize=(10, 8))
plt.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='hot')
plt.colorbar(label='Intensity')
plt.xlabel('k_parallel (Å^-1)')
plt.ylabel('Energy (eV)')
plt.title('ARPES Data in Momentum Space')

# Adjust the y-axis direction
plt.gca().invert_yaxis()

plt.show()
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_kinetic = np.abs(data.eV)
k_parallel = np.sqrt(2 * m_e * E_kinetic) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Create the plot
plt.figure(figsize=(10, 8))
plt.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='hot')
plt.colorbar(label='Intensity')
plt.xlabel('k_parallel (Å^-1)')
plt.ylabel('Energy (eV)')
plt.title('ARPES Data in Momentum Space')

# Adjust the y-axis direction
plt.gca().invert_yaxis()

plt.show()
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.0": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = np.abs(data.eV)\nk_parallel = np.sqrt(2 * m_e * E_kinetic) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = data.eV.values\n\n# Create the plot\nplt.figure(figsize=(10, 8))\nplt.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='hot')\nplt.colorbar(label='Intensity')\nplt.xlabel('k_parallel (Å^-1)')\nplt.ylabel('Energy (eV)')\nplt.title('ARPES Data in Momentum Space')\n\n# Adjust the y-axis direction\nplt.gca().invert_yaxis()\n\nplt.show()", 657)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.0.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.0.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.1": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = np.abs(data.eV)\nk_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = data.eV.values\n\n# Create the plot\nplt.figure(figsize=(10, 8))\nplt.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='hot')\nplt.colorbar(label='Intensity')\nplt.xlabel('k_parallel (Å^-1)')\nplt.ylabel('Energy (eV)')\nplt.title('ARPES Data in Momentum Space')\n\n# Adjust the y-axis direction\nplt.gca().invert_yaxis()\n\nplt.show()", 710)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.1.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.1.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.2": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = w-np.abs(data.eV)\nk_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = data.eV.values\n\n# Create the plot\nplt.figure(figsize=(10, 8))\nplt.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='hot')\nplt.colorbar(label='Intensity')\nplt.xlabel('k_parallel (Å^-1)')\nplt.ylabel('Energy (eV)')\nplt.title('ARPES Data in Momentum Space')\n\n# Adjust the y-axis direction\nplt.gca().invert_yaxis()\n\nplt.show()", 661)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.2.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.2.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.3": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = Ework_function-np.abs(data.eV)\nk_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = data.eV.values\n\n# Create the plot\nplt.figure(figsize=(10, 8))\nplt.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='hot')\nplt.colorbar(label='Intensity')\nplt.xlabel('k_parallel (Å^-1)')\nplt.ylabel('Energy (eV)')\nplt.title('ARPES Data in Momentum Space')\n\n# Adjust the y-axis direction\nplt.gca().invert_yaxis()\n\nplt.show()", 661)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.3.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.3.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = E_photon -work_function-np.abs(data.eV)
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Create the plot
plt.figure(figsize=(10, 8))
plt.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='hot')
plt.colorbar(label='Intensity')
plt.xlabel('k_parallel (Å^-1)')
plt.ylabel('Energy (eV)')
plt.title('ARPES Data in Momentum Space')

# Adjust the y-axis direction
plt.gca().invert_yaxis()

plt.show()
