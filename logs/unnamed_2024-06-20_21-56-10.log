# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap
from matplotlib.collections import QuadMesh
import warnings
from numba import jit
from functools import lru_cache

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

@jit(nopython=True)
def calculate_k_parallel(E_b, phi, hbar, m_e):
    return np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(phi)) / hbar

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

@lru_cache(maxsize=None)
def load_and_process_data(file_path, work_function):
    data = read_single_pxt(file_path)
    
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg
    E_photon = data.attrs['hv']
    
    E_b = work_function + np.abs(data.eV.values) - E_photon
    k_parallel = calculate_k_parallel(E_b, data.phi.values, hbar, m_e)
    energy_values = -data.eV.values
    
    return k_parallel, energy_values, data.values

def kspace(data_files, work_function):
    # Load the first file to initialize the plot
    k_parallel, energy_values, initial_data = load_and_process_data(data_files[0], work_function)

    fig, ax = plt.subplots(figsize=(10, 8))
    quad_mesh = QuadMesh(k_parallel, energy_values, initial_data, shading='flat')
    ax.add_collection(quad_mesh)
    cbar = fig.colorbar(quad_mesh, ax=ax, label='Intensity')
    
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
    ax.set_ylabel(r'$E-E_f$ (eV)')
    ax.set_xlim(k_parallel.min(), k_parallel.max())
    ax.set_ylim(energy_values.min(), energy_values.max())
    
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1
        k_parallel, energy_values, data_values = load_and_process_data(data_files[file_index], work_function)

        norm = Normalize(vmin=vmin, vmax=vmax/scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        quad_mesh.set_array(data_values.ravel())
        quad_mesh.set_cmap(cmap)
        quad_mesh.set_norm(norm)
        
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_xlim(k_parallel.min(), k_parallel.max())
        ax.set_ylim(energy_values.min(), energy_values.max())

        cbar.update_normal(quad_mesh)
        fig.canvas.draw_idle()

    # Get the maximum data value across all files
    max_data_value = max(np.max(load_and_process_data(f, work_function)[2]) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    
    return interactive_plot, fig

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot, fig = kspace(data_files, work_function)
display(interactive_plot)
plt.show()
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap
from matplotlib.collections import QuadMesh
import warnings
from functools import lru_cache

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def calculate_k_parallel(E_b, phi, hbar, m_e):
    return np.sqrt(-2 * m_e * E_b[:, np.newaxis]) * np.sin(np.radians(phi[np.newaxis, :])) / hbar

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

@lru_cache(maxsize=None)
def load_and_process_data(file_path, work_function):
    data = read_single_pxt(file_path)
    
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg
    E_photon = data.attrs['hv']
    
    E_b = work_function + np.abs(data.eV.values) - E_photon
    k_parallel = calculate_k_parallel(E_b, data.phi.values, hbar, m_e)
    energy_values = -data.eV.values
    
    return k_parallel, energy_values, data.values

def kspace(data_files, work_function):
    # Load the first file to initialize the plot
    k_parallel, energy_values, initial_data = load_and_process_data(data_files[0], work_function)

    fig, ax = plt.subplots(figsize=(10, 8))
    quad_mesh = QuadMesh(k_parallel, energy_values, initial_data, shading='flat')
    ax.add_collection(quad_mesh)
    cbar = fig.colorbar(quad_mesh, ax=ax, label='Intensity')
    
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
    ax.set_ylabel(r'$E-E_f$ (eV)')
    ax.set_xlim(k_parallel.min(), k_parallel.max())
    ax.set_ylim(energy_values.min(), energy_values.max())
    
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1
        k_parallel, energy_values, data_values = load_and_process_data(data_files[file_index], work_function)

        norm = Normalize(vmin=vmin, vmax=vmax/scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        quad_mesh.set_array(data_values.ravel())
        quad_mesh.set_cmap(cmap)
        quad_mesh.set_norm(norm)
        
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_xlim(k_parallel.min(), k_parallel.max())
        ax.set_ylim(energy_values.min(), energy_values.max())

        cbar.update_normal(quad_mesh)
        fig.canvas.draw_idle()

    # Get the maximum data value across all files
    max_data_value = max(np.max(load_and_process_data(f, work_function)[2]) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    
    return interactive_plot, fig

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot, fig = kspace(data_files, work_function)
display(interactive_plot)
plt.show()
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout
from matplotlib.colors import Normalize, ListedColormap
import warnings
from functools import lru_cache

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def calculate_k_parallel(E_b, phi, hbar, m_e):
    return np.sqrt(-2 * m_e * E_b[:, np.newaxis]) * np.sin(np.radians(phi[np.newaxis, :])) / hbar

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

@lru_cache(maxsize=None)
def load_and_process_data(file_path, work_function):
    data = read_single_pxt(file_path)
    
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg
    E_photon = data.attrs['hv']
    
    E_b = work_function + np.abs(data.eV.values) - E_photon
    k_parallel = calculate_k_parallel(E_b, data.phi.values, hbar, m_e)
    energy_values = -data.eV.values
    
    return k_parallel, energy_values, data.values

def kspace(data_files, work_function):
    # Load the first file to initialize the plot
    k_parallel, energy_values, initial_data = load_and_process_data(data_files[0], work_function)

    fig, ax = plt.subplots(figsize=(10, 8))
    mesh = ax.pcolormesh(k_parallel, energy_values, initial_data, shading='auto')
    cbar = fig.colorbar(mesh, ax=ax, label='Intensity')
    
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
    ax.set_ylabel(r'$E-E_f$ (eV)')
    ax.set_xlim(k_parallel.min(), k_parallel.max())
    ax.set_ylim(energy_values.min(), energy_values.max())
    
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1
        k_parallel, energy_values, data_values = load_and_process_data(data_files[file_index], work_function)

        norm = Normalize(vmin=vmin, vmax=vmax/scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        mesh.set_array(data_values.ravel())
        mesh.set_cmap(cmap)
        mesh.set_norm(norm)
        
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_xlim(k_parallel.min(), k_parallel.max())
        ax.set_ylim(energy_values.min(), energy_values.max())

        cbar.update_normal(mesh)
        fig.canvas.draw_idle()

    # Get the maximum data value across all files
    max_data_value = max(np.max(load_and_process_data(f, work_function)[2]) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    
    return interactive_plot, fig

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot, fig = kspace(data_files, work_function)
display(interactive_plot)
plt.show()
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
import warnings
from functools import lru_cache

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def calculate_k_parallel(E_b, phi, hbar, m_e):
    return np.sqrt(-2 * m_e * E_b[:, np.newaxis]) * np.sin(np.radians(phi[np.newaxis, :])) / hbar

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

@lru_cache(maxsize=None)
def load_and_process_data(file_path, work_function):
    data = read_single_pxt(file_path)
    
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg
    E_photon = data.attrs['hv']
    
    E_b = work_function + np.abs(data.eV.values) - E_photon
    k_parallel = calculate_k_parallel(E_b, data.phi.values, hbar, m_e)
    energy_values = -data.eV.values
    
    return k_parallel, energy_values, data.values

def kspace(data_files, work_function):
    fig, ax = plt.subplots(figsize=(10, 8))
    cbar = None
    out = Output()

    @out.capture(clear_output=True)
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        nonlocal cbar
        file_index = scan_index - 1
        k_parallel, energy_values, data_values = load_and_process_data(data_files[file_index], work_function)

        norm = Normalize(vmin=vmin, vmax=vmax/scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        ax.clear()
        mesh = ax.pcolormesh(k_parallel, energy_values, data_values, shading='auto', cmap=cmap, norm=norm)
        
        if cbar is None:
            cbar = fig.colorbar(mesh, ax=ax, label='Intensity')
        else:
            cbar.update_normal(mesh)
        
        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_xlim(k_parallel.min(), k_parallel.max())
        ax.set_ylim(energy_values.min(), energy_values.max())

        fig.canvas.draw_idle()
        plt.show()

    # Get the maximum data value across all files
    max_data_value = max(np.max(load_and_process_data(f, work_function)[2]) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=False),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=False),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False))
    
    return interactive_plot, out

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot, out = kspace(data_files, work_function)
display(interactive_plot, out)
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
import warnings

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Load the first file to initialize the plot
    initial_data = read_single_pxt(data_files[0])
    
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg
    E_photon = initial_data.attrs['hv']  # Photon energy from the attributes  

    # Calculate kinetic energy and momentum for initial data
    E_b = work_function + np.abs(initial_data.eV) - E_photon  # Negative E_b values
    k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(initial_data.phi)) / hbar

    # Get the energy values for initial data
    energy_values = -initial_data.eV.values  # Negative energy values

    fig, ax = plt.subplots(figsize=(10, 8))
    im = ax.pcolormesh(k_parallel, energy_values, initial_data.values, shading='auto')
    cbar = fig.colorbar(im, ax=ax, label='Intensity')
    
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        # Update the existing pcolormesh
        im.set_array(None)
        im.set_edgecolors('none')
        im.set_cmap(cmap)
        im.set_norm(norm)
        im.set_offsets(np.c_[k_parallel.ravel(), energy_values.ravel()])
        im.set_array(data.values.ravel())

        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_xlim(k_parallel.min(), k_parallel.max())
        ax.set_ylim(energy_values.min(), energy_values.max())

        # Update colorbar
        cbar.update_normal(im)
        
        fig.canvas.draw_idle()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    
    return interactive_plot, fig

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot, fig = kspace(data_files, work_function)
display(interactive_plot)
plt.show()
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Output
from matplotlib.colors import Normalize, ListedColormap
import warnings

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def kspace(data_files, work_function):
    # Load the first file to initialize the plot
    initial_data = read_single_pxt(data_files[0])
    
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg
    E_photon = initial_data.attrs['hv']  # Photon energy from the attributes  

    # Calculate kinetic energy and momentum for initial data
    E_b = work_function + np.abs(initial_data.eV) - E_photon  # Negative E_b values
    k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(initial_data.phi))
    k_parallel = k_parallel.values / hbar  # Convert to numpy array and divide by hbar

    # Get the energy values for initial data
    energy_values = -initial_data.eV.values  # Negative energy values

    fig, ax = plt.subplots(figsize=(10, 8))
    im = ax.pcolormesh(k_parallel, energy_values, initial_data.values, shading='auto')
    cbar = fig.colorbar(im, ax=ax, label='Intensity')
    
    def plot_arpes(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        file_index = scan_index - 1  # Convert scan_index to zero-based index
        data = read_single_pxt(data_files[file_index])

        # Constants
        E_photon = data.attrs['hv']  # Photon energy from the attributes  

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi))
        k_parallel = k_parallel.values / hbar  # Convert to numpy array and divide by hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        # Create meshgrid
        K, E = np.meshgrid(k_parallel, energy_values)

        # Create a custom normalization
        norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
        # Create a colormap with the specified range
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
    
        # Update the existing pcolormesh
        im.set_array(None)
        im.set_edgecolors('none')
        im.set_cmap(cmap)
        im.set_norm(norm)
        im.set_offsets(np.c_[K.ravel(), E.ravel()])
        im.set_array(data.values.ravel())

        ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
        ax.set_ylabel(r'$E-E_f$ (eV)')
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(data_files[file_index])}')
        ax.set_xlim(k_parallel.min(), k_parallel.max())
        ax.set_ylim(energy_values.min(), energy_values.max())

        # Update colorbar
        cbar.update_normal(im)
        
        fig.canvas.draw_idle()

    # Get the maximum data value across all files
    max_data_value = max(np.max(read_single_pxt(f).values) for f in data_files)

    # Create the interactive widget
    interactive_plot = interactive(plot_arpes,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))
    
    return interactive_plot, fig

# Usage
folder_path = '/home/<USER>/Documents/September 2022 Beamtime/bruh/'
data_files = load_data_files(folder_path)
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

interactive_plot, fig = kspace(data_files, work_function)
display(interactive_plot)
plt.show()
