# IPython log file

import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",  # Set default text color to black
    "axes.labelcolor": "black",  # Set default axes label color to black
})

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']  # Photon energy from the attributes

        # Calculate kinetic energy and momentum
        E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

        # Get the energy values
        energy_values = -data.eV.values  # Negative energy values

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': file_path
        })
        max_data_value = max(max_data_value, np.max(data.values))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    plot_data = all_plots[0]
    norm = Normalize(vmin=0, vmax=max_data_value)
    cmap_array = plt.cm.jet(np.linspace(0, 1, 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()

    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
        norm = Normalize(vmin=vmin, vmax=vmax / scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
        im.set_array(plot_data['data_values'].ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
        fig.canvas.draw_idle()

    interactive_plot = interactive(update_plot,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
        vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
        vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
        scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
        cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
        cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True)
    )

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: save_plot(b, interactive_plot, all_plots))

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(lambda b: make_video(b, interactive_plot, all_plots, data_files))

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])
    return output

def save_plot(b, interactive_plot, all_plots):
    # Get current slider values directly from the widgets
    current_values = {
        'scan_index': interactive_plot.children[0].value,
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Generate the plot with current values
    fig = plot_arpes(current_values, all_plots)

    # Get the current file name being plotted
    current_file = all_plots[current_values['scan_index'] - 1]['file_name']
    
    # Save the plot in the same folder as the data files
    save_folder = os.path.dirname(current_file)
    filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
    fig.savefig(filename, dpi=2000, bbox_inches='tight')
    plt.close(fig)
    print(f"Plot saved as {filename}")

def make_video(b, interactive_plot, all_plots, data_files):
    # Get current slider values directly from the widgets
    current_values = {
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Determine scan types
    scan_types = determine_scan_type(data_files)

    # Create the video
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)

    plot_data = all_plots[0]
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')

    ims = []
    for i, (plot_data, (scan_type, scan_value)) in enumerate(zip(all_plots, scan_types)):
        im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],
                           shading='auto', cmap=cmap, norm=norm)
        if scan_type != 'unknown':
            text = ax.text(0.5, -0.1, f'{scan_type}: {scan_value:.2f}',  # Position text below the plot
                           transform=ax.transAxes, color='black',  # Set text color to black
                           horizontalalignment='center', verticalalignment='top',
                           fontsize=10, fontweight='bold')
            ims.append([im, text])
        else:
            ims.append([im])

    ani = animation.ArtistAnimation(fig, ims, interval=500, blit=True)

    # Save the video in the same folder as the data files, named after the folder
    save_folder = os.path.dirname(all_plots[0]['file_name'])
    folder_name = os.path.basename(save_folder)
    video_filename = os.path.join(save_folder, f"{folder_name}_ARPES_video.mp4")
    ani.save(video_filename)
    plt.close(fig)
    print(f"Video saved as {video_filename}")

def plot_arpes(current_values, all_plots):
    plot_data = all_plots[current_values['scan_index'] - 1]  # Convert scan_index to zero-based index
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'],
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()
    return fig

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    scan_types = determine_scan_type(data_files)
    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog
import tensorflow as tf
from tensorflow.keras import layers, models

get_ipython().run_line_magic('matplotlib', 'widget')

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def create_denoising_model(input_shape):
    model = models.Sequential([
        layers.Input(shape=input_shape),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.MaxPooling2D((2, 2), padding='same'),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.MaxPooling2D((2, 2), padding='same'),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.UpSampling2D((2, 2)),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.UpSampling2D((2, 2)),
        layers.Conv2D(1, (3, 3), activation='linear', padding='same')
    ])
    model.compile(optimizer='adam', loss='mse')
    return model

def train_denoising_model(data_files, work_function):
    all_data = []
    for file_path in data_files:
        data = read_single_pxt(file_path)
        all_data.append(data.values)
    
    all_data = np.array(all_data)
    
    # Normalize data
    all_data = (all_data - np.min(all_data)) / (np.max(all_data) - np.min(all_data))
    
    # Add noise to create noisy-clean pairs
    noisy_data = all_data + 0.1 * np.random.randn(*all_data.shape)
    noisy_data = np.clip(noisy_data, 0, 1)
    
    # Reshape data for the model
    input_shape = all_data.shape[1:]
    noisy_data = noisy_data.reshape((-1, *input_shape, 1))
    clean_data = all_data.reshape((-1, *input_shape, 1))
    
    # Create and train the model
    model = create_denoising_model((*input_shape, 1))
    model.fit(noisy_data, clean_data, epochs=50, batch_size=32, validation_split=0.2)
    
    return model

def denoise_data(model, data):
    # Normalize data
    data_norm = (data - np.min(data)) / (np.max(data) - np.min(data))
    
    # Reshape for prediction
    data_reshaped = data_norm.reshape((1, *data.shape, 1))
    
    # Denoise
    denoised = model.predict(data_reshaped)
    
    # Reshape and denormalize
    denoised = denoised.reshape(data.shape)
    denoised = denoised * (np.max(data) - np.min(data)) + np.min(data)
    
    return denoised

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Train the denoising model
    denoising_model = train_denoising_model(data_files, work_function)

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar
        energy_values = -data.eV.values

        # Denoise the data
        denoised_data = denoise_data(denoising_model, data.values)

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': denoised_data,
            'file_name': file_path
        })
        max_data_value = max(max_data_value, np.max(denoised_data))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    plot_data = all_plots[0]
    norm = Normalize(vmin=0, vmax=max_data_value)
    cmap_array = plt.cm.jet(np.linspace(0, 1, 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()

    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
        norm = Normalize(vmin=vmin, vmax=vmax / scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
        im.set_array(plot_data['data_values'].ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
        fig.canvas.draw_idle()

    interactive_plot = interactive(update_plot,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: save_plot(b, interactive_plot, all_plots))

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(lambda b: make_video(b, interactive_plot, all_plots, data_files))

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])
    return output

def save_plot(b, interactive_plot, all_plots):
    # Get current slider values directly from the widgets
    current_values = {
        'scan_index': interactive_plot.children[0].value,
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Generate the plot with current values
    fig = plot_arpes(current_values, all_plots)

    # Get the current file name being plotted
    current_file = all_plots[current_values['scan_index'] - 1]['file_name']

    # Save the plot in the same folder as the data files
    save_folder = os.path.dirname(current_file)
    filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
    fig.savefig(filename, dpi=2000, bbox_inches='tight')
    plt.close(fig)
    print(f"Plot saved as {filename}")

def plot_arpes(current_values, all_plots):
    plot_data = all_plots[current_values['scan_index'] - 1]  # Convert scan_index to zero-based index
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()
    return fig
def make_video(b, interactive_plot, all_plots, data_files):
    # Get current slider values directly from the widgets
    current_values = {
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Determine scan types
    scan_types = determine_scan_type(data_files)

    # Create the video
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    plot_data = all_plots[0]
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')

    def animate(i):
        plot_data = all_plots[i]
        im.set_array(plot_data['data_values'].ravel())
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                     fontsize=14, fontweight='bold')
        if scan_types[i][0] != 'unknown':
            ax.text(0.5, -0.1, f'{scan_types[i][0]}: {scan_types[i][1]:.2f}',
                    transform=ax.transAxes, color='black',
                    horizontalalignment='center', verticalalignment='top',
                    fontsize=10, fontweight='bold')
        return [im]

    anim = animation.FuncAnimation(fig, animate, frames=len(all_plots), interval=500, blit=True)

    # Save the video in the same folder as the data files, named after the folder
    save_folder = os.path.dirname(all_plots[0]['file_name'])
    folder_name = os.path.basename(save_folder)
    video_filename = os.path.join(save_folder, f"{folder_name}_ARPES_video.mp4")
    
    # Use FFMpegWriter to save the animation
    writer = animation.FFMpegWriter(fps=2, metadata=dict(artist='Me'), bitrate=1800)
    anim.save(video_filename, writer=writer)
    
    plt.close(fig)
    print(f"Video saved as {video_filename}")

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    scan_types = determine_scan_type(data_files)
    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.0": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("p", 1)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.0.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.0.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.1": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip i", 5)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.1.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.1.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.2": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip instal", 10)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.2.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.2.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.3": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install", 11)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.3.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.3.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.4": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install t", 13)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.4.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.4.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.5": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install te", 14)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.5.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.5.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.6": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install tensor", 18)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.6.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.6.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.7": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install tensorf", 19)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.7.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.7.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.8": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("pip install tensorflo", 21)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.8.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.8.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
get_ipython().run_line_magic('pip', 'install tensorflow')
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog
import tensorflow as tf
from tensorflow.keras import layers, models

get_ipython().run_line_magic('matplotlib', 'widget')

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def create_denoising_model(input_shape):
    model = models.Sequential([
        layers.Input(shape=input_shape),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.MaxPooling2D((2, 2), padding='same'),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.MaxPooling2D((2, 2), padding='same'),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.UpSampling2D((2, 2)),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.UpSampling2D((2, 2)),
        layers.Conv2D(1, (3, 3), activation='linear', padding='same')
    ])
    model.compile(optimizer='adam', loss='mse')
    return model

def train_denoising_model(data_files, work_function):
    all_data = []
    for file_path in data_files:
        data = read_single_pxt(file_path)
        all_data.append(data.values)
    
    all_data = np.array(all_data)
    
    # Normalize data
    all_data = (all_data - np.min(all_data)) / (np.max(all_data) - np.min(all_data))
    
    # Add noise to create noisy-clean pairs
    noisy_data = all_data + 0.1 * np.random.randn(*all_data.shape)
    noisy_data = np.clip(noisy_data, 0, 1)
    
    # Reshape data for the model
    input_shape = all_data.shape[1:]
    noisy_data = noisy_data.reshape((-1, *input_shape, 1))
    clean_data = all_data.reshape((-1, *input_shape, 1))
    
    # Create and train the model
    model = create_denoising_model((*input_shape, 1))
    model.fit(noisy_data, clean_data, epochs=50, batch_size=32, validation_split=0.2)
    
    return model

def denoise_data(model, data):
    # Normalize data
    data_norm = (data - np.min(data)) / (np.max(data) - np.min(data))
    
    # Reshape for prediction
    data_reshaped = data_norm.reshape((1, *data.shape, 1))
    
    # Denoise
    denoised = model.predict(data_reshaped)
    
    # Reshape and denormalize
    denoised = denoised.reshape(data.shape)
    denoised = denoised * (np.max(data) - np.min(data)) + np.min(data)
    
    return denoised

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Train the denoising model
    denoising_model = train_denoising_model(data_files, work_function)

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar
        energy_values = -data.eV.values

        # Denoise the data
        denoised_data = denoise_data(denoising_model, data.values)

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': denoised_data,
            'file_name': file_path
        })
        max_data_value = max(max_data_value, np.max(denoised_data))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    plot_data = all_plots[0]
    norm = Normalize(vmin=0, vmax=max_data_value)
    cmap_array = plt.cm.jet(np.linspace(0, 1, 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()

    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
        norm = Normalize(vmin=vmin, vmax=vmax / scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
        im.set_array(plot_data['data_values'].ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
        fig.canvas.draw_idle()

    interactive_plot = interactive(update_plot,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: save_plot(b, interactive_plot, all_plots))

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(lambda b: make_video(b, interactive_plot, all_plots, data_files))

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])
    return output

def save_plot(b, interactive_plot, all_plots):
    # Get current slider values directly from the widgets
    current_values = {
        'scan_index': interactive_plot.children[0].value,
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Generate the plot with current values
    fig = plot_arpes(current_values, all_plots)

    # Get the current file name being plotted
    current_file = all_plots[current_values['scan_index'] - 1]['file_name']

    # Save the plot in the same folder as the data files
    save_folder = os.path.dirname(current_file)
    filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
    fig.savefig(filename, dpi=2000, bbox_inches='tight')
    plt.close(fig)
    print(f"Plot saved as {filename}")

def plot_arpes(current_values, all_plots):
    plot_data = all_plots[current_values['scan_index'] - 1]  # Convert scan_index to zero-based index
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()
    return fig
def make_video(b, interactive_plot, all_plots, data_files):
    # Get current slider values directly from the widgets
    current_values = {
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Determine scan types
    scan_types = determine_scan_type(data_files)

    # Create the video
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    plot_data = all_plots[0]
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')

    def animate(i):
        plot_data = all_plots[i]
        im.set_array(plot_data['data_values'].ravel())
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                     fontsize=14, fontweight='bold')
        if scan_types[i][0] != 'unknown':
            ax.text(0.5, -0.1, f'{scan_types[i][0]}: {scan_types[i][1]:.2f}',
                    transform=ax.transAxes, color='black',
                    horizontalalignment='center', verticalalignment='top',
                    fontsize=10, fontweight='bold')
        return [im]

    anim = animation.FuncAnimation(fig, animate, frames=len(all_plots), interval=500, blit=True)

    # Save the video in the same folder as the data files, named after the folder
    save_folder = os.path.dirname(all_plots[0]['file_name'])
    folder_name = os.path.basename(save_folder)
    video_filename = os.path.join(save_folder, f"{folder_name}_ARPES_video.mp4")
    
    # Use FFMpegWriter to save the animation
    writer = animation.FFMpegWriter(fps=2, metadata=dict(artist='Me'), bitrate=1800)
    anim.save(video_filename, writer=writer)
    
    plt.close(fig)
    print(f"Video saved as {video_filename}")

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    scan_types = determine_scan_type(data_files)
    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog
import tensorflow as tf
from tensorflow.keras import layers, models

get_ipython().run_line_magic('matplotlib', 'widget')

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def create_denoising_model(input_shape):
    model = models.Sequential([
        layers.Input(shape=input_shape),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.MaxPooling2D((2, 2), padding='same'),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.MaxPooling2D((2, 2), padding='same'),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.UpSampling2D((2, 2)),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.UpSampling2D((2, 2)),
        layers.Conv2D(1, (3, 3), activation='linear', padding='same'),
        layers.Resizing(input_shape[0], input_shape[1], interpolation='bilinear')
    ])
    model.compile(optimizer='adam', loss='mse')
    return model

def train_denoising_model(data_files, work_function):
    all_data = []
    for file_path in data_files:
        data = read_single_pxt(file_path)
        all_data.append(data.values)
    
    all_data = np.array(all_data)
    
    # Find the minimum dimensions across all data
    min_height = min(data.shape[0] for data in all_data)
    min_width = min(data.shape[1] for data in all_data)
    
    # Resize all data to the minimum dimensions
    resized_data = np.array([tf.image.resize(data, (min_height, min_width)).numpy() for data in all_data])
    
    # Normalize data
    resized_data = (resized_data - np.min(resized_data)) / (np.max(resized_data) - np.min(resized_data))
    
    # Add noise to create noisy-clean pairs
    noisy_data = resized_data + 0.1 * np.random.randn(*resized_data.shape)
    noisy_data = np.clip(noisy_data, 0, 1)
    
    # Reshape data for the model
    input_shape = resized_data.shape[1:]
    noisy_data = noisy_data.reshape((-1, *input_shape, 1))
    clean_data = resized_data.reshape((-1, *input_shape, 1))
    
    # Create and train the model
    model = create_denoising_model((*input_shape, 1))
    model.fit(noisy_data, clean_data, epochs=50, batch_size=32, validation_split=0.2)
    
    return model, (min_height, min_width)

def denoise_data(model, data, target_shape):
    # Resize data to target shape
    resized_data = tf.image.resize(data, target_shape).numpy()
    
    # Normalize data
    data_norm = (resized_data - np.min(resized_data)) / (np.max(resized_data) - np.min(resized_data))
    
    # Reshape for prediction
    data_reshaped = data_norm.reshape((1, *data_norm.shape, 1))
    
    # Denoise
    denoised = model.predict(data_reshaped)
    
    # Reshape and denormalize
    denoised = denoised.reshape(data_norm.shape)
    denoised = denoised * (np.max(resized_data) - np.min(resized_data)) + np.min(resized_data)
    
    return denoised

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Train the denoising model
    denoising_model, target_shape = train_denoising_model(data_files, work_function)

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar
        energy_values = -data.eV.values

        # Denoise the data
        denoised_data = denoise_data(denoising_model, data.values, target_shape)

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': denoised_data,
            'file_name': file_path
        })
        max_data_value = max(max_data_value, np.max(denoised_data))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    plot_data = all_plots[0]
    norm = Normalize(vmin=0, vmax=max_data_value)
    cmap_array = plt.cm.jet(np.linspace(0, 1, 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()

    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
        norm = Normalize(vmin=vmin, vmax=vmax / scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
        im.set_array(plot_data['data_values'].ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
        fig.canvas.draw_idle()

    interactive_plot = interactive(update_plot,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: save_plot(b, interactive_plot, all_plots))

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(lambda b: make_video(b, interactive_plot, all_plots, data_files))

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])
    return output

def save_plot(b, interactive_plot, all_plots):
    # Get current slider values directly from the widgets
    current_values = {
        'scan_index': interactive_plot.children[0].value,
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Generate the plot with current values
    fig = plot_arpes(current_values, all_plots)

    # Get the current file name being plotted
    current_file = all_plots[current_values['scan_index'] - 1]['file_name']

    # Save the plot in the same folder as the data files
    save_folder = os.path.dirname(current_file)
    filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
    fig.savefig(filename, dpi=2000, bbox_inches='tight')
    plt.close(fig)
    print(f"Plot saved as {filename}")

def plot_arpes(current_values, all_plots):
    plot_data = all_plots[current_values['scan_index'] - 1]  # Convert scan_index to zero-based index
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()
    return fig

def make_video(b, interactive_plot, all_plots, data_files):
    # Get current slider values directly from the widgets
    current_values = {
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Determine scan types
    scan_types = determine_scan_type(data_files)

    # Create the video
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    plot_data = all_plots[0]
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')

    def animate(i):
        plot_data = all_plots[i]
        im.set_array(plot_data['data_values'].ravel())
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                     fontsize=14, fontweight='bold')
        if scan_types[i][0] != 'unknown':
            ax.text(0.5, -0.1, f'{scan_types[i][0]}: {scan_types[i][1]:.2f}',
                    transform=ax.transAxes, color='black',
                    horizontalalignment='center', verticalalignment='top',
                    fontsize=10, fontweight='bold')
        return [im]

    anim = animation.FuncAnimation(fig, animate, frames=len(all_plots), interval=500, blit=True)

    # Save the video in the same folder as the data files, named after the folder
    save_folder = os.path.dirname(all_plots[0]['file_name'])
    folder_name = os.path.basename(save_folder)
    video_filename = os.path.join(save_folder, f"{folder_name}_ARPES_video.mp4")
    
    # Use FFMpegWriter to save the animation
    writer = animation.FFMpegWriter(fps=2, metadata=dict(artist='Me'), bitrate=1800)
    anim.save(video_filename, writer=writer)
    
    plt.close(fig)
    print(f"Video saved as {video_filename}")

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    scan_types = determine_scan_type(data_files)
    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog
import tensorflow as tf
from tensorflow.keras import layers, models

get_ipython().run_line_magic('matplotlib', 'widget')

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()  # Sort files alphabetically
    return [os.path.join(folder_path, f) for f in data_files]

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def create_denoising_model(input_shape):
    model = models.Sequential([
        layers.Input(shape=input_shape),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.MaxPooling2D((2, 2), padding='same'),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.MaxPooling2D((2, 2), padding='same'),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.UpSampling2D((2, 2)),
        layers.Conv2D(32, (3, 3), activation='relu', padding='same'),
        layers.UpSampling2D((2, 2)),
        layers.Conv2D(1, (3, 3), activation='linear', padding='same')
    ])
    model.compile(optimizer='adam', loss='mse')
    return model

def train_denoising_model(data_files, work_function):
    all_data = []
    for file_path in data_files:
        data = read_single_pxt(file_path)
        data_values = data.values
        print(f"Shape of data from {file_path}: {data_values.shape}")
        
        # Ensure data is 3D
        if len(data_values.shape) == 2:
            data_values = data_values[:, :, np.newaxis]
        elif len(data_values.shape) != 3:
            print(f"Skipping {file_path} due to unexpected shape: {data_values.shape}")
            continue
        
        all_data.append(data_values)
    
    all_data = np.array(all_data)
    
    # Find the minimum dimensions across all data
    min_height = min(data.shape[0] for data in all_data)
    min_width = min(data.shape[1] for data in all_data)
    
    # Resize all data to the minimum dimensions
    resized_data = []
    for data in all_data:
        try:
            resized = tf.image.resize(data, (min_height, min_width)).numpy()
            resized_data.append(resized)
        except ValueError as e:
            print(f"Error resizing data with shape {data.shape}: {e}")
            continue
    resized_data = np.array(resized_data)
    
    # Normalize data
    resized_data = (resized_data - np.min(resized_data)) / (np.max(resized_data) - np.min(resized_data))
    
    # Add noise to create noisy-clean pairs
    noisy_data = resized_data + 0.1 * np.random.randn(*resized_data.shape)
    noisy_data = np.clip(noisy_data, 0, 1)
    
    # Reshape data for the model
    input_shape = resized_data.shape[1:]
    noisy_data = noisy_data.reshape((-1, *input_shape))
    clean_data = resized_data.reshape((-1, *input_shape))
    
    # Create and train the model
    model = create_denoising_model(input_shape)
    model.fit(noisy_data, clean_data, epochs=50, batch_size=32, validation_split=0.2)
    
    return model, (min_height, min_width)

def denoise_data(model, data, target_shape):
    # Resize data to target shape
    resized_data = tf.image.resize(data, target_shape).numpy()
    
    # Normalize data
    data_norm = (resized_data - np.min(resized_data)) / (np.max(resized_data) - np.min(resized_data))
    
    # Reshape for prediction
    data_reshaped = data_norm.reshape((1, *data_norm.shape))
    
    # Denoise
    denoised = model.predict(data_reshaped)
    
    # Reshape and denormalize
    denoised = denoised.reshape(data_norm.shape)
    denoised = denoised * (np.max(resized_data) - np.min(resized_data)) + np.min(resized_data)
    
    return denoised

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Train the denoising model
    denoising_model, target_shape = train_denoising_model(data_files, work_function)

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar
        energy_values = -data.eV.values

        # Denoise the data
        denoised_data = denoise_data(denoising_model, data.values, target_shape)

        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': denoised_data,
            'file_name': file_path
        })
        max_data_value = max(max_data_value, np.max(denoised_data))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    plot_data = all_plots[0]
    norm = Normalize(vmin=0, vmax=max_data_value)
    cmap_array = plt.cm.jet(np.linspace(0, 1, 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()

    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end):
        plot_data = all_plots[scan_index - 1]  # Convert scan_index to zero-based index
        norm = Normalize(vmin=vmin, vmax=vmax / scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
        im.set_array(plot_data['data_values'].ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
        fig.canvas.draw_idle()

    interactive_plot = interactive(update_plot,
                                   scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=True, layout=Layout(width='50%')),
                                   vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=True),
                                   vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=True),
                                   scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=True),
                                   cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=True),
                                   cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=True))

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: save_plot(b, interactive_plot, all_plots))

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(lambda b: make_video(b, interactive_plot, all_plots, data_files))

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])
    return output

def save_plot(b, interactive_plot, all_plots):
    # Get current slider values directly from the widgets
    current_values = {
        'scan_index': interactive_plot.children[0].value,
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Generate the plot with current values
    fig = plot_arpes(current_values, all_plots)

    # Get the current file name being plotted
    current_file = all_plots[current_values['scan_index'] - 1]['file_name']

    # Save the plot in the same folder as the data files
    save_folder = os.path.dirname(current_file)
    filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
    fig.savefig(filename, dpi=2000, bbox_inches='tight')
    plt.close(fig)
    print(f"Plot saved as {filename}")

def plot_arpes(current_values, all_plots):
    plot_data = all_plots[current_values['scan_index'] - 1]  # Convert scan_index to zero-based index
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())  # Adjust y-axis limits
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()
    return fig

def make_video(b, interactive_plot, all_plots, data_files):
    # Get current slider values directly from the widgets
    current_values = {
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Determine scan types
    scan_types = determine_scan_type(data_files)

    # Create the video
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    plot_data = all_plots[0]
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')

    def animate(i):
        plot_data = all_plots[i]
        im.set_array(plot_data['data_values'].ravel())
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                     fontsize=14, fontweight='bold')
        if scan_types[i][0] != 'unknown':
            ax.text(0.5, -0.1, f'{scan_types[i][0]}: {scan_types[i][1]:.2f}',
                    transform=ax.transAxes, color='black',
                    horizontalalignment='center', verticalalignment='top',
                    fontsize=10, fontweight='bold')
        return [im]

    anim = animation.FuncAnimation(fig, animate, frames=len(all_plots), interval=500, blit=True)

    # Save the video in the same folder as the data files, named after the folder
    save_folder = os.path.dirname(all_plots[0]['file_name'])
    folder_name = os.path.basename(save_folder)
    video_filename = os.path.join(save_folder, f"{folder_name}_ARPES_video.mp4")
    
    # Use FFMpegWriter to save the animation
    writer = animation.FFMpegWriter(fps=2, metadata=dict(artist='Me'), bitrate=1800)
    anim.save(video_filename, writer=writer)
    
    plt.close(fig)
    print(f"Video saved as {video_filename}")

# Usage
root = tk.Tk()
root.withdraw()
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    scan_types = determine_scan_type(data_files)
    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def convolve2d(image, kernel):
    # Simple 2D convolution function
    output = np.zeros_like(image)
    padded_image = np.pad(image, ((1, 1), (1, 1)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+3, j:j+3] * kernel)
    return output

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar
        energy_values = -data.eV.values
        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': file_path
        })
        max_data_value = max(max_data_value, np.max(data.values))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    plot_data = all_plots[0]
    norm = Normalize(vmin=0, vmax=max_data_value)
    cmap_array = plt.cm.jet(np.linspace(0, 1, 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()

    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end, vertical_edge, horizontal_edge):
        plot_data = all_plots[scan_index - 1]
        norm = Normalize(vmin=vmin, vmax=vmax / scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
        
        data_to_plot = plot_data['data_values']
        
        if vertical_edge:
            kernel = np.array([[-1, 0, 1],
                               [-2, 0, 2],
                               [-1, 0, 1]])
            data_to_plot = convolve2d(data_to_plot, kernel)
        elif horizontal_edge:
            kernel = np.array([[-1, -2, -1],
                               [0, 0, 0],
                               [1, 2, 1]])
            data_to_plot = convolve2d(data_to_plot, kernel)
        
        im.set_array(data_to_plot.ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
        fig.canvas.draw_idle()

    interactive_plot = interactive(
        update_plot,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
        vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
        vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
        scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
        cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
        cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False),
        vertical_edge=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_edge=Checkbox(value=False, description='Horizontal Edge Detection')
    )

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: save_plot(b, interactive_plot, all_plots))

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(lambda b: make_video(b, interactive_plot, all_plots, data_files))

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])
    return output

def save_plot(b, interactive_plot, all_plots):
    current_values = {
        'scan_index': interactive_plot.children[0].value,
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value,
        'vertical_edge': interactive_plot.children[6].value,
        'horizontal_edge': interactive_plot.children[7].value
    }
    fig = plot_arpes(current_values, all_plots)
    current_file = all_plots[current_values['scan_index'] - 1]['file_name']
    save_folder = os.path.dirname(current_file)
    filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
    fig.savefig(filename, dpi=2000, bbox_inches='tight')
    plt.close(fig)
    print(f"Plot saved as {filename}")

def plot_arpes(current_values, all_plots):
    plot_data = all_plots[current_values['scan_index'] - 1]
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    
    data_to_plot = plot_data['data_values']
    if current_values['vertical_edge']:
        kernel = np.array([[-1, 0, 1],
                           [-2, 0, 2],
                           [-1, 0, 1]])
        data_to_plot = convolve2d(data_to_plot, kernel)
    elif current_values['horizontal_edge']:
        kernel = np.array([[-1, -2, -1],
                           [0, 0, 0],
                           [1, 2, 1]])
        data_to_plot = convolve2d(data_to_plot, kernel)
    
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], data_to_plot, shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()
    return fig

def make_video(b, interactive_plot, all_plots, data_files):
    # Get current slider values directly from the widgets
    current_values = {
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Determine scan types
    scan_types = determine_scan_type(data_files)

    # Create the video
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    plot_data = all_plots[0]
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')

    def animate(i):
        plot_data = all_plots[i]
        im.set_array(plot_data['data_values'].ravel())
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                     fontsize=14, fontweight='bold')
        if scan_types[i][0] != 'unknown':
            ax.text(0.5, -0.1, f'{scan_types[i][0]}: {scan_types[i][1]:.2f}',
                    transform=ax.transAxes, color='black',
                    horizontalalignment='center', verticalalignment='top',
                    fontsize=10, fontweight='bold')
        return [im]

    anim = animation.FuncAnimation(fig, animate, frames=len(all_plots), interval=500, blit=True)

    # Save the video in the same folder as the data files, named after the folder
    save_folder = os.path.dirname(all_plots[0]['file_name'])
    folder_name = os.path.basename(save_folder)
    video_filename = os.path.join(save_folder, f"{folder_name}_ARPES_video.mp4")
    
    # Use FFMpegWriter to save the animation
    writer = animation.FFMpegWriter(fps=2, metadata=dict(artist='Me'), bitrate=1800)
    anim.save(video_filename, writer=writer)
    
    plt.close(fig)
    print(f"Video saved as {video_filename}")

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    scan_types = determine_scan_type(data_files)
    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar
        energy_values = -data.eV.values
        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': file_path
        })
        max_data_value = max(max_data_value, np.max(data.values))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    plot_data = all_plots[0]
    norm = Normalize(vmin=0, vmax=max_data_value)
    cmap_array = plt.cm.jet(np.linspace(0, 1, 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()

    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end, 
                    vertical_magnitude, horizontal_magnitude, kernel_size):
        plot_data = all_plots[scan_index - 1]
        norm = Normalize(vmin=vmin, vmax=vmax / scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
        
        data_to_plot = plot_data['data_values']
        
        # Create kernels with adjustable magnitude and size
        vertical_kernel = np.array([[-1, 0, 1],
                                    [-2, 0, 2],
                                    [-1, 0, 1]]) * vertical_magnitude
        horizontal_kernel = np.array([[-1, -2, -1],
                                      [0, 0, 0],
                                      [1, 2, 1]]) * horizontal_magnitude
        
        # Adjust kernel size
        if kernel_size > 3:
            vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
            horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
        
        # Apply convolutions
        vertical_output = convolve2d(data_to_plot, vertical_kernel)
        horizontal_output = convolve2d(data_to_plot, horizontal_kernel)
        
        # Combine outputs
        data_to_plot = vertical_output + horizontal_output
        
        im.set_array(data_to_plot.ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
        fig.canvas.draw_idle()

    interactive_plot = interactive(
        update_plot,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
        vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
        vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
        scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
        cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
        cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=9, step=2, description='Kernel Size', continuous_update=False)
    )

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: save_plot(b, interactive_plot, all_plots))

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(lambda b: make_video(b, interactive_plot, all_plots, data_files))

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])
    return output

def save_plot(b, interactive_plot, all_plots):
    current_values = {
        'scan_index': interactive_plot.children[0].value,
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value,
        'vertical_magnitude': interactive_plot.children[6].value,
        'horizontal_magnitude': interactive_plot.children[7].value,
        'kernel_size': interactive_plot.children[8].value
    }
    fig = plot_arpes(current_values, all_plots)
    current_file = all_plots[current_values['scan_index'] - 1]['file_name']
    save_folder = os.path.dirname(current_file)
    filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
    fig.savefig(filename, dpi=2000, bbox_inches='tight')
    plt.close(fig)
    print(f"Plot saved as {filename}")

def plot_arpes(current_values, all_plots):
    plot_data = all_plots[current_values['scan_index'] - 1]
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    
    data_to_plot = plot_data['data_values']
    
    # Create kernels with adjustable magnitude and size
    vertical_kernel = np.array([[-1, 0, 1],
                                [-2, 0, 2],
                                [-1, 0, 1]]) * current_values['vertical_magnitude']
    horizontal_kernel = np.array([[-1, -2, -1],
                                  [0, 0, 0],
                                  [1, 2, 1]]) * current_values['horizontal_magnitude']
    
    # Adjust kernel size
    if current_values['kernel_size'] > 3:
        vertical_kernel = np.repeat(np.repeat(vertical_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)
        horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)
    
    # Apply convolutions
    vertical_output = convolve2d(data_to_plot, vertical_kernel)
    horizontal_output = convolve2d(data_to_plot, horizontal_kernel)
    
    # Combine outputs
    data_to_plot = vertical_output + horizontal_output
    
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], data_to_plot, shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()
    return fig

def make_video(b, interactive_plot, all_plots, data_files):
    # Get current slider values directly from the widgets
    current_values = {
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Determine scan types
    scan_types = determine_scan_type(data_files)

    # Create the video
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    plot_data = all_plots[0]
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')

    def animate(i):
        plot_data = all_plots[i]
        im.set_array(plot_data['data_values'].ravel())
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                     fontsize=14, fontweight='bold')
        if scan_types[i][0] != 'unknown':
            ax.text(0.5, -0.1, f'{scan_types[i][0]}: {scan_types[i][1]:.2f}',
                    transform=ax.transAxes, color='black',
                    horizontalalignment='center', verticalalignment='top',
                    fontsize=10, fontweight='bold')
        return [im]

    anim = animation.FuncAnimation(fig, animate, frames=len(all_plots), interval=500, blit=True)

    # Save the video in the same folder as the data files, named after the folder
    save_folder = os.path.dirname(all_plots[0]['file_name'])
    folder_name = os.path.basename(save_folder)
    video_filename = os.path.join(save_folder, f"{folder_name}_ARPES_video.mp4")
    
    # Use FFMpegWriter to save the animation
    writer = animation.FFMpegWriter(fps=2, metadata=dict(artist='Me'), bitrate=1800)
    anim.save(video_filename, writer=writer)
    
    plt.close(fig)
    print(f"Video saved as {video_filename}")

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    scan_types = determine_scan_type(data_files)
    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar
        energy_values = -data.eV.values
        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': file_path
        })
        max_data_value = max(max_data_value, np.max(data.values))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    plot_data = all_plots[0]
    norm = Normalize(vmin=0, vmax=max_data_value)
    cmap_array = plt.cm.jet(np.linspace(0, 1, 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()

    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end, 
                    vertical_enabled, horizontal_enabled,
                    vertical_magnitude, horizontal_magnitude, kernel_size):
        plot_data = all_plots[scan_index - 1]
        norm = Normalize(vmin=vmin, vmax=vmax / scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
        
        data_to_plot = plot_data['data_values']
        
        # Create kernels with adjustable magnitude and size
        vertical_kernel = np.array([[-1, 0, 1],
                                    [-2, 0, 2],
                                    [-1, 0, 1]]) * vertical_magnitude
        horizontal_kernel = np.array([[-1, -2, -1],
                                      [0, 0, 0],
                                      [1, 2, 1]]) * horizontal_magnitude
        
        # Adjust kernel size
        if kernel_size > 3:
            vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
            horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
        
        # Apply convolutions if enabled
        if vertical_enabled:
            data_to_plot = convolve2d(data_to_plot, vertical_kernel)
        if horizontal_enabled:
            data_to_plot = convolve2d(data_to_plot, horizontal_kernel)
        
        im.set_array(data_to_plot.ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
        fig.canvas.draw_idle()

    interactive_plot = interactive(
        update_plot,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
        vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
        vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
        scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
        cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
        cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=3, max=9, step=2, description='Kernel Size', continuous_update=False)
    )

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: save_plot(b, interactive_plot, all_plots))

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(lambda b: make_video(b, interactive_plot, all_plots, data_files))

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])
    return output

def save_plot(b, interactive_plot, all_plots):
    current_values = {
        'scan_index': interactive_plot.children[0].value,
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value,
        'vertical_enabled': interactive_plot.children[6].value,
        'horizontal_enabled': interactive_plot.children[7].value,
        'vertical_magnitude': interactive_plot.children[8].value,
        'horizontal_magnitude': interactive_plot.children[9].value,
        'kernel_size': interactive_plot.children[10].value
    }
    fig = plot_arpes(current_values, all_plots)
    current_file = all_plots[current_values['scan_index'] - 1]['file_name']
    save_folder = os.path.dirname(current_file)
    filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
    fig.savefig(filename, dpi=2000, bbox_inches='tight')
    plt.close(fig)
    print(f"Plot saved as {filename}")

def plot_arpes(current_values, all_plots):
    plot_data = all_plots[current_values['scan_index'] - 1]
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    
    data_to_plot = plot_data['data_values']
    
    # Create kernels with adjustable magnitude and size
    vertical_kernel = np.array([[-1, 0, 1],
                                [-2, 0, 2],
                                [-1, 0, 1]]) * current_values['vertical_magnitude']
    horizontal_kernel = np.array([[-1, -2, -1],
                                  [0, 0, 0],
                                  [1, 2, 1]]) * current_values['horizontal_magnitude']
    
    # Adjust kernel size
    if current_values['kernel_size'] > 3:
        vertical_kernel = np.repeat(np.repeat(vertical_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)
        horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)
    
    # Apply convolutions if enabled
    if current_values['vertical_enabled']:
        data_to_plot = convolve2d(data_to_plot, vertical_kernel)
    if current_values['horizontal_enabled']:
        data_to_plot = convolve2d(data_to_plot, horizontal_kernel)
    
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], data_to_plot, shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()
    return fig

def make_video(b, interactive_plot, all_plots, data_files):
    # Get current slider values directly from the widgets
    current_values = {
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Determine scan types
    scan_types = determine_scan_type(data_files)

    # Create the video
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    plot_data = all_plots[0]
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')

    def animate(i):
        plot_data = all_plots[i]
        im.set_array(plot_data['data_values'].ravel())
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                     fontsize=14, fontweight='bold')
        if scan_types[i][0] != 'unknown':
            ax.text(0.5, -0.1, f'{scan_types[i][0]}: {scan_types[i][1]:.2f}',
                    transform=ax.transAxes, color='black',
                    horizontalalignment='center', verticalalignment='top',
                    fontsize=10, fontweight='bold')
        return [im]

    anim = animation.FuncAnimation(fig, animate, frames=len(all_plots), interval=500, blit=True)

    # Save the video in the same folder as the data files, named after the folder
    save_folder = os.path.dirname(all_plots[0]['file_name'])
    folder_name = os.path.basename(save_folder)
    video_filename = os.path.join(save_folder, f"{folder_name}_ARPES_video.mp4")
    
    # Use FFMpegWriter to save the animation
    writer = animation.FFMpegWriter(fps=2, metadata=dict(artist='Me'), bitrate=1800)
    anim.save(video_filename, writer=writer)
    
    plt.close(fig)
    print(f"Video saved as {video_filename}")

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    scan_types = determine_scan_type(data_files)
    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar
        energy_values = -data.eV.values
        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': file_path
        })
        max_data_value = max(max_data_value, np.max(data.values))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    plot_data = all_plots[0]
    norm = Normalize(vmin=0, vmax=max_data_value)
    cmap_array = plt.cm.jet(np.linspace(0, 1, 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()

    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end, 
                    vertical_enabled, horizontal_enabled,
                    vertical_magnitude, horizontal_magnitude, kernel_size):
        plot_data = all_plots[scan_index - 1]
        norm = Normalize(vmin=vmin, vmax=vmax / scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
        
        data_to_plot = plot_data['data_values']
        
        # Create kernels with adjustable magnitude and size
        vertical_kernel = np.array([[-1, 0, 1],
                                    [-2, 0, 2],
                                    [-1, 0, 1]]) * vertical_magnitude
        horizontal_kernel = np.array([[-1, -2, -1],
                                      [0, 0, 0],
                                      [1, 2, 1]]) * horizontal_magnitude
        
        # Adjust kernel size
        if kernel_size > 3:
            vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
            horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
        
        # Apply convolutions if enabled
        if vertical_enabled:
            data_to_plot = convolve2d(data_to_plot, vertical_kernel)
        if horizontal_enabled:
            data_to_plot = convolve2d(data_to_plot, horizontal_kernel)
        
        im.set_array(data_to_plot.ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
        fig.canvas.draw_idle()

    interactive_plot = interactive(
        update_plot,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
        vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
        vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
        scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
        cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
        cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=1, max=30, step=1, description='Kernel Size', continuous_update=False)
    )

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: save_plot(b, interactive_plot, all_plots))

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(lambda b: make_video(b, interactive_plot, all_plots, data_files))

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])
    return output

def save_plot(b, interactive_plot, all_plots):
    current_values = {
        'scan_index': interactive_plot.children[0].value,
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value,
        'vertical_enabled': interactive_plot.children[6].value,
        'horizontal_enabled': interactive_plot.children[7].value,
        'vertical_magnitude': interactive_plot.children[8].value,
        'horizontal_magnitude': interactive_plot.children[9].value,
        'kernel_size': interactive_plot.children[10].value
    }
    fig = plot_arpes(current_values, all_plots)
    current_file = all_plots[current_values['scan_index'] - 1]['file_name']
    save_folder = os.path.dirname(current_file)
    filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
    fig.savefig(filename, dpi=2000, bbox_inches='tight')
    plt.close(fig)
    print(f"Plot saved as {filename}")

def plot_arpes(current_values, all_plots):
    plot_data = all_plots[current_values['scan_index'] - 1]
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    
    data_to_plot = plot_data['data_values']
    
    # Create kernels with adjustable magnitude and size
    vertical_kernel = np.array([[-1, 0, 1],
                                [-2, 0, 2],
                                [-1, 0, 1]]) * current_values['vertical_magnitude']
    horizontal_kernel = np.array([[-1, -2, -1],
                                  [0, 0, 0],
                                  [1, 2, 1]]) * current_values['horizontal_magnitude']
    
    # Adjust kernel size
    if current_values['kernel_size'] > 3:
        vertical_kernel = np.repeat(np.repeat(vertical_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)
        horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)
    
    # Apply convolutions if enabled
    if current_values['vertical_enabled']:
        data_to_plot = convolve2d(data_to_plot, vertical_kernel)
    if current_values['horizontal_enabled']:
        data_to_plot = convolve2d(data_to_plot, horizontal_kernel)
    
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], data_to_plot, shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()
    return fig

def make_video(b, interactive_plot, all_plots, data_files):
    # Get current slider values directly from the widgets
    current_values = {
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Determine scan types
    scan_types = determine_scan_type(data_files)

    # Create the video
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    plot_data = all_plots[0]
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')

    def animate(i):
        plot_data = all_plots[i]
        im.set_array(plot_data['data_values'].ravel())
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                     fontsize=14, fontweight='bold')
        if scan_types[i][0] != 'unknown':
            ax.text(0.5, -0.1, f'{scan_types[i][0]}: {scan_types[i][1]:.2f}',
                    transform=ax.transAxes, color='black',
                    horizontalalignment='center', verticalalignment='top',
                    fontsize=10, fontweight='bold')
        return [im]

    anim = animation.FuncAnimation(fig, animate, frames=len(all_plots), interval=500, blit=True)

    # Save the video in the same folder as the data files, named after the folder
    save_folder = os.path.dirname(all_plots[0]['file_name'])
    folder_name = os.path.basename(save_folder)
    video_filename = os.path.join(save_folder, f"{folder_name}_ARPES_video.mp4")
    
    # Use FFMpegWriter to save the animation
    writer = animation.FFMpegWriter(fps=2, metadata=dict(artist='Me'), bitrate=1800)
    anim.save(video_filename, writer=writer)
    
    plt.close(fig)
    print(f"Video saved as {video_filename}")

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    scan_types = determine_scan_type(data_files)
    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.9": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display\nimport matplotlib.animation as animation\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\nimport tkinter as tk\nfrom tkinter import filedialog\n\n%matplotlib widget\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",\n    \"axes.labelcolor\": \"black\",\n})\n\n# Suppress specific warnings\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()\n    return [os.path.join(folder_path, f) for f in data_files]\n\ndef determine_scan_type(data_files):\n    scan_types = []\n    previous_polar = None\n    previous_hv = None\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        if 'polar' in data.attrs and 'hv' in data.attrs:\n            current_polar = data.attrs['polar']\n            current_hv = data.attrs['hv']\n            if previous_polar is not None and previous_hv is not None:\n                if current_polar != previous_polar:\n                    scan_types.append(('polar', current_polar))\n                elif current_hv != previous_hv:\n                    scan_types.append(('hv', current_hv))\n                else:\n                    scan_types.append(('unknown', None))\n            else:\n                scan_types.append(('unknown', None))\n            previous_polar = current_polar\n            previous_hv = current_hv\n        else:\n            scan_types.append(('unknown', None))\n    return scan_types\n\ndef convolve2d(image, kernel):\n    output = np.zeros_like(image)\n    pad = kernel.shape[0] // 2\n    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')\n    for i in range(image.shape[0]):\n        for j in range(image.shape[1]):\n            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)\n    return output\n\ndef kspace(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    max_data_value = 0\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']\n        E_b = work_function + np.abs(data.eV) - E_photon\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n        energy_values = -data.eV.values\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': file_path\n        })\n        max_data_value = max(max_data_value, np.max(data.values))\n\n    # Create the initial plot\n    fig, ax = plt.subplots(figsize=(10, 8))\n    plot_data = all_plots[0]\n    norm = Normalize(vmin=0, vmax=max_data_value)\n    cmap_array = plt.cm.jet(np.linspace(0, 1, 256))\n    cmap = ListedColormap(cmap_array)\n    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)\n    cbar = fig.colorbar(im, ax=ax)\n    cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n    cbar.ax.tick_params(labelsize=10)\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data[\"file_name\"])}', fontsize=14, fontweight='bold')\n    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())\n    ax.tick_params(axis='both', which='major', labelsize=10)\n    plt.tight_layout()\n\n    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end, \n                    vertical_enabled, horizontal_enabled,\n                    vertical_magnitude, horizontal_magnitude, kernel_size):\n        plot_data = all_plots[scan_index - 1]\n        norm = Normalize(vmin=vmin, vmax=vmax / scale)\n        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n        \n        data_to_plot = plot_data['data_values']\n        \n        # Create kernels with adjustable magnitude and size\n        vertical_kernel = np.array([[-1, 0, 1],\n                                    [-2, 0, 2],\n                                    [-1, 0, 1]]) * vertical_magnitude\n        horizontal_kernel = np.array([[-1, -2, -1],\n                                      [0, 0, 0],\n                                      [1, 2, 1]]) * horizontal_magnitude\n        \n        # Adjust kernel size\n        if kernel_size > 3:\n            vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)\n            horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)\n        \n        # Apply convolutions if enabled\n        if vertical_enabled:\n            data_to_plot = convolve2d(data_to_plot, vertical_kernel)\n        if horizontal_enabled:\n            data_to_plot = convolve2d(data_to_plot, horizontal_kernel)\n        \n        im.set_array(data_to_plot.ravel())\n        im.set_norm(norm)\n        im.set_cmap(cmap)\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data[\"file_name\"])}', fontsize=14, fontweight='bold')\n        fig.canvas.draw_idle()\n\n    interactive_plot = interactive(\n        update_plot,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),\n        vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),\n        vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),\n        scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),\n        cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),\n        cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False),\n        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),\n        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),\n        vertical_magnitude=FloatSlider(value=1.0, min=-5., max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),\n        horizontal_magnitude=FloatSlider(value=1.0, min=0.1, max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),\n        kernel_size=IntSlider(value=3, min=1, max=30, step=1, description='Kernel Size', continuous_update=False)\n    )\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(lambda b: save_plot(b, interactive_plot, all_plots))\n\n    # Create a video button\n    video_button = Button(description=\"Make Video\")\n    video_button.on_click(lambda b: make_video(b, interactive_plot, all_plots, data_files))\n\n    # Combine the interactive plot, save button, and video button\n    output = VBox([interactive_plot, save_button, video_button])\n    return output\n\ndef save_plot(b, interactive_plot, all_plots):\n    current_values = {\n        'scan_index': interactive_plot.children[0].value,\n        'vmin': interactive_plot.children[1].value,\n        'vmax': interactive_plot.children[2].value,\n        'scale': interactive_plot.children[3].value,\n        'cmap_start': interactive_plot.children[4].value,\n        'cmap_end': interactive_plot.children[5].value,\n        'vertical_enabled': interactive_plot.children[6].value,\n        'horizontal_enabled': interactive_plot.children[7].value,\n        'vertical_magnitude': interactive_plot.children[8].value,\n        'horizontal_magnitude': interactive_plot.children[9].value,\n        'kernel_size': interactive_plot.children[10].value\n    }\n    fig = plot_arpes(current_values, all_plots)\n    current_file = all_plots[current_values['scan_index'] - 1]['file_name']\n    save_folder = os.path.dirname(current_file)\n    filename = os.path.join(save_folder, f\"ARPES_plot_{os.path.basename(current_file)}.png\")\n    fig.savefig(filename, dpi=2000, bbox_inches='tight')\n    plt.close(fig)\n    print(f\"Plot saved as {filename}\")\n\ndef plot_arpes(current_values, all_plots):\n    plot_data = all_plots[current_values['scan_index'] - 1]\n    fig, ax = plt.subplots(figsize=(10, 8))\n    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])\n    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))\n    cmap = ListedColormap(cmap_array)\n    \n    data_to_plot = plot_data['data_values']\n    \n    # Create kernels with adjustable magnitude and size\n    vertical_kernel = np.array([[-1, 0, 1],\n                                [-2, 0, 2],\n                                [-1, 0, 1]]) * current_values['vertical_magnitude']\n    horizontal_kernel = np.array([[-1, -2, -1],\n                                  [0, 0, 0],\n                                  [1, 2, 1]]) * current_values['horizontal_magnitude']\n    \n    # Adjust kernel size\n    if current_values['kernel_size'] > 3:\n        vertical_kernel = np.repeat(np.repeat(vertical_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)\n        horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)\n    \n    # Apply convolutions if enabled\n    if current_values['vertical_enabled']:\n        data_to_plot = convolve2d(data_to_plot, vertical_kernel)\n    if current_values['horizontal_enabled']:\n        data_to_plot = convolve2d(data_to_plot, horizontal_kernel)\n    \n    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], data_to_plot, shading='auto', cmap=cmap, norm=norm)\n    cbar = fig.colorbar(im, ax=ax)\n    cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n    cbar.ax.tick_params(labelsize=10)\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data[\"file_name\"])}', fontsize=14, fontweight='bold')\n    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())\n    ax.tick_params(axis='both', which='major', labelsize=10)\n    plt.tight_layout()\n    return fig\n\ndef make_video(b, interactive_plot, all_plots, data_files):\n    # Get current slider values directly from the widgets\n    current_values = {\n        'vmin': interactive_plot.children[1].value,\n        'vmax': interactive_plot.children[2].value,\n        'scale': interactive_plot.children[3].value,\n        'cmap_start': interactive_plot.children[4].value,\n        'cmap_end': interactive_plot.children[5].value\n    }\n\n    # Determine scan types\n    scan_types = determine_scan_type(data_files)\n\n    # Create the video\n    fig, ax = plt.subplots(figsize=(10, 8))\n    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])\n    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))\n    cmap = ListedColormap(cmap_array)\n    plot_data = all_plots[0]\n    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], \n                       shading='auto', cmap=cmap, norm=norm)\n    cbar = fig.colorbar(im, ax=ax)\n    cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n    cbar.ax.tick_params(labelsize=10)\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n    ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')\n\n    def animate(i):\n        plot_data = all_plots[i]\n        im.set_array(plot_data['data_values'].ravel())\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data[\"file_name\"])}', \n                     fontsize=14, fontweight='bold')\n        if scan_types[i][0] != 'unknown':\n            ax.text(0.5, -0.1, f'{scan_types[i][0]}: {scan_types[i][1]:.2f}',\n                    transform=ax.transAxes, color='black',\n                    horizontalalignment='center', verticalalignment='top',\n                    fontsize=10, fontweight='bold')\n        return [im]\n\n    anim = animation.FuncAnimation(fig, animate, frames=len(all_plots), interval=500, blit=True)\n\n    # Save the video in the same folder as the data files, named after the folder\n    save_folder = os.path.dirname(all_plots[0]['file_name'])\n    folder_name = os.path.basename(save_folder)\n    video_filename = os.path.join(save_folder, f\"{folder_name}_ARPES_video.mp4\")\n    \n    # Use FFMpegWriter to save the animation\n    writer = animation.FFMpegWriter(fps=2, metadata=dict(artist='Me'), bitrate=1800)\n    anim.save(video_filename, writer=writer)\n    \n    plt.close(fig)\n    print(f\"Video saved as {video_filename}\")\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()  # Hide the root window\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()  # Destroy the root window\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n    scan_types = determine_scan_type(data_files)\n    interactive_plot_with_save = kspace(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")", 7219)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.9.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.9.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.10": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import os\nimport numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox\nfrom matplotlib.colors import Normalize, ListedColormap\nfrom IPython.display import display\nimport matplotlib.animation as animation\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\nimport warnings\nfrom matplotlib import MatplotlibDeprecationWarning\nimport tkinter as tk\nfrom tkinter import filedialog\n\n%matplotlib widget\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\n# Set the font family for all text elements\nplt.rcParams['font.family'] = 'sans-serif'\nplt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']\nplt.rcParams.update({\n    \"text.usetex\": True,\n    \"font.family\": \"sans-serif\",\n    \"font.sans-serif\": \"Helvetica\",\n    \"text.color\": \"black\",\n    \"axes.labelcolor\": \"black\",\n})\n\n# Suppress specific warnings\nwarnings.filterwarnings(\"ignore\", category=UserWarning, message=\"The input coordinates to pcolormesh are interpreted as cell centers*\")\nwarnings.filterwarnings(\"ignore\", category=MatplotlibDeprecationWarning)\n\ndef load_data_files(folder_path):\n    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]\n    data_files.sort()\n    return [os.path.join(folder_path, f) for f in data_files]\n\ndef determine_scan_type(data_files):\n    scan_types = []\n    previous_polar = None\n    previous_hv = None\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        if 'polar' in data.attrs and 'hv' in data.attrs:\n            current_polar = data.attrs['polar']\n            current_hv = data.attrs['hv']\n            if previous_polar is not None and previous_hv is not None:\n                if current_polar != previous_polar:\n                    scan_types.append(('polar', current_polar))\n                elif current_hv != previous_hv:\n                    scan_types.append(('hv', current_hv))\n                else:\n                    scan_types.append(('unknown', None))\n            else:\n                scan_types.append(('unknown', None))\n            previous_polar = current_polar\n            previous_hv = current_hv\n        else:\n            scan_types.append(('unknown', None))\n    return scan_types\n\ndef convolve2d(image, kernel):\n    output = np.zeros_like(image)\n    pad = kernel.shape[0] // 2\n    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')\n    for i in range(image.shape[0]):\n        for j in range(image.shape[1]):\n            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)\n    return output\n\ndef kspace(data_files, work_function):\n    # Constants\n    hbar = 6.582119569e-16  # eV*s\n    m_e = 9.1093837015e-31  # kg\n\n    # Pre-calculate all plots\n    all_plots = []\n    max_data_value = 0\n    for file_path in data_files:\n        data = read_single_pxt(file_path)\n        E_photon = data.attrs['hv']\n        E_b = work_function + np.abs(data.eV) - E_photon\n        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n        energy_values = -data.eV.values\n        all_plots.append({\n            'k_parallel': k_parallel,\n            'energy_values': energy_values,\n            'data_values': data.values,\n            'file_name': file_path\n        })\n        max_data_value = max(max_data_value, np.max(data.values))\n\n    # Create the initial plot\n    fig, ax = plt.subplots(figsize=(10, 8))\n    plot_data = all_plots[0]\n    norm = Normalize(vmin=0, vmax=max_data_value)\n    cmap_array = plt.cm.jet(np.linspace(0, 1, 256))\n    cmap = ListedColormap(cmap_array)\n    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)\n    cbar = fig.colorbar(im, ax=ax)\n    cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n    cbar.ax.tick_params(labelsize=10)\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data[\"file_name\"])}', fontsize=14, fontweight='bold')\n    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())\n    ax.tick_params(axis='both', which='major', labelsize=10)\n    plt.tight_layout()\n\n    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end, \n                    vertical_enabled, horizontal_enabled,\n                    vertical_magnitude, horizontal_magnitude, kernel_size):\n        plot_data = all_plots[scan_index - 1]\n        norm = Normalize(vmin=vmin, vmax=vmax / scale)\n        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n        cmap = ListedColormap(cmap_array)\n        \n        data_to_plot = plot_data['data_values']\n        \n        # Create kernels with adjustable magnitude and size\n        vertical_kernel = np.array([[-1, 0, 1],\n                                    [-2, 0, 2],\n                                    [-1, 0, 1]]) * vertical_magnitude\n        horizontal_kernel = np.array([[-1, -2, -1],\n                                      [0, 0, 0],\n                                      [1, 2, 1]]) * horizontal_magnitude\n        \n        # Adjust kernel size\n        if kernel_size > 3:\n            vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)\n            horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)\n        \n        # Apply convolutions if enabled\n        if vertical_enabled:\n            data_to_plot = convolve2d(data_to_plot, vertical_kernel)\n        if horizontal_enabled:\n            data_to_plot = convolve2d(data_to_plot, horizontal_kernel)\n        \n        im.set_array(data_to_plot.ravel())\n        im.set_norm(norm)\n        im.set_cmap(cmap)\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data[\"file_name\"])}', fontsize=14, fontweight='bold')\n        fig.canvas.draw_idle()\n\n    interactive_plot = interactive(\n        update_plot,\n        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),\n        vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),\n        vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),\n        scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),\n        cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),\n        cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False),\n        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),\n        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),\n        vertical_magnitude=FloatSlider(value=1.0, min=-5.0, max=5.0, step=0.1, description='Vertical Magnitude', continuous_update=False),\n        horizontal_magnitude=FloatSlider(value=1.0, min=-5., max=5.0, step=0.1, description='Horizontal Magnitude', continuous_update=False),\n        kernel_size=IntSlider(value=3, min=1, max=30, step=1, description='Kernel Size', continuous_update=False)\n    )\n\n    # Create a save button\n    save_button = Button(description=\"Save Plot\")\n    save_button.on_click(lambda b: save_plot(b, interactive_plot, all_plots))\n\n    # Create a video button\n    video_button = Button(description=\"Make Video\")\n    video_button.on_click(lambda b: make_video(b, interactive_plot, all_plots, data_files))\n\n    # Combine the interactive plot, save button, and video button\n    output = VBox([interactive_plot, save_button, video_button])\n    return output\n\ndef save_plot(b, interactive_plot, all_plots):\n    current_values = {\n        'scan_index': interactive_plot.children[0].value,\n        'vmin': interactive_plot.children[1].value,\n        'vmax': interactive_plot.children[2].value,\n        'scale': interactive_plot.children[3].value,\n        'cmap_start': interactive_plot.children[4].value,\n        'cmap_end': interactive_plot.children[5].value,\n        'vertical_enabled': interactive_plot.children[6].value,\n        'horizontal_enabled': interactive_plot.children[7].value,\n        'vertical_magnitude': interactive_plot.children[8].value,\n        'horizontal_magnitude': interactive_plot.children[9].value,\n        'kernel_size': interactive_plot.children[10].value\n    }\n    fig = plot_arpes(current_values, all_plots)\n    current_file = all_plots[current_values['scan_index'] - 1]['file_name']\n    save_folder = os.path.dirname(current_file)\n    filename = os.path.join(save_folder, f\"ARPES_plot_{os.path.basename(current_file)}.png\")\n    fig.savefig(filename, dpi=2000, bbox_inches='tight')\n    plt.close(fig)\n    print(f\"Plot saved as {filename}\")\n\ndef plot_arpes(current_values, all_plots):\n    plot_data = all_plots[current_values['scan_index'] - 1]\n    fig, ax = plt.subplots(figsize=(10, 8))\n    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])\n    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))\n    cmap = ListedColormap(cmap_array)\n    \n    data_to_plot = plot_data['data_values']\n    \n    # Create kernels with adjustable magnitude and size\n    vertical_kernel = np.array([[-1, 0, 1],\n                                [-2, 0, 2],\n                                [-1, 0, 1]]) * current_values['vertical_magnitude']\n    horizontal_kernel = np.array([[-1, -2, -1],\n                                  [0, 0, 0],\n                                  [1, 2, 1]]) * current_values['horizontal_magnitude']\n    \n    # Adjust kernel size\n    if current_values['kernel_size'] > 3:\n        vertical_kernel = np.repeat(np.repeat(vertical_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)\n        horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)\n    \n    # Apply convolutions if enabled\n    if current_values['vertical_enabled']:\n        data_to_plot = convolve2d(data_to_plot, vertical_kernel)\n    if current_values['horizontal_enabled']:\n        data_to_plot = convolve2d(data_to_plot, horizontal_kernel)\n    \n    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], data_to_plot, shading='auto', cmap=cmap, norm=norm)\n    cbar = fig.colorbar(im, ax=ax)\n    cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n    cbar.ax.tick_params(labelsize=10)\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data[\"file_name\"])}', fontsize=14, fontweight='bold')\n    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())\n    ax.tick_params(axis='both', which='major', labelsize=10)\n    plt.tight_layout()\n    return fig\n\ndef make_video(b, interactive_plot, all_plots, data_files):\n    # Get current slider values directly from the widgets\n    current_values = {\n        'vmin': interactive_plot.children[1].value,\n        'vmax': interactive_plot.children[2].value,\n        'scale': interactive_plot.children[3].value,\n        'cmap_start': interactive_plot.children[4].value,\n        'cmap_end': interactive_plot.children[5].value\n    }\n\n    # Determine scan types\n    scan_types = determine_scan_type(data_files)\n\n    # Create the video\n    fig, ax = plt.subplots(figsize=(10, 8))\n    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])\n    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))\n    cmap = ListedColormap(cmap_array)\n    plot_data = all_plots[0]\n    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], \n                       shading='auto', cmap=cmap, norm=norm)\n    cbar = fig.colorbar(im, ax=ax)\n    cbar.set_label('Intensity', fontsize=12, fontweight='bold')\n    cbar.ax.tick_params(labelsize=10)\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')\n    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')\n    ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')\n\n    def animate(i):\n        plot_data = all_plots[i]\n        im.set_array(plot_data['data_values'].ravel())\n        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data[\"file_name\"])}', \n                     fontsize=14, fontweight='bold')\n        if scan_types[i][0] != 'unknown':\n            ax.text(0.5, -0.1, f'{scan_types[i][0]}: {scan_types[i][1]:.2f}',\n                    transform=ax.transAxes, color='black',\n                    horizontalalignment='center', verticalalignment='top',\n                    fontsize=10, fontweight='bold')\n        return [im]\n\n    anim = animation.FuncAnimation(fig, animate, frames=len(all_plots), interval=500, blit=True)\n\n    # Save the video in the same folder as the data files, named after the folder\n    save_folder = os.path.dirname(all_plots[0]['file_name'])\n    folder_name = os.path.basename(save_folder)\n    video_filename = os.path.join(save_folder, f\"{folder_name}_ARPES_video.mp4\")\n    \n    # Use FFMpegWriter to save the animation\n    writer = animation.FFMpegWriter(fps=2, metadata=dict(artist='Me'), bitrate=1800)\n    anim.save(video_filename, writer=writer)\n    \n    plt.close(fig)\n    print(f\"Video saved as {video_filename}\")\n\n# Usage\nroot = tk.Tk()\nroot.withdraw()  # Hide the root window\nfolder_path = filedialog.askdirectory(title=\"Select Folder Containing Data Files\")\nroot.destroy()  # Destroy the root window\n\nif folder_path:\n    data_files = load_data_files(folder_path)\n    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n    scan_types = determine_scan_type(data_files)\n    interactive_plot_with_save = kspace(data_files, work_function)\n    display(interactive_plot_with_save)\nelse:\n    print(\"No folder selected.\")", 7360)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.10.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.10.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar
        energy_values = -data.eV.values
        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': file_path
        })
        max_data_value = max(max_data_value, np.max(data.values))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    plot_data = all_plots[0]
    norm = Normalize(vmin=0, vmax=max_data_value)
    cmap_array = plt.cm.jet(np.linspace(0, 1, 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()

    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end, 
                    vertical_enabled, horizontal_enabled,
                    vertical_magnitude, horizontal_magnitude, kernel_size):
        plot_data = all_plots[scan_index - 1]
        norm = Normalize(vmin=vmin, vmax=vmax / scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
        
        data_to_plot = plot_data['data_values']
        
        # Create kernels with adjustable magnitude and size
        vertical_kernel = np.array([[-1, 0, 1],
                                    [-2, 0, 2],
                                    [-1, 0, 1]]) * vertical_magnitude
        horizontal_kernel = np.array([[-1, -2, -1],
                                      [0, 0, 0],
                                      [1, 2, 1]]) * horizontal_magnitude
        
        # Adjust kernel size
        if kernel_size > 3:
            vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
            horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
        
        # Apply convolutions if enabled
        if vertical_enabled:
            data_to_plot = convolve2d(data_to_plot, vertical_kernel)
        if horizontal_enabled:
            data_to_plot = convolve2d(data_to_plot, horizontal_kernel)
        
        im.set_array(data_to_plot.ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
        fig.canvas.draw_idle()

    interactive_plot = interactive(
        update_plot,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
        vmin=IntSlider(value=0, min=0, max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
        vmax=IntSlider(value=int(max_data_value), min=0, max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
        scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
        cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
        cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=-5.0, max=5.0, step=0.01, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=-5.0, max=5.0, step=0.01, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=1, max=30, step=1, description='Kernel Size', continuous_update=False)
    )

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: save_plot(b, interactive_plot, all_plots))

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(lambda b: make_video(b, interactive_plot, all_plots, data_files))

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])
    return output

def save_plot(b, interactive_plot, all_plots):
    current_values = {
        'scan_index': interactive_plot.children[0].value,
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value,
        'vertical_enabled': interactive_plot.children[6].value,
        'horizontal_enabled': interactive_plot.children[7].value,
        'vertical_magnitude': interactive_plot.children[8].value,
        'horizontal_magnitude': interactive_plot.children[9].value,
        'kernel_size': interactive_plot.children[10].value
    }
    fig = plot_arpes(current_values, all_plots)
    current_file = all_plots[current_values['scan_index'] - 1]['file_name']
    save_folder = os.path.dirname(current_file)
    filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
    fig.savefig(filename, dpi=2000, bbox_inches='tight')
    plt.close(fig)
    print(f"Plot saved as {filename}")

def plot_arpes(current_values, all_plots):
    plot_data = all_plots[current_values['scan_index'] - 1]
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    
    data_to_plot = plot_data['data_values']
    
    # Create kernels with adjustable magnitude and size
    vertical_kernel = np.array([[-1, 0, 1],
                                [-2, 0, 2],
                                [-1, 0, 1]]) * current_values['vertical_magnitude']
    horizontal_kernel = np.array([[-1, -2, -1],
                                  [0, 0, 0],
                                  [1, 2, 1]]) * current_values['horizontal_magnitude']
    
    # Adjust kernel size
    if current_values['kernel_size'] > 3:
        vertical_kernel = np.repeat(np.repeat(vertical_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)
        horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)
    
    # Apply convolutions if enabled
    if current_values['vertical_enabled']:
        data_to_plot = convolve2d(data_to_plot, vertical_kernel)
    if current_values['horizontal_enabled']:
        data_to_plot = convolve2d(data_to_plot, horizontal_kernel)
    
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], data_to_plot, shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()
    return fig

def make_video(b, interactive_plot, all_plots, data_files):
    # Get current slider values directly from the widgets
    current_values = {
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Determine scan types
    scan_types = determine_scan_type(data_files)

    # Create the video
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    plot_data = all_plots[0]
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')

    def animate(i):
        plot_data = all_plots[i]
        im.set_array(plot_data['data_values'].ravel())
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                     fontsize=14, fontweight='bold')
        if scan_types[i][0] != 'unknown':
            ax.text(0.5, -0.1, f'{scan_types[i][0]}: {scan_types[i][1]:.2f}',
                    transform=ax.transAxes, color='black',
                    horizontalalignment='center', verticalalignment='top',
                    fontsize=10, fontweight='bold')
        return [im]

    anim = animation.FuncAnimation(fig, animate, frames=len(all_plots), interval=500, blit=True)

    # Save the video in the same folder as the data files, named after the folder
    save_folder = os.path.dirname(all_plots[0]['file_name'])
    folder_name = os.path.basename(save_folder)
    video_filename = os.path.join(save_folder, f"{folder_name}_ARPES_video.mp4")
    
    # Use FFMpegWriter to save the animation
    writer = animation.FFMpegWriter(fps=2, metadata=dict(artist='Me'), bitrate=1800)
    anim.save(video_filename, writer=writer)
    
    plt.close(fig)
    print(f"Video saved as {video_filename}")

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    scan_types = determine_scan_type(data_files)
    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox
from matplotlib.colors import Normalize, ListedColormap
from IPython.display import display
import matplotlib.animation as animation
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
import warnings
from matplotlib import MatplotlibDeprecationWarning
import tkinter as tk
from tkinter import filedialog

get_ipython().run_line_magic('matplotlib', 'widget')

if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Set the font family for all text elements
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Helvetica', 'Arial', 'Lucida Grande', 'Verdana']
plt.rcParams.update({
    "text.usetex": True,
    "font.family": "sans-serif",
    "font.sans-serif": "Helvetica",
    "text.color": "black",
    "axes.labelcolor": "black",
})

# Suppress specific warnings
warnings.filterwarnings("ignore", category=UserWarning, message="The input coordinates to pcolormesh are interpreted as cell centers*")
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def convolve2d(image, kernel):
    output = np.zeros_like(image)
    pad = kernel.shape[0] // 2
    padded_image = np.pad(image, ((pad, pad), (pad, pad)), mode='edge')
    for i in range(image.shape[0]):
        for j in range(image.shape[1]):
            output[i, j] = np.sum(padded_image[i:i+kernel.shape[0], j:j+kernel.shape[1]] * kernel)
    return output

def kspace(data_files, work_function):
    # Constants
    hbar = 6.582119569e-16  # eV*s
    m_e = 9.1093837015e-31  # kg

    # Pre-calculate all plots
    all_plots = []
    max_data_value = 0
    for file_path in data_files:
        data = read_single_pxt(file_path)
        E_photon = data.attrs['hv']
        E_b = work_function + np.abs(data.eV) - E_photon
        k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar
        energy_values = -data.eV.values
        all_plots.append({
            'k_parallel': k_parallel,
            'energy_values': energy_values,
            'data_values': data.values,
            'file_name': file_path
        })
        max_data_value = max(max_data_value, np.max(data.values))

    # Create the initial plot
    fig, ax = plt.subplots(figsize=(10, 8))
    plot_data = all_plots[0]
    norm = Normalize(vmin=0, vmax=max_data_value)
    cmap_array = plt.cm.jet(np.linspace(0, 1, 256))
    cmap = ListedColormap(cmap_array)
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()

    def update_plot(scan_index, vmin, vmax, scale, cmap_start, cmap_end, 
                    vertical_enabled, horizontal_enabled,
                    vertical_magnitude, horizontal_magnitude, kernel_size):
        plot_data = all_plots[scan_index - 1]
        norm = Normalize(vmin=vmin, vmax=vmax / scale)
        cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
        cmap = ListedColormap(cmap_array)
        
        data_to_plot = plot_data['data_values']
        
        # Create kernels with adjustable magnitude and size
        vertical_kernel = np.array([[-1, 0, 1],
                                    [-2, 0, 2],
                                    [-1, 0, 1]]) * vertical_magnitude
        horizontal_kernel = np.array([[-1, -2, -1],
                                      [0, 0, 0],
                                      [1, 2, 1]]) * horizontal_magnitude
        
        # Adjust kernel size
        if kernel_size > 3:
            vertical_kernel = np.repeat(np.repeat(vertical_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
            horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, kernel_size // 3, axis=0), kernel_size // 3, axis=1)
        
        # Apply convolutions if enabled
        if vertical_enabled:
            data_to_plot = convolve2d(data_to_plot, vertical_kernel)
        if horizontal_enabled:
            data_to_plot = convolve2d(data_to_plot, horizontal_kernel)
        
        im.set_array(data_to_plot.ravel())
        im.set_norm(norm)
        im.set_cmap(cmap)
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
        fig.canvas.draw_idle()

    interactive_plot = interactive(
        update_plot,
        scan_index=IntSlider(value=1, min=1, max=len(data_files), step=1, description='Scan Index', continuous_update=False, layout=Layout(width='50%')),
        vmin=IntSlider(value=0, min=-int(max_data_value), max=int(max_data_value), step=1, description='Min Value', continuous_update=False),
        vmax=IntSlider(value=int(max_data_value), min=-int(max_data_value), max=int(max_data_value), step=1, description='Max Value', continuous_update=False),
        scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, description='Scale', continuous_update=False),
        cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, description='Cmap Start', continuous_update=False),
        cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, description='Cmap End', continuous_update=False),
        vertical_enabled=Checkbox(value=False, description='Vertical Edge Detection'),
        horizontal_enabled=Checkbox(value=False, description='Horizontal Edge Detection'),
        vertical_magnitude=FloatSlider(value=1.0, min=-5.0, max=5.0, step=0.01, description='Vertical Magnitude', continuous_update=False),
        horizontal_magnitude=FloatSlider(value=1.0, min=-5.0, max=5.0, step=0.01, description='Horizontal Magnitude', continuous_update=False),
        kernel_size=IntSlider(value=3, min=1, max=30, step=1, description='Kernel Size', continuous_update=False)
    )

    # Create a save button
    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: save_plot(b, interactive_plot, all_plots))

    # Create a video button
    video_button = Button(description="Make Video")
    video_button.on_click(lambda b: make_video(b, interactive_plot, all_plots, data_files))

    # Combine the interactive plot, save button, and video button
    output = VBox([interactive_plot, save_button, video_button])
    return output

def save_plot(b, interactive_plot, all_plots):
    current_values = {
        'scan_index': interactive_plot.children[0].value,
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value,
        'vertical_enabled': interactive_plot.children[6].value,
        'horizontal_enabled': interactive_plot.children[7].value,
        'vertical_magnitude': interactive_plot.children[8].value,
        'horizontal_magnitude': interactive_plot.children[9].value,
        'kernel_size': interactive_plot.children[10].value
    }
    fig = plot_arpes(current_values, all_plots)
    current_file = all_plots[current_values['scan_index'] - 1]['file_name']
    save_folder = os.path.dirname(current_file)
    filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
    fig.savefig(filename, dpi=2000, bbox_inches='tight')
    plt.close(fig)
    print(f"Plot saved as {filename}")

def plot_arpes(current_values, all_plots):
    plot_data = all_plots[current_values['scan_index'] - 1]
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    
    data_to_plot = plot_data['data_values']
    
    # Create kernels with adjustable magnitude and size
    vertical_kernel = np.array([[-1, 0, 1],
                                [-2, 0, 2],
                                [-1, 0, 1]]) * current_values['vertical_magnitude']
    horizontal_kernel = np.array([[-1, -2, -1],
                                  [0, 0, 0],
                                  [1, 2, 1]]) * current_values['horizontal_magnitude']
    
    # Adjust kernel size
    if current_values['kernel_size'] > 3:
        vertical_kernel = np.repeat(np.repeat(vertical_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)
        horizontal_kernel = np.repeat(np.repeat(horizontal_kernel, current_values['kernel_size'] // 3, axis=0), current_values['kernel_size'] // 3, axis=1)
    
    # Apply convolutions if enabled
    if current_values['vertical_enabled']:
        data_to_plot = convolve2d(data_to_plot, vertical_kernel)
    if current_values['horizontal_enabled']:
        data_to_plot = convolve2d(data_to_plot, horizontal_kernel)
    
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], data_to_plot, shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', fontsize=14, fontweight='bold')
    ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
    ax.tick_params(axis='both', which='major', labelsize=10)
    plt.tight_layout()
    return fig

def make_video(b, interactive_plot, all_plots, data_files):
    # Get current slider values directly from the widgets
    current_values = {
        'vmin': interactive_plot.children[1].value,
        'vmax': interactive_plot.children[2].value,
        'scale': interactive_plot.children[3].value,
        'cmap_start': interactive_plot.children[4].value,
        'cmap_end': interactive_plot.children[5].value
    }

    # Determine scan types
    scan_types = determine_scan_type(data_files)

    # Create the video
    fig, ax = plt.subplots(figsize=(10, 8))
    norm = Normalize(vmin=current_values['vmin'], vmax=current_values['vmax'] / current_values['scale'])
    cmap_array = plt.cm.jet(np.linspace(current_values['cmap_start'], current_values['cmap_end'], 256))
    cmap = ListedColormap(cmap_array)
    plot_data = all_plots[0]
    im = ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], plot_data['data_values'], 
                       shading='auto', cmap=cmap, norm=norm)
    cbar = fig.colorbar(im, ax=ax)
    cbar.set_label('Intensity', fontsize=12, fontweight='bold')
    cbar.ax.tick_params(labelsize=10)
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title('ARPES Data in Momentum Space', fontsize=14, fontweight='bold')

    def animate(i):
        plot_data = all_plots[i]
        im.set_array(plot_data['data_values'].ravel())
        ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                     fontsize=14, fontweight='bold')
        if scan_types[i][0] != 'unknown':
            ax.text(0.5, -0.1, f'{scan_types[i][0]}: {scan_types[i][1]:.2f}',
                    transform=ax.transAxes, color='black',
                    horizontalalignment='center', verticalalignment='top',
                    fontsize=10, fontweight='bold')
        return [im]

    anim = animation.FuncAnimation(fig, animate, frames=len(all_plots), interval=500, blit=True)

    # Save the video in the same folder as the data files, named after the folder
    save_folder = os.path.dirname(all_plots[0]['file_name'])
    folder_name = os.path.basename(save_folder)
    video_filename = os.path.join(save_folder, f"{folder_name}_ARPES_video.mp4")
    
    # Use FFMpegWriter to save the animation
    writer = animation.FFMpegWriter(fps=2, metadata=dict(artist='Me'), bitrate=1800)
    anim.save(video_filename, writer=writer)
    
    plt.close(fig)
    print(f"Video saved as {video_filename}")

# Usage
root = tk.Tk()
root.withdraw()  # Hide the root window
folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
root.destroy()  # Destroy the root window

if folder_path:
    data_files = load_data_files(folder_path)
    work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)
    scan_types = determine_scan_type(data_files)
    interactive_plot_with_save = kspace(data_files, work_function)
    display(interactive_plot_with_save)
else:
    print("No folder selected.")
