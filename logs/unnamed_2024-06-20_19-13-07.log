# IPython log file

import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = E_photon - work_function - np.abs(data.eV)
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.invert_yaxis()
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = -(E_photon - work_function - np.abs(data.eV))
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.invert_yaxis()
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = E_photon - work_function - np.abs(data.eV)
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.invert_yaxis()
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

# Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Binding Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

# Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Binding Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider
import matplotlib.colors as colors

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

# Define the plotting function
def plot_arpes(vmin, scale, vmin_color, vmax_color):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    norm = colors.Normalize(vmin=vmin_color, vmax=vmax_color)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale, norm=norm)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Binding Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               vmin_color=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax_color=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider
import matplotlib.colors as colors

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

# Define the plotting function
def plot_arpes(vmin, scale, vmin_color, vmax_color):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    norm = colors.Normalize(vmin=vmin_color, vmax=vmax_color)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale, norm=norm)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Binding Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               vmin_color=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax_color=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider
import matplotlib.colors as colors

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

# Define the plotting function
def plot_arpes(vmin, scale, vmin_color, vmax_color):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    norm = colors.Normalize(vmin=vmin_color, vmax=vmax_color)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale, norm=norm)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Binding Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               vmin_color=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax_color=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider
import matplotlib.colors as colors

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values  # Negative energy values

# Define the plotting function
def plot_arpes(vmin, scale, vmin_color, vmax_color):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    norm = colors.Normalize(vmin=vmin_color, vmax=vmax_color)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale, norm=norm)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Binding Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               vmin_color=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax_color=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = E_photon - work_function - np.abs(data.eV)
k_parallel = np.sqrt(2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = data.eV.values

# Define the plotting function
def plot_arpes(vmin, scale, vmin_color, vmax_color):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    norm = colors.Normalize(vmin=vmin_color, vmax=vmax_color)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale, norm=norm)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Binding Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               vmin_color=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax_color=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

# Define the plotting function
def plot_arpes(vmin, scale, vmin_color, vmax_color):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    norm = colors.Normalize(vmin=vmin_color, vmax=vmax_color)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale, norm=norm)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Binding Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               vmin_color=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax_color=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

# Define the plotting function
def plot_arpes(vmin, scale, vmin_color, vmax_color):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    norm = colors.Normalize(vmin=vmin_color, vmax=vmax_color)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale, norm=norm)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Binding Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               vmin_color=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax_color=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

# Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Binding Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.0": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n# Define the plotting function\ndef plot_arpes(vmin, scale):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    vmax = np.max(data.values)\n    im = ax.pcolormesh(k_parallel, E, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel('k_parallel (Å^-1)')\n    ax.set_ylabel('Binding Energy (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))\ninteractive_plot", 1122)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.0.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.0.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

# Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, E_b, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Binding Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

# Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Binding Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Binding Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.1": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = E  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, scale):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    vmax = np.max(data.values)\n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel('k_parallel (Å^-1)')\n    ax.set_ylabel('Binding Energy (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))\ninteractive_plot", 909)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.1.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.1.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.2": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = E_b.  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, scale):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    vmax = np.max(data.values)\n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel('k_parallel (Å^-1)')\n    ax.set_ylabel('Binding Energy (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))\ninteractive_plot", 912)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.2.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.2.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.3": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = E_b.va  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, scale):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    vmax = np.max(data.values)\n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel('k_parallel (Å^-1)')\n    ax.set_ylabel('Binding Energy (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))\ninteractive_plot", 913)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.3.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.3.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.4": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = E_b.val  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, scale):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    vmax = np.max(data.values)\n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel('k_parallel (Å^-1)')\n    ax.set_ylabel('Binding Energy (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))\ninteractive_plot", 915)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.4.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.4.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.5": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = E_b.valu  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, scale):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    vmax = np.max(data.values)\n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel('k_parallel (Å^-1)')\n    ax.set_ylabel('Binding Energy (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))\ninteractive_plot", 916)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.5.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.5.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.6": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = E_b.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, scale):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    vmax = np.max(data.values)\n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel('k_parallel (Å^-1)')\n    ax.set_ylabel('Binding Energy (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))\ninteractive_plot", 918)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.6.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.6.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = E_b.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Binding Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel('Binding Energy (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel('k_parallel (Å^-1)')
    ax.set_ylabel(r'$E-E_f$(eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel(r'$k_\parallel $(Å^-1)')
    ax.set_ylabel(r'$E-E_f$(eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel(r'$k_\parallel $(Å$^-1$)')
    ax.set_ylabel(r'$E-E_f$(eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.7": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, scale):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    vmax = np.max(data.values)\n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1\"$)')\n    ax.set_ylabel(r'$E-E_f$(eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))\ninteractive_plot", 1305)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.7.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.7.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.8": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_inspect("import numpy as np\nimport xarray as xr\nimpATTRS_MAP", 51, 0)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.8.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.8.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.9": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, scale):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    vmax = np.max(data.values)\n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1$)')\n    ax.set_ylabel(r'$E-E_f$(eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))\ninteractive_plot", 1304)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.9.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.9.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
    ax.set_ylabel(r'$E-E_f$(eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    vmax = np.max(data.values)
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
    ax.set_ylabel(r'$E-E_f$ (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes, vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, vmax, scale):
    fig, ax = plt.subplots(figsize=(10, 8))
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap='nipy_spectral', vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
    ax.set_ylabel(r'$E-E_f$ (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider
from matplotlib.colors import ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, vmax, scale, min_color, max_color):
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # Create a custom colormap
    cmap = ListedColormap(['black', min_color, max_color])
    
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
    ax.set_ylabel(r'$E-E_f$ (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               min_color=fixed('black'),
                               max_color=fixed('red'))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider
from matplotlib.colors import ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, vmax, scale, min_color, max_color):
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # Create a custom colormap
    cmap = ListedColormap(['black', min_color, max_color])
    
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
    ax.set_ylabel(r'$E-E_f$ (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               min_color=fixed('purple'),
                               max_color=fixed('red'))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider
from matplotlib.colors import ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, vmax, scale, min_color, max_color):
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # Create a custom colormap
    cmap = ListedColormap(['black', min_color, max_color])
    
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, vmin=vmin, vmax=vmax/scale)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
    ax.set_ylabel(r'$E-E_f$ (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               min_color=fixed('purple'),
                               max_color=fixed('green'))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider
from matplotlib.colors import Normalize

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # Create a custom normalization
    norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
    # Create a colormap with the specified range
    cmap = plt.cm.nipy_spectral(np.linspace(cmap_start, cmap_end, 256))
    
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
    ax.set_ylabel(r'$E-E_f$ (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),
                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))
interactive_plot
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # Create a custom normalization
    norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
    # Create a colormap with the specified range
    cmap_array = plt.cm.nipy_spectral(np.linspace(cmap_start, cmap_end, 256))
    cmap = ListedColormap(cmap_array)
    
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
    ax.set_ylabel(r'$E-E_f$ (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),
                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))
interactive_plot
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.10": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.j(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 1309)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.10.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.10.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
def __jupyter_exec_background__():
    from IPython.display import display
    from threading import Thread
    from traceback import format_exc

    # First send a dummy response to get the display id.
    # Later we'll send the real response with the actual data.
    # And that can happen much later even after the execution completes,
    # as that response will be sent from a bg thread.
    output = display({"application/vnd.vscode.bg.execution.11": ""}, raw=True, display_id=True)

    def do_implementation():
        return get_ipython().kernel.do_complete("import numpy as np\nimport xarray as xr\nimport matplotlib.pyplot as plt\nfrom ipywidgets import interactive, fixed, IntSlider, FloatSlider\nfrom matplotlib.colors import Normalize, ListedColormap\n\n# Your existing code to load the data\nfrom arpes.load_pxt import *\nfrom arpes.io import *\nfrom arpes import *\nfrom arpes.utilities import *\n\nif not hasattr(np, 'complex'):\n    np.complex = np.complex128\n\ndata = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')\n\n# Constants\nhbar = 6.582119569e-16  # eV*s\nm_e = 9.1093837015e-31  # kg\nE_photon = data.attrs['hv']  # Photon energy from the attributes\nwork_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)\n\n# Calculate kinetic energy and momentum\nE_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values\nk_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar\n\n# Get the energy values\nenergy_values = -data.eV.values  # Negative energy values\n\n## Define the plotting function\ndef plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):\n    fig, ax = plt.subplots(figsize=(10, 8))\n    \n    # Create a custom normalization\n    norm = Normalize(vmin=vmin, vmax=vmax/scale)\n    \n    # Create a colormap with the specified range\n    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))\n    cmap = ListedColormap(cmap_array)\n    \n    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)\n    fig.colorbar(im, ax=ax, label='Intensity')\n    ax.set_xlabel(r'$k_\\parallel $(Å$^{-1}$)')\n    ax.set_ylabel(r'$E-E_f$ (eV)')\n    ax.set_title('ARPES Data in Momentum Space')\n    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits\n    plt.show()\n\n# Create the interactive widget\nmax_data_value = np.max(data.values)\ninteractive_plot = interactive(plot_arpes,\n                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),\n                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),\n                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),\n                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),\n                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))\ninteractive_plot", 1311)

    def bg_main():
        try:
            output.update({"application/vnd.vscode.bg.execution.11.result": do_implementation()}, raw=True)
        except Exception as e:
            output.update({"application/vnd.vscode.bg.execution.11.error": format_exc()}, raw=True)


    Thread(target=bg_main, daemon=True).start()


__jupyter_exec_background__()
del __jupyter_exec_background__
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, fixed, IntSlider, FloatSlider
from matplotlib.colors import Normalize, ListedColormap

# Your existing code to load the data
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *

if not hasattr(np, 'complex'):
    np.complex = np.complex128

data = read_single_pxt('/home/<USER>/Documents/September 2022 Beamtime/PtTe2_3/PtTe_092_S008.pxt')

# Constants
hbar = 6.582119569e-16  # eV*s
m_e = 9.1093837015e-31  # kg
E_photon = data.attrs['hv']  # Photon energy from the attributes
work_function = 4.5  # eV (adjust this value if you know the correct work function for your sample)

# Calculate kinetic energy and momentum
E_b = work_function + np.abs(data.eV) - E_photon  # Negative E_b values
k_parallel = np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar

# Get the energy values
energy_values = -data.eV.values  # Negative energy values

## Define the plotting function
def plot_arpes(vmin, vmax, scale, cmap_start, cmap_end):
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # Create a custom normalization
    norm = Normalize(vmin=vmin, vmax=vmax/scale)
    
    # Create a colormap with the specified range
    cmap_array = plt.cm.jet(np.linspace(cmap_start, cmap_end, 256))
    cmap = ListedColormap(cmap_array)
    
    im = ax.pcolormesh(k_parallel, energy_values, data.values, shading='auto', cmap=cmap, norm=norm)
    fig.colorbar(im, ax=ax, label='Intensity')
    ax.set_xlabel(r'$k_\parallel $(Å$^{-1}$)')
    ax.set_ylabel(r'$E-E_f$ (eV)')
    ax.set_title('ARPES Data in Momentum Space')
    ax.set_ylim(energy_values.min(), energy_values.max())  # Adjust y-axis limits
    plt.show()

# Create the interactive widget
max_data_value = np.max(data.values)
interactive_plot = interactive(plot_arpes,
                               vmin=IntSlider(value=0, min=0, max=max_data_value, step=1, continuous_update=False),
                               vmax=IntSlider(value=max_data_value, min=0, max=max_data_value, step=1, continuous_update=False),
                               scale=FloatSlider(value=1.0, min=0.1, max=1.0, step=0.1, continuous_update=False),
                               cmap_start=FloatSlider(value=0.0, min=0.0, max=1.0, step=0.01, continuous_update=False),
                               cmap_end=FloatSlider(value=1.0, min=0.0, max=1.0, step=0.01, continuous_update=False))
interactive_plot
