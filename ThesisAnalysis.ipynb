import os
import numpy as np
import xarray as xr
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.colors as pcolors
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox, Tab, Accordion, HTML
from IPython.display import display
import tkinter as tk
from tkinter import filedialog, messagebox
from arpes.load_pxt import read_single_pxt
import warnings
from skimage.feature import canny
import pandas as pd

igor_data = np.array([
    [57600, 54784, 58112],
    [56561.95, 53415.66, 57121.13],
    [55523.89, 52047.31, 56130.26],
    [54485.84, 50678.96, 55139.39],
    [53447.78, 49310.62, 54148.52],
    [52409.73, 47942.27, 53157.65],
    [51371.67, 46655.25, 52193.88],
    [50333.62, 45428.45, 51250.2],
    [49295.56, 44201.66, 50306.51],
    [48257.51, 42974.87, 49362.82],
    [47219.45, 41748.08, 48419.14],
    [46223.56, 40563.45, 47510.59],
    [45468.61, 39619.77, 46802.82],
    [44713.66, 38676.08, 46095.06],
    [43958.71, 37732.39, 45387.29],
    [43203.77, 36788.71, 44679.53],
    [42448.82, 35845.02, 43971.77],
    [41693.87, 34833.07, 43195.73],
    [40938.92, 33795.01, 42393.6],
    [40183.97, 32756.96, 41591.46],
    [39429.02, 31718.9, 40789.33],
    [38674.07, 30680.85, 39987.2],
    [37919.12, 29670.9, 39171.01],
    [37164.17, 28727.21, 38321.7],
    [36409.22, 27783.53, 37472.38],
    [35654.27, 26839.84, 36623.06],
    [34899.32, 25896.16, 35773.74],
    [34144.38, 24952.47, 34924.42],
    [33512.91, 24173.43, 34239.75],
    [32899.52, 23418.48, 33579.17],
    [32286.12, 22663.53, 32918.59],
    [31672.72, 21908.58, 32258.01],
    [31059.33, 21153.63, 31597.43],
    [30467.01, 20419.77, 30957.93],
    [29900.8, 19712, 30344.53],
    [29334.59, 19004.23, 29731.14],
    [28768.38, 18296.47, 29117.74],
    [28202.16, 17588.71, 28504.35],
    [27641.98, 16886.96, 27901.99],
    [27358.87, 16462.31, 27807.62],
    [27075.77, 16037.65, 27713.26],
    [26792.66, 15612.99, 27618.89],
    [26509.55, 15188.33, 27524.52],
    [26226.45, 14763.67, 27430.15],
    [26027.67, 14479.56, 27448.22],
    [25886.12, 14290.82, 27542.59],
    [25744.56, 14102.09, 27636.96],
    [25603.01, 13913.35, 27731.33],
    [25461.46, 13724.61, 27825.69],
    [25279.75, 13503.75, 27944.16],
    [24902.28, 13126.27, 28180.08],
    [24524.8, 12748.8, 28416],
    [24147.33, 12371.33, 28651.92],
    [23769.85, 11993.85, 28887.84],
    [23392.38, 11616.38, 29123.77],
    [22874.35, 11168.63, 29359.69],
    [22308.14, 10696.78, 29595.61],
    [21741.93, 10224.94, 29831.53],
    [21175.72, 9753.098, 30067.45],
    [20609.51, 9281.255, 30303.37],
    [19952.94, 8899.765, 30539.29],
    [19103.62, 8711.027, 30775.21],
    [18254.31, 8522.29, 31011.14],
    [17404.99, 8333.553, 31247.06],
    [16555.67, 8144.816, 31482.98],
    [15706.35, 7956.079, 31718.9],
    [14688.38, 7893.835, 31828.33],
    [13650.32, 7846.651, 31922.7],
    [12612.27, 7799.467, 32017.07],
    [11574.21, 7752.282, 32111.44],
    [10536.16, 7705.098, 32205.8],
    [9807.31, 7922.949, 32388.52],
    [9429.835, 8441.977, 32671.62],
    [9052.36, 8961.004, 32954.73],
    [8674.887, 9480.031, 33237.84],
    [8297.412, 9999.059, 33520.94],
    [7911.906, 10526.12, 33812.08],
    [7345.694, 11233.88, 34283.92],
    [6779.482, 11941.65, 34755.77],
    [6213.271, 12649.41, 35227.61],
    [5647.059, 13357.18, 35699.45],
    [5080.847, 14064.94, 36171.29],
    [4543.749, 14714.48, 36614.02],
    [4024.722, 15327.87, 37038.68],
    [3505.694, 15941.27, 37463.34],
    [2986.667, 16554.67, 37888],
    [2467.639, 17168.06, 38312.66],
    [1984.753, 17790.49, 38764.42],
    [1654.463, 18451.07, 39330.64],
    [1324.173, 19111.65, 39896.85],
    [993.8823, 19772.23, 40463.06],
    [663.5922, 20432.82, 41029.27],
    [333.302, 21093.4, 41595.48],
    [256, 21464.85, 41944.85],
    [256, 21747.95, 42227.95],
    [256, 22031.06, 42511.06],
    [256, 22314.16, 42794.16],
    [256, 22597.27, 43077.27],
    [239.9373, 23008.88, 43456.75],
    [192.7529, 23669.46, 44022.96],
    [145.5686, 24330.04, 44589.18],
    [98.38432, 24990.62, 45155.39],
    [51.2, 25651.2, 45721.6],
    [4.015687, 26311.78, 46287.81],
    [0, 26972.36, 46897.19],
    [0, 27632.94, 47510.59],
    [0, 28293.52, 48123.98],
    [0, 28954.1, 48737.38],
    [0, 29614.68, 49350.78],
    [0, 30344.53, 50033.44],
    [0, 31146.67, 50788.39],
    [0, 31948.8, 51543.34],
    [0, 32750.93, 52298.29],
    [0, 33553.07, 53053.24],
    [0, 34358.21, 53805.18],
    [0, 35207.53, 54512.94],
    [0, 36056.85, 55220.71],
    [0, 36906.16, 55928.47],
    [0, 37755.48, 56636.23],
    [0, 38604.8, 57344],
    [0, 39062.59, 57208.47],
    [0, 39298.51, 56595.07],
    [0, 39534.43, 55981.68],
    [0, 39770.35, 55368.28],
    [0, 40006.27, 54754.89],
    [0, 40181.96, 54041.1],
    [0, 40134.78, 52955.86],
    [0, 40087.59, 51870.62],
    [0, 40040.41, 50785.38],
    [0, 39993.22, 49700.14],
    [0, 39946.04, 48614.9],
    [0, 39936, 47641.1],
    [0, 39936, 46697.41],
    [0, 39936, 45753.73],
    [0, 39936, 44810.04],
    [0, 39936, 43866.35],
    [0, 39918.93, 42854.4],
    [0, 39871.75, 41721.98],
    [0, 39824.57, 40589.55],
    [0, 39777.38, 39457.13],
    [0, 39730.2, 38324.71],
    [0, 39683.01, 37192.28],
    [0, 39680, 36369.07],
    [0, 39680, 35566.93],
    [0, 39680, 34764.8],
    [0, 39680, 33962.67],
    [0, 39680, 33160.54],
    [0, 39680, 32527.06],
    [0, 39680, 32055.21],
    [0, 39680, 31583.37],
    [0, 39680, 31111.53],
    [0, 39680, 30639.69],
    [0, 39675.98, 30123.67],
    [0, 39628.8, 29132.8],
    [0, 39581.62, 28141.93],
    [0, 39534.43, 27151.06],
    [0, 39487.25, 26160.19],
    [0, 39440.06, 25169.32],
    [0, 39361.76, 24240.69],
    [0, 39267.39, 23344.19],
    [0, 39173.02, 22447.69],
    [0, 39078.65, 21551.18],
    [0, 38984.28, 20654.68],
    [0, 38923.04, 19835.48],
    [0, 38970.23, 19269.27],
    [0, 39017.41, 18703.06],
    [0, 39064.6, 18136.85],
    [0, 39111.78, 17570.63],
    [0, 39158.96, 17004.42],
    [0, 39435.04, 16781.55],
    [0, 39765.33, 16640],
    [0, 40095.62, 16498.45],
    [0, 40425.91, 16356.89],
    [0, 40756.2, 16215.34],
    [993.8823, 41122.64, 16073.79],
    [3589.02, 41547.29, 15932.24],
    [6184.157, 41971.95, 15790.68],
    [8779.294, 42396.61, 15649.13],
    [11374.43, 42821.27, 15507.58],
    [13969.57, 43245.93, 15366.02],
    [15796.71, 43715.77, 15224.47],
    [17589.71, 44187.61, 15082.92],
    [19382.71, 44659.45, 14941.36],
    [21175.72, 45131.29, 14799.81],
    [22968.72, 45603.14, 14658.26],
    [24686.43, 46100.08, 14516.71],
    [26337.88, 46619.11, 14375.15],
    [27989.33, 47138.13, 14233.6],
    [29640.79, 47657.16, 14092.05],
    [31292.23, 48176.19, 13950.49],
    [32933.65, 48705.25, 13798.9],
    [34490.73, 49318.65, 13562.98],
    [36047.81, 49932.05, 13327.06],
    [37604.89, 50545.44, 13091.14],
    [39161.98, 51158.84, 12855.22],
    [40719.06, 51772.23, 12619.29],
    [41922.76, 52225, 12415.5],
    [42960.82, 52602.48, 12226.76],
    [43998.87, 52979.95, 12038.02],
    [45036.93, 53357.43, 11849.29],
    [46074.98, 53734.9, 11660.55],
    [47293.74, 54196.71, 11411.58],
    [49039.56, 54904.47, 10986.92],
    [50785.38, 55612.23, 10562.26],
    [52531.2, 56320, 10137.6],
    [54277.02, 57027.77, 9712.941],
    [56022.84, 57735.53, 9288.282],
    [57494.59, 58325.84, 8785.317],
    [58910.12, 58892.05, 8266.29],
    [60325.65, 59458.26, 7747.263],
    [61741.18, 60024.47, 7228.235],
    [63156.71, 60590.68, 6709.208],
    [64076.3, 60470.21, 6457.224],
    [64265.04, 59337.79, 6598.776],
    [64453.77, 58205.36, 6740.33],
    [64642.51, 57072.94, 6881.882],
    [64831.25, 55940.52, 7023.435],
    [65019.98, 54808.09, 7164.988],
    [64746.92, 53260.05, 7398.902],
    [64463.81, 51702.96, 7634.824],
    [64180.71, 50145.88, 7870.745],
    [63897.6, 48588.8, 8106.667],
    [63614.49, 47031.72, 8342.588],
    [63592.41, 45605.14, 8474.102],
    [63781.14, 44283.98, 8521.286],
    [63969.88, 42962.82, 8568.471],
    [64158.62, 41641.66, 8615.655],
    [64347.36, 40320.5, 8662.839],
    [64415.62, 38993.32, 8704],
    [63660.68, 37624.97, 8704],
    [62905.73, 36256.63, 8704],
    [62150.78, 34888.28, 8704],
    [61395.83, 33519.94, 8704],
    [60640.88, 32151.59, 8704],
    [60283.48, 30882.63, 8704],
    [60094.75, 29655.84, 8704],
    [59906.01, 28429.05, 8704],
    [59717.27, 27202.26, 8704],
    [59528.54, 25975.47, 8704],
    [59339.8, 24722.57, 8704],
    [59151.06, 23401.41, 8704],
    [58962.32, 22080.25, 8704],
    [58773.59, 20759.09, 8704],
    [58584.85, 19437.93, 8704],
    [58396.11, 18116.77, 8704],
    [58287.69, 17197.18, 8704],
    [58193.32, 16347.86, 8704],
    [58098.95, 15498.54, 8704],
    [58004.58, 14649.22, 8704],
    [57910.21, 13799.91, 8704],
    [57795.77, 12267.92, 8704],
    [57654.21, 9814.337, 8704],
    [57512.66, 7360.753, 8704],
    [57371.11, 4907.168, 8704],
    [57229.55, 2453.584, 8704],
    [57088, 0, 8704]])
# Create the array from the provided data


# Normalize the RGB values to the range 0-1
normalized_data = igor_data / 65535.0

# Create custom colorscale for Plotly
def create_custom_colorscale(rgb_array):
    """Convert RGB array to Plotly colorscale format"""
    n_colors = len(rgb_array)
    colorscale = []
    for i, rgb in enumerate(rgb_array):
        position = i / (n_colors - 1)
        color = f'rgb({int(rgb[0]*255)}, {int(rgb[1]*255)}, {int(rgb[2]*255)})'
        colorscale.append([position, color])
    return colorscale

rainbowlightct = create_custom_colorscale(normalized_data)

def load_data_files(folder_path):
    data_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    data_files.sort()
    return [os.path.join(folder_path, f) for f in data_files]

def determine_scan_type(data_files):
    scan_types = []
    previous_polar = None
    previous_hv = None
    for file_path in data_files:
        data = read_single_pxt(file_path)
        if 'polar' in data.attrs and 'hv' in data.attrs:
            current_polar = data.attrs['polar']
            current_hv = data.attrs['hv']
            if previous_polar is not None and previous_hv is not None:
                if current_polar != previous_polar:
                    scan_types.append(('polar', current_polar))
                elif current_hv != previous_hv:
                    scan_types.append(('hv', current_hv))
                else:
                    scan_types.append(('unknown', None))
            else:
                scan_types.append(('unknown', None))
            previous_polar = current_polar
            previous_hv = current_hv
        else:
            scan_types.append(('unknown', None))
    return scan_types

def moving_average(data, window_size):
    kernel = np.ones((window_size, window_size)) / (window_size * window_size)
    return np.convolve(data.flatten(), kernel.flatten(), mode='same').reshape(data.shape)

def load_dft_data(file_paths):
    dft_data = []
    for file_path in file_paths:
        data = np.loadtxt(file_path)
        dft_data.append(data)
    return dft_data

# Additional configuration and warnings suppression
warnings.filterwarnings("ignore", category=UserWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128

# Duplicate igor_data definition (keeping for compatibility)
igor_data = np.array([
    [57600, 54784, 58112],
    [56561.95, 53415.66, 57121.13],
    [55523.89, 52047.31, 56130.26],
    [54485.84, 50678.96, 55139.39],
    [53447.78, 49310.62, 54148.52],
    [52409.73, 47942.27, 53157.65],
    [51371.67, 46655.25, 52193.88],
    [50333.62, 45428.45, 51250.2],
    [49295.56, 44201.66, 50306.51],
    [48257.51, 42974.87, 49362.82],
    [47219.45, 41748.08, 48419.14],
    [46223.56, 40563.45, 47510.59],
    [45468.61, 39619.77, 46802.82],
    [44713.66, 38676.08, 46095.06],
    [43958.71, 37732.39, 45387.29],
    [43203.77, 36788.71, 44679.53],
    [42448.82, 35845.02, 43971.77],
    [41693.87, 34833.07, 43195.73],
    [40938.92, 33795.01, 42393.6],
    [40183.97, 32756.96, 41591.46],
    [39429.02, 31718.9, 40789.33],
    [38674.07, 30680.85, 39987.2],
    [37919.12, 29670.9, 39171.01],
    [37164.17, 28727.21, 38321.7],
    [36409.22, 27783.53, 37472.38],
    [35654.27, 26839.84, 36623.06],
    [34899.32, 25896.16, 35773.74],
    [34144.38, 24952.47, 34924.42],
    [33512.91, 24173.43, 34239.75],
    [32899.52, 23418.48, 33579.17],
    [32286.12, 22663.53, 32918.59],
    [31672.72, 21908.58, 32258.01],
    [31059.33, 21153.63, 31597.43],
    [30467.01, 20419.77, 30957.93],
    [29900.8, 19712, 30344.53],
    [29334.59, 19004.23, 29731.14],
    [28768.38, 18296.47, 29117.74],
    [28202.16, 17588.71, 28504.35],
    [27641.98, 16886.96, 27901.99],
    [27358.87, 16462.31, 27807.62],
    [27075.77, 16037.65, 27713.26],
    [26792.66, 15612.99, 27618.89],
    [26509.55, 15188.33, 27524.52],
    [26226.45, 14763.67, 27430.15],
    [26027.67, 14479.56, 27448.22],
    [25886.12, 14290.82, 27542.59],
    [25744.56, 14102.09, 27636.96],
    [25603.01, 13913.35, 27731.33],
    [25461.46, 13724.61, 27825.69],
    [25279.75, 13503.75, 27944.16],
    [24902.28, 13126.27, 28180.08],
    [24524.8, 12748.8, 28416],
    [24147.33, 12371.33, 28651.92],
    [23769.85, 11993.85, 28887.84],
    [23392.38, 11616.38, 29123.77],
    [22874.35, 11168.63, 29359.69],
    [22308.14, 10696.78, 29595.61],
    [21741.93, 10224.94, 29831.53],
    [21175.72, 9753.098, 30067.45],
    [20609.51, 9281.255, 30303.37],
    [19952.94, 8899.765, 30539.29],
    [19103.62, 8711.027, 30775.21],
    [18254.31, 8522.29, 31011.14],
    [17404.99, 8333.553, 31247.06],
    [16555.67, 8144.816, 31482.98],
    [15706.35, 7956.079, 31718.9],
    [14688.38, 7893.835, 31828.33],
    [13650.32, 7846.651, 31922.7],
    [12612.27, 7799.467, 32017.07],
    [11574.21, 7752.282, 32111.44],
    [10536.16, 7705.098, 32205.8],
    [9807.31, 7922.949, 32388.52],
    [9429.835, 8441.977, 32671.62],
    [9052.36, 8961.004, 32954.73],
    [8674.887, 9480.031, 33237.84],
    [8297.412, 9999.059, 33520.94],
    [7911.906, 10526.12, 33812.08],
    [7345.694, 11233.88, 34283.92],
    [6779.482, 11941.65, 34755.77],
    [6213.271, 12649.41, 35227.61],
    [5647.059, 13357.18, 35699.45],
    [5080.847, 14064.94, 36171.29],
    [4543.749, 14714.48, 36614.02],
    [4024.722, 15327.87, 37038.68],
    [3505.694, 15941.27, 37463.34],
    [2986.667, 16554.67, 37888],
    [2467.639, 17168.06, 38312.66],
    [1984.753, 17790.49, 38764.42],
    [1654.463, 18451.07, 39330.64],
    [1324.173, 19111.65, 39896.85],
    [993.8823, 19772.23, 40463.06],
    [663.5922, 20432.82, 41029.27],
    [333.302, 21093.4, 41595.48],
    [256, 21464.85, 41944.85],
    [256, 21747.95, 42227.95],
    [256, 22031.06, 42511.06],
    [256, 22314.16, 42794.16],
    [256, 22597.27, 43077.27],
    [239.9373, 23008.88, 43456.75],
    [192.7529, 23669.46, 44022.96],
    [145.5686, 24330.04, 44589.18],
    [98.38432, 24990.62, 45155.39],
    [51.2, 25651.2, 45721.6],
    [4.015687, 26311.78, 46287.81],
    [0, 26972.36, 46897.19],
    [0, 27632.94, 47510.59],
    [0, 28293.52, 48123.98],
    [0, 28954.1, 48737.38],
    [0, 29614.68, 49350.78],
    [0, 30344.53, 50033.44],
    [0, 31146.67, 50788.39],
    [0, 31948.8, 51543.34],
    [0, 32750.93, 52298.29],
    [0, 33553.07, 53053.24],
    [0, 34358.21, 53805.18],
    [0, 35207.53, 54512.94],
    [0, 36056.85, 55220.71],
    [0, 36906.16, 55928.47],
    [0, 37755.48, 56636.23],
    [0, 38604.8, 57344],
    [0, 39062.59, 57208.47],
    [0, 39298.51, 56595.07],
    [0, 39534.43, 55981.68],
    [0, 39770.35, 55368.28],
    [0, 40006.27, 54754.89],
    [0, 40181.96, 54041.1],
    [0, 40134.78, 52955.86],
    [0, 40087.59, 51870.62],
    [0, 40040.41, 50785.38],
    [0, 39993.22, 49700.14],
    [0, 39946.04, 48614.9],
    [0, 39936, 47641.1],
    [0, 39936, 46697.41],
    [0, 39936, 45753.73],
    [0, 39936, 44810.04],
    [0, 39936, 43866.35],
    [0, 39918.93, 42854.4],
    [0, 39871.75, 41721.98],
    [0, 39824.57, 40589.55],
    [0, 39777.38, 39457.13],
    [0, 39730.2, 38324.71],
    [0, 39683.01, 37192.28],
    [0, 39680, 36369.07],
    [0, 39680, 35566.93],
    [0, 39680, 34764.8],
    [0, 39680, 33962.67],
    [0, 39680, 33160.54],
    [0, 39680, 32527.06],
    [0, 39680, 32055.21],
    [0, 39680, 31583.37],
    [0, 39680, 31111.53],
    [0, 39680, 30639.69],
    [0, 39675.98, 30123.67],
    [0, 39628.8, 29132.8],
    [0, 39581.62, 28141.93],
    [0, 39534.43, 27151.06],
    [0, 39487.25, 26160.19],
    [0, 39440.06, 25169.32],
    [0, 39361.76, 24240.69],
    [0, 39267.39, 23344.19],
    [0, 39173.02, 22447.69],
    [0, 39078.65, 21551.18],
    [0, 38984.28, 20654.68],
    [0, 38923.04, 19835.48],
    [0, 38970.23, 19269.27],
    [0, 39017.41, 18703.06],
    [0, 39064.6, 18136.85],
    [0, 39111.78, 17570.63],
    [0, 39158.96, 17004.42],
    [0, 39435.04, 16781.55],
    [0, 39765.33, 16640],
    [0, 40095.62, 16498.45],
    [0, 40425.91, 16356.89],
    [0, 40756.2, 16215.34],
    [993.8823, 41122.64, 16073.79],
    [3589.02, 41547.29, 15932.24],
    [6184.157, 41971.95, 15790.68],
    [8779.294, 42396.61, 15649.13],
    [11374.43, 42821.27, 15507.58],
    [13969.57, 43245.93, 15366.02],
    [15796.71, 43715.77, 15224.47],
    [17589.71, 44187.61, 15082.92],
    [19382.71, 44659.45, 14941.36],
    [21175.72, 45131.29, 14799.81],
    [22968.72, 45603.14, 14658.26],
    [24686.43, 46100.08, 14516.71],
    [26337.88, 46619.11, 14375.15],
    [27989.33, 47138.13, 14233.6],
    [29640.79, 47657.16, 14092.05],
    [31292.23, 48176.19, 13950.49],
    [32933.65, 48705.25, 13798.9],
    [34490.73, 49318.65, 13562.98],
    [36047.81, 49932.05, 13327.06],
    [37604.89, 50545.44, 13091.14],
    [39161.98, 51158.84, 12855.22],
    [40719.06, 51772.23, 12619.29],
    [41922.76, 52225, 12415.5],
    [42960.82, 52602.48, 12226.76],
    [43998.87, 52979.95, 12038.02],
    [45036.93, 53357.43, 11849.29],
    [46074.98, 53734.9, 11660.55],
    [47293.74, 54196.71, 11411.58],
    [49039.56, 54904.47, 10986.92],
    [50785.38, 55612.23, 10562.26],
    [52531.2, 56320, 10137.6],
    [54277.02, 57027.77, 9712.941],
    [56022.84, 57735.53, 9288.282],
    [57494.59, 58325.84, 8785.317],
    [58910.12, 58892.05, 8266.29],
    [60325.65, 59458.26, 7747.263],
    [61741.18, 60024.47, 7228.235],
    [63156.71, 60590.68, 6709.208],
    [64076.3, 60470.21, 6457.224],
    [64265.04, 59337.79, 6598.776],
    [64453.77, 58205.36, 6740.33],
    [64642.51, 57072.94, 6881.882],
    [64831.25, 55940.52, 7023.435],
    [65019.98, 54808.09, 7164.988],
    [64746.92, 53260.05, 7398.902],
    [64463.81, 51702.96, 7634.824],
    [64180.71, 50145.88, 7870.745],
    [63897.6, 48588.8, 8106.667],
    [63614.49, 47031.72, 8342.588],
    [63592.41, 45605.14, 8474.102],
    [63781.14, 44283.98, 8521.286],
    [63969.88, 42962.82, 8568.471],
    [64158.62, 41641.66, 8615.655],
    [64347.36, 40320.5, 8662.839],
    [64415.62, 38993.32, 8704],
    [63660.68, 37624.97, 8704],
    [62905.73, 36256.63, 8704],
    [62150.78, 34888.28, 8704],
    [61395.83, 33519.94, 8704],
    [60640.88, 32151.59, 8704],
    [60283.48, 30882.63, 8704],
    [60094.75, 29655.84, 8704],
    [59906.01, 28429.05, 8704],
    [59717.27, 27202.26, 8704],
    [59528.54, 25975.47, 8704],
    [59339.8, 24722.57, 8704],
    [59151.06, 23401.41, 8704],
    [58962.32, 22080.25, 8704],
    [58773.59, 20759.09, 8704],
    [58584.85, 19437.93, 8704],
    [58396.11, 18116.77, 8704],
    [58287.69, 17197.18, 8704],
    [58193.32, 16347.86, 8704],
    [58098.95, 15498.54, 8704],
    [58004.58, 14649.22, 8704],
    [57910.21, 13799.91, 8704],
    [57795.77, 12267.92, 8704],
    [57654.21, 9814.337, 8704],
    [57512.66, 7360.753, 8704],
    [57371.11, 4907.168, 8704],
    [57229.55, 2453.584, 8704],
    [57088, 0, 8704]])
# Create the array from the provided data


# Normalize the RGB values to the range 0-1
normalized_data = igor_data / 65535.0

# Create the custom colorscale for Plotly (duplicate for compatibility)
rainbowlightct = create_custom_colorscale(normalized_data)



import os
import pandas as pd
import numpy as np
from arpes.load_pxt import read_single_pxt
import tkinter as tk
from tkinter import filedialog, messagebox

def load_pxt_files():
    folder_path = filedialog.askdirectory(title="Select folder containing PXT files")
    if not folder_path:
        return

    pxt_files = [f for f in os.listdir(folder_path) if f.endswith('.pxt')]
    if not pxt_files:
        messagebox.showinfo("No PXT files", "No PXT files found in the selected folder.")
        return

    # Sort the files based on their names
    pxt_files.sort()

    data_arrays = []
    attributes = []
    for file in pxt_files:
        file_path = os.path.join(folder_path, file)
        try:
            data = read_single_pxt(file_path)
            df = pd.DataFrame(data.values, columns=data.coords['phi'].values, index=data.coords['eV'].values)
            data_arrays.append(df)
            attributes.append(data.attrs)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load {file}: {str(e)}")

    messagebox.showinfo("Success", f"Loaded {len(data_arrays)} PXT files into pandas DataFrames.")
    return data_arrays, attributes

# Create the main window
root = tk.Tk()
root.title("ARPES PXT File Loader")

# Create and pack a button to trigger file loading
def load_and_store_data():
    global data, data_attributes
    data, data_attributes = load_pxt_files()
    
    # Add new code to correspond data_attributes[i]['polar'] to data[i]
    for i in range(len(data)):
        data[i].attrs['polar'] = data_attributes[i]['polar']

load_button = tk.Button(root, text="Load PXT Files", command=load_and_store_data)
load_button.pack(pady=20)

# Start the GUI event loop
root.mainloop()


work_function = 4.5 #eV
E_photon = np.zeros(np.shape(data_attributes)[0])
for i in range(len(E_photon)):
    E_photon[i]=data_attributes[i]['hv']

E_photon

theta =  np.zeros(np.shape(data_attributes)[0])
for i in range(len(theta)):
    theta[i]=data_attributes[i]['polar']

theta
data_proc = data.copy()
for i in range(len(data_proc)):
    new_index = [data_attributes[i]['hv'] - work_function - abs(idx) for idx in data[i].index]
    data_proc[i] = data_proc[i].set_index(pd.Index(new_index))
data_proc

# Import additional libraries for enhanced plotting
from scipy.interpolate import griddata, LinearNDInterpolator
import ipywidgets as widgets
from scipy.ndimage import gaussian_filter, maximum_filter
from skimage.measure import label, euler_number
from scipy.spatial import cKDTree

# Use the custom colorscale created earlier
# rainbowlightct is already defined as a Plotly colorscale

# Constants
work_function = 4.5  # eV
V0 = 10  # Inner potential in eV, adjust based on your material properties

# Function to apply moving average filter
def moving_average(data, kernel_size):
    kernel = np.ones(kernel_size) / kernel_size
    return np.convolve(data, kernel, mode='same')

# Compute the binding energy range spanned by the data
E_binding_values = []

for i in range(len(data_proc)):
    df = data_proc[i]
    hv = data_attributes[i]['hv']
    E_kinetic_values = df.index.values.astype(float)
    # Compute binding energies for these kinetic energies
    E_binding_scan = hv - work_function - E_kinetic_values
    E_binding_values.extend(E_binding_scan)

E_binding_array = np.array(E_binding_values)
E_binding_min = E_binding_array.min()
E_binding_max = E_binding_array.max()

# Global variables for plot management
current_fig = None

# Function to find peaks in the intensity data
def find_peaks_in_intensity(intensities, neighborhood_size, threshold, smoothing_sigma):
    # Smooth the intensities to reduce noise
    intensities_smooth = gaussian_filter(intensities, sigma=smoothing_sigma)

    # Apply maximum filter to find local maxima
    local_max = maximum_filter(intensities_smooth, size=neighborhood_size) == intensities_smooth

    # Apply threshold to identify significant peaks
    detected_peaks = (intensities_smooth > threshold) & local_max

    # Get peak indices
    peak_indices = np.argwhere(detected_peaks)

    return peak_indices

# Enhanced Plotly-based plotting function
def plot_constant_energy_map(
    mode, E_binding, vmin, vmax, use_contours, contour_levels,
    kernel_size, x_offset, y_offset, sigma, low_threshold, high_threshold,
    display_edges, display_components, scan_number, peak_threshold, neighborhood_size, smoothing_sigma,
    euler_binary_threshold, display_euler_points, intensity_threshold, surface_resolution,
    gradient_threshold=0.01, enable_critical_points=False, data_density_threshold=5
):
    """Enhanced plotting function using Plotly for interactive visualization"""
    global current_fig
    
    try:

    if mode == 'E vs kx':
        fig, ax = plt.subplots(figsize=(10, 8))
        colorbar = None  # Reset colorbar

        # Ensure the scan_number is within valid range
        if scan_number < 0 or scan_number >= len(data_proc):
            print("Invalid scan number selected.")
            return

        # Use data from the selected scan only
        df = data_proc[scan_number]
        hv = data_attributes[scan_number]['hv']

        # Emission angles (theta) in degrees, convert to radians
        emission_angles = df.columns.values.astype(float)
        theta_rad = np.deg2rad(emission_angles)

        # Kinetic energy values
        E_kinetic_values = df.index.values.astype(float)

        # Reverse E_kinetic_values to ensure increasing order if necessary
        if np.any(np.diff(E_kinetic_values) < 0):
            E_kinetic_values = E_kinetic_values[::-1]
            df = df.iloc[::-1]

        # Create grids for E_kinetic and theta using 'xy' indexing
        theta_grid, E_kinetic_grid = np.meshgrid(theta_rad, E_kinetic_values, indexing='xy')

        # Compute E_binding_grid
        E_binding_grid = hv - work_function - E_kinetic_grid + y_offset

        # Compute kx for all combinations
        kx_grid = 0.5123 * np.sqrt(E_kinetic_grid) * np.sin(theta_grid) + x_offset

        # Intensities from the data
        intensities = df.values

        # Apply moving average filter along the energy axis
        if kernel_size > 1:
            intensities = np.apply_along_axis(
                lambda m: moving_average(m, kernel_size),
                axis=0,
                arr=intensities
            )

        # Normalize the intensities
        max_intensity = np.nanmax(intensities)
        if max_intensity > 0:
            intensities = intensities / max_intensity

        # Mask invalid data (e.g., due to sqrt of negative energies)
        valid_mask = np.isfinite(kx_grid) & np.isfinite(E_binding_grid) & np.isfinite(intensities)
        intensities[~valid_mask] = np.nan

        # Update the plot
        pcm = ax.pcolormesh(
            kx_grid, E_binding_grid, intensities,
            shading='auto', cmap=rainbowlightct, vmin=vmin, vmax=vmax
        )

        if colorbar is None:
            colorbar = fig.colorbar(pcm, ax=ax, label='Normalized Intensity')
        else:
            colorbar.update_normal(pcm)

        ax.set_xlabel(r'$k_x$ (Å$^{-1}$)')
        ax.set_ylabel('Binding Energy (eV)')
        ax.set_title(f'Binding Energy vs $k_x$ Map\nScan Number: {scan_number}')

        # Ensure y-axis is inverted (binding energy increases downward)
        ax.set_ylim(ax.get_ylim()[::-1])

        # Prepare intensities for peak detection
        intensities_filled = np.nan_to_num(intensities, nan=0.0)

        # Find peaks in the intensity data
        peak_indices = find_peaks_in_intensity(
            intensities_filled, neighborhood_size, peak_threshold, smoothing_sigma
        )

        # Plot peaks
        if peak_indices.size > 0:
            peak_coords = np.array([(kx_grid[i, j], E_binding_grid[i, j]) for i, j in peak_indices])
            ax.plot(peak_coords[:, 0], peak_coords[:, 1], 'o', color='red', label='Peaks')

        # Threshold the intensities to create a binary image for Euler characteristic calculation
        binary_intensity = intensities_filled >= euler_binary_threshold

        # Compute the Euler characteristic
        euler_char = euler_number(binary_intensity)

        # Display the Euler characteristic on the plot
        #ax.text(
        #    0.95, 0.95,
        #    f'Euler characteristic: {euler_char}',
        #    transform=ax.transAxes,
        #    fontsize=12,
        #    verticalalignment='top',
        #    horizontalalignment='right',
        #    bbox=dict(facecolor='white', alpha=0.5)
        #)

        # Display binary points used in Euler characteristic calculation
        if display_euler_points:
            # Overlay the binary image onto the plot
            ax.contour(
                kx_grid, E_binding_grid, binary_intensity,
                levels=[0.5], colors='white', linewidths=1.0, linestyles='--', label='Euler Binary'
            )

        # Add legend for peaks and Euler binary contour
        ax.legend(loc='lower left')

        if display_edges or display_components:
            # Apply Canny edge detection
            edges = canny(
                intensities_filled,
                sigma=sigma,
                low_threshold=low_threshold,
                high_threshold=high_threshold
            )

            if display_edges:
                # Overlay edges on the plot
                ax.contour(
                    kx_grid, E_binding_grid, edges,
                    levels=[0.5], colors='cyan', linewidths=1.0
                )

            if display_components:
                # Label connected components
                labeled_array, num_features = label(edges, connectivity=2, return_num=True)

                # Iterate over each detected component
                for region_label in range(1, num_features + 1):
                    # Create a mask for the current component
                    component_mask = labeled_array == region_label

                    # Extract the coordinates of the component pixels
                    kx_component = kx_grid[component_mask]
                    E_binding_component = E_binding_grid[component_mask]

                    # Plot the component
                    ax.plot(
                        kx_component,
                        E_binding_component,
                        '.', markersize=1, color='yellow'
                    )

        plt.tight_layout()
        plt.show()

    elif mode in ['kx vs ky', 'kx vs kz']:
        fig, ax = plt.subplots(figsize=(10, 8))
        colorbar = None  # Reset colorbar

        # Prepare lists to store kx, ky_kz, and intensity values
        kx_list = []
        ky_kz_list = []
        intensity_list = []

        # Loop over all scans
        for i in range(len(data_proc)):
            df = data_proc[i]
            hv = data_attributes[i]['hv']

            # Emission angles (theta) in degrees, convert to radians
            emission_angles = df.columns.values.astype(float)
            theta_rad = np.deg2rad(emission_angles)

            # Compute the kinetic energy corresponding to the desired binding energy
            E_kinetic = hv - work_function - E_binding

            # Check if E_kinetic is within the kinetic energy range of the data
            E_kinetic_values = df.index.values.astype(float)
            if E_kinetic < E_kinetic_values.min() or E_kinetic > E_kinetic_values.max():
                continue  # Skip if E_kinetic is outside the data range

            # Calculate k_magnitude (wave vector magnitude) in Å⁻¹
            k_magnitude = 0.5123 * np.sqrt(E_kinetic)  # Å⁻¹
            polar_angle = data_attributes[i]['polar']
            polar_angle_rad = np.deg2rad(polar_angle)

            # Calculate kx components
            kx = k_magnitude * np.sin(theta_rad) + x_offset  # Array of kx values

            # Extract intensities at the specified kinetic energy
            if E_kinetic in E_kinetic_values:
                intensities = df.loc[E_kinetic].values
            else:
                # Interpolate intensities at E_kinetic for each theta
                intensities = df.apply(
                    lambda col: np.interp(E_kinetic, E_kinetic_values, col.values)
                ).values

            # Apply moving average filter to intensities
            if kernel_size > 1:
                intensities = moving_average(intensities, kernel_size)

            # Depending on the mode, calculate ky or kz
            if mode == 'kx vs ky':
                # Polar angle for this scan (in degrees)
                polar_angle = data_attributes[i]['polar']
                polar_angle_rad = np.deg2rad(polar_angle)

                # Calculate ky components
                ky = k_magnitude * np.sin(polar_angle_rad) + y_offset  # Scalar ky value

                # Create an array of ky values matching the length of kx
                ky_kz_array = np.full_like(kx, ky)
                ylabel = r'$k_y$ (Å$^{-1}$)'
                title = f'Constant Energy Map at Binding Energy = {E_binding:.2f} eV\nMode: kx vs ky'

            elif mode == 'kx vs kz':
                # Calculate kz components using the inner potential V0
                kz = 0.5123 * np.sqrt(E_kinetic * np.cos(theta_rad)**2 + V0) + y_offset  # Å⁻¹

                ky_kz_array = kz
                ylabel = r'$k_z$ (Å$^{-1}$)'
                title = f'Constant Energy Map at Binding Energy = {E_binding:.2f} eV\nMode: kx vs kz'

            else:
                print("Invalid mode selected.")
                return

            # Append to the lists
            kx_list.extend(kx)
            ky_kz_list.extend(ky_kz_array)
            intensity_list.extend(intensities)

        # Convert lists to numpy arrays
        kx_array = np.array(kx_list)
        ky_kz_array = np.array(ky_kz_list)
        intensity_array = np.array(intensity_list)

        # Check if there is data to plot
        if kx_array.size == 0 or ky_kz_array.size == 0 or intensity_array.size == 0:
            print("No data available for the selected binding energy.")
            return

        # Create grid for interpolation
        grid_resolution = 600
        kx_grid = np.linspace(kx_array.min(), kx_array.max(), grid_resolution)
        ky_kz_grid = np.linspace(ky_kz_array.min(), ky_kz_array.max(), grid_resolution)
        kx_mesh, ky_kz_mesh = np.meshgrid(kx_grid, ky_kz_grid)

        # Interpolate intensity data onto the grid
        intensity_grid = griddata(
            points=(kx_array, ky_kz_array),
            values=intensity_array,
            xi=(kx_mesh, ky_kz_mesh),
            method='cubic'
        )

        # Handle NaN values in the interpolated data
        intensity_grid = np.nan_to_num(intensity_grid)

        # Normalize intensity_grid to maximum value
        max_intensity = intensity_grid.max()
        if max_intensity > 0:
            intensity_grid /= max_intensity

        # Update the plot
        if use_contours:
            # Use the contour_levels parameter to adjust contour density
            cs = ax.contour(
                kx_mesh, ky_kz_mesh, intensity_grid,
                levels=contour_levels, cmap=rainbowlightct
            )
            if colorbar is None:
                colorbar = fig.colorbar(cs, ax=ax, label='Intensity')
            else:
                colorbar.update_normal(cs)
        else:
            pcm = ax.pcolormesh(
                kx_mesh, ky_kz_mesh, intensity_grid,
                shading='auto', cmap=rainbowlightct, vmin=vmin, vmax=vmax
            )
            if colorbar is None:
                colorbar = fig.colorbar(pcm, ax=ax, label='Intensity')
            else:
                colorbar.update_normal(pcm)

        ax.set_xlabel(r'$k_x$ (Å$^{-1}$)')
        ax.set_ylabel(ylabel)
        ax.set_title(title)

        # Prepare intensities for peak detection
        intensities_filled = np.nan_to_num(intensity_grid, nan=0.0)

        # Find peaks in the intensity data
        peak_indices = find_peaks_in_intensity(
            intensities_filled, neighborhood_size, peak_threshold, smoothing_sigma
        )

        # Plot peaks
        if peak_indices.size > 0:
            peak_coords = np.array([(kx_mesh[i, j], ky_kz_mesh[i, j]) for i, j in peak_indices])
            ax.plot(peak_coords[:, 0], peak_coords[:, 1], 'o', color='red', label='Peaks')

        # Threshold the intensities to create a binary image for Euler characteristic calculation
        binary_intensity = intensities_filled >= euler_binary_threshold

        # Compute the Euler characteristic
        euler_char = euler_number(binary_intensity)

        # Display the Euler characteristic on the plot
        #ax.text(
         #   0.95, 0.95,
          #  f'Euler characteristic: {euler_char}',
           # transform=ax.transAxes,
            #fontsize=12,
            #verticalalignment='top',
            #horizontalalignment='right',
            #bbox=dict(facecolor='white', alpha=0.5)
        #)

        # Display binary points used in Euler characteristic calculation
        if display_euler_points:
            # Overlay the binary image onto the plot
            ax.contour(
                kx_mesh, ky_kz_mesh, binary_intensity,
                levels=[0.5], colors='white', linewidths=1.0, linestyles='--', label='Euler Binary'
            )

        # Add legend for peaks and Euler binary contour
        ax.legend(loc='lower left')

        if display_edges or display_components:
            # Apply Canny edge detection
            edges = canny(
                intensities_filled,
                sigma=sigma,
                low_threshold=low_threshold,
                high_threshold=high_threshold
            )

            if display_edges:
                # Overlay edges on the plot
                ax.contour(
                    kx_mesh, ky_kz_mesh, edges,
                    levels=[0.5], colors='cyan', linewidths=1.0
                )

            if display_components:
                # Label connected components
                labeled_array, num_features = label(edges, connectivity=2, return_num=True)

                # Iterate over each detected component
                for region_label in range(1, num_features + 1):
                    # Create a mask for the current component
                    component_mask = labeled_array == region_label

                    # Extract the coordinates of the component pixels
                    kx_component = kx_mesh[component_mask]
                    ky_kz_component = ky_kz_mesh[component_mask]

                    # Plot the component
                    ax.plot(
                        kx_component,
                        ky_kz_component,
                        '.', markersize=1, color='yellow'
                    )

        plt.tight_layout()
        plt.show()

    elif mode == 'E vs ky vs kx':
        # Handle 3D point cloud plot with critical point detection
        fig = plt.figure(figsize=(10, 8))
        ax = fig.add_subplot(111, projection='3d')
        colorbar = None  # Reset colorbar

        # Prepare lists to store kx, ky, E_binding, and intensity values
        kx_list = []
        ky_list = []
        E_binding_list = []
        intensity_list = []

        # Loop over all scans
        for i in range(len(data_proc)):
            df = data_proc[i]
            hv = data_attributes[i]['hv']
            polar_angle = data_attributes[i]['polar']
            polar_angle_rad = np.deg2rad(polar_angle)

            # Emission angles (theta) in degrees, convert to radians
            emission_angles = df.columns.values.astype(float)
            theta_rad = np.deg2rad(emission_angles)

            # Kinetic energy values
            E_kinetic_values = df.index.values.astype(float)

            # Compute E_binding_values
            E_binding_values = hv - work_function - E_kinetic_values

            # Compute k_magnitude
            k_magnitude = 0.5123 * np.sqrt(E_kinetic_values)  # Shape (n_Ekinetic,)

            # Compute kx_grid
            kx_grid = k_magnitude[:, np.newaxis] * np.sin(theta_rad[np.newaxis, :]) + x_offset  # Shape (n_Ekinetic, n_theta)

            # Compute ky_grid
            ky_grid = k_magnitude[:, np.newaxis] * np.sin(polar_angle_rad) + y_offset  # Shape (n_Ekinetic, 1)
            ky_grid = np.tile(ky_grid, (1, len(theta_rad)))  # Shape (n_Ekinetic, n_theta)

            # Compute E_binding_grid
            E_binding_grid = E_binding_values[:, np.newaxis]  # Shape (n_Ekinetic, 1)
            E_binding_grid = np.tile(E_binding_grid, (1, len(theta_rad)))  # Shape (n_Ekinetic, n_theta)

            # Intensities
            intensities = df.values  # Shape (n_Ekinetic, n_theta)

            # Apply moving average filter along energy axis
            if kernel_size > 1:
                intensities = np.apply_along_axis(
                    lambda m: moving_average(m, kernel_size),
                    axis=0,
                    arr=intensities
                )

            # Normalize intensities
            max_intensity = np.nanmax(intensities)
            if max_intensity > 0:
                intensities = intensities / max_intensity

            # Flatten the arrays
            kx_flat = kx_grid.flatten()
            ky_flat = ky_grid.flatten()
            E_binding_flat = E_binding_grid.flatten()
            intensities_flat = intensities.flatten()

            # Collect data
            kx_list.append(kx_flat)
            ky_list.append(ky_flat)
            E_binding_list.append(E_binding_flat)
            intensity_list.append(intensities_flat)

        # Concatenate the lists
        kx_array = np.concatenate(kx_list)
        ky_array = np.concatenate(ky_list)
        E_binding_array = np.concatenate(E_binding_list)
        intensity_array = np.concatenate(intensity_list)

        # Apply intensity threshold to remove points below threshold
        intensity_mask = intensity_array >= intensity_threshold
        kx_array = kx_array[intensity_mask]
        ky_array = ky_array[intensity_mask]
        E_binding_array = E_binding_array[intensity_mask]
        intensity_array = intensity_array[intensity_mask]

        # Check if there is data to plot
        if kx_array.size == 0:
            print("No data to plot with the given intensity threshold.")
            return

        # Normalize intensity_array for color mapping
        intensity_array_normalized = intensity_array / intensity_array.max()

        # Create a KDTree for efficient neighborhood queries
        from scipy.spatial import cKDTree

        data_points = np.column_stack((kx_array, ky_array, E_binding_array))
        tree = cKDTree(data_points)

        # Set the radius for local neighborhood (based on bin size)
        kx_range = kx_array.max() - kx_array.min()
        ky_range = ky_array.max() - ky_array.min()
        E_range = E_binding_array.max() - E_binding_array.min()

        # Define the neighborhood radius as a fraction of the data ranges
        radius_kx = kx_range / surface_resolution
        radius_ky = ky_range / surface_resolution
        radius_E = E_range / surface_resolution

        # Maximum radius for neighborhood search
        max_radius = max(radius_kx, radius_ky, radius_E)

        # Only perform critical point detection if enabled
        if enable_critical_points:
            # Initialize lists to store critical points
            critical_kx = []
            critical_ky = []
            critical_E = []

            # Loop over a subset of points for efficiency
            num_points = len(data_points)
            max_points = 50000  # Adjust as needed
            if num_points > max_points:
                indices = np.random.choice(num_points, max_points, replace=False)
                sample_points = data_points[indices]
                sample_intensities = intensity_array_normalized[indices]
            else:
                sample_points = data_points
                sample_intensities = intensity_array_normalized

            # For each sample point, compute the gradient based on neighbors
            for idx, point in enumerate(sample_points):
                # Find neighbors within the radius
                indices = tree.query_ball_point(point, r=max_radius)

                # Ensure sufficient number of neighbors
                if len(indices) < data_density_threshold:
                    continue  # Skip this point due to insufficient data

                # Get neighbor points and intensities
                neighbor_points = data_points[indices]
                neighbor_intensities = intensity_array_normalized[indices]

                # Fit a local plane (first-order polynomial) to the intensities
                A = np.column_stack((
                    neighbor_points[:, 0] - point[0],
                    neighbor_points[:, 1] - point[1],
                    neighbor_points[:, 2] - point[2],
                    np.ones(len(indices))
                ))
                b = neighbor_intensities

                # Use least squares to solve for the gradient components
                try:
                    coeffs, residuals, rank, s = np.linalg.lstsq(A, b, rcond=None)
                    grad_kx, grad_ky, grad_E = coeffs[:3]

                    # Compute gradient magnitude
                    grad_magnitude = np.sqrt(grad_kx**2 + grad_ky**2 + grad_E**2)

                    # Check if gradient magnitude is below threshold
                    if grad_magnitude < gradient_threshold:
                        critical_kx.append(point[0])
                        critical_ky.append(point[1])
                        critical_E.append(point[2])
                except np.linalg.LinAlgError:
                    continue  # Skip if singular matrix encountered

        # Plot the point cloud
        # Optionally downsample for plotting efficiency
        num_points = kx_array.size
        max_points_plot = 100000  # Adjust as needed
        if num_points > max_points_plot:
            # Randomly sample max_points indices
            indices = np.random.choice(num_points, max_points_plot, replace=False)
            kx_array_plot = kx_array[indices]
            ky_array_plot = ky_array[indices]
            E_binding_array_plot = E_binding_array[indices]
            intensity_array_normalized_plot = intensity_array_normalized[indices]
        else:
            kx_array_plot = kx_array
            ky_array_plot = ky_array
            E_binding_array_plot = E_binding_array
            intensity_array_normalized_plot = intensity_array_normalized

        sc = ax.scatter(
            kx_array_plot, ky_array_plot, E_binding_array_plot,
            c=intensity_array_normalized_plot, cmap=rainbowlightct, marker='.', s=1, vmin=0, vmax=1
        )

        if enable_critical_points and len(critical_kx) > 0:
            # Plot critical points as larger pink points
            ax.scatter(
                critical_kx, critical_ky, critical_E,
                color='pink', s=150, label='Critical Points'
            )
            ax.legend(loc='best')  # Add legend to identify critical points

        # Add colorbar
        mappable = plt.cm.ScalarMappable(cmap=rainbowlightct, norm=plt.Normalize(vmin=0, vmax=1))
        mappable.set_array([])
        if colorbar is None:
            colorbar = fig.colorbar(mappable, ax=ax, label='Normalized Intensity')
        else:
            colorbar.update_normal(mappable)

        ax.set_xlabel(r'$k_x$ (Å$^{-1}$)')
        ax.set_ylabel(r'$k_y$ (Å$^{-1}$)')
        ax.set_zlabel('Binding Energy (eV)')
        ax.set_title('3D Scatter Plot: E vs $k_y$ vs $k_x$ with Critical Points')

        plt.tight_layout()
        plt.show()

    else:
        print("Invalid mode selected.")
        return

# Widgets for user interaction
mode_selector = widgets.Dropdown(
    options=['kx vs ky', 'kx vs kz', 'E vs kx', 'E vs ky vs kx'],
    value='kx vs ky',
    description='Select Mode:',
    style={'description_width': 'initial'}
)

# Create a slider for binding energy
E_binding_widget = widgets.FloatSlider(
    value=0.0,
    min=E_binding_min,
    max=E_binding_max,
    step=0.01,
    description='Binding Energy (eV):',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Add a toggle for enabling contour maps
contour_toggle = widgets.Checkbox(
    value=False,
    description='Enable Contour Map',
    style={'description_width': 'initial'}
)

# Add a slider to adjust contour density
contour_density_widget = widgets.IntSlider(
    value=20,
    min=1,
    max=100,
    step=1,
    description='Contour Density:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Widget for adjusting kernel size of moving average filter
kernel_size_widget = widgets.IntSlider(
    value=1,
    min=1,
    max=51,
    step=2,
    description='Kernel Size:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Widgets for adjusting x and y axis offsets
x_offset_widget = widgets.FloatSlider(
    value=0.0,
    min=-5.0,
    max=5.0,
    step=0.01,
    description='X Offset:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

y_offset_widget = widgets.FloatSlider(
    value=0.0,
    min=-5.0,
    max=5.0,
    step=0.01,
    description='Y Offset:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Intensity range is now between 0 and 1 after normalization
vmin_widget = widgets.FloatSlider(
    value=0.0,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Min Intensity:',
    continuous_update=False,
    readout_format='.2f',
    style={'description_width': 'initial'}
)

vmax_widget = widgets.FloatSlider(
    value=1.0,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Max Intensity:',
    continuous_update=False,
    readout_format='.2f',
    style={'description_width': 'initial'}
)

# Intensity threshold specific to the 3D mode
intensity_threshold_widget = widgets.FloatSlider(
    value=0.0,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Intensity Threshold:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Checkbox to enable surface plot
surface_plot_toggle = widgets.Checkbox(
    value=False,
    description='Enable Surface Plot',
    style={'description_width': 'initial'}
)

# Slider for surface resolution
surface_resolution_widget = widgets.IntSlider(
    value=200,
    min=50,
    max=1000,
    step=50,
    description='Surface Resolution:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Widgets for Canny edge detection parameters
sigma_widget = widgets.FloatSlider(
    value=1.0,
    min=0.1,
    max=5.0,
    step=0.1,
    description='Canny Sigma:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

low_threshold_widget = widgets.FloatSlider(
    value=0.1,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Canny Low Threshold:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

high_threshold_widget = widgets.FloatSlider(
    value=0.3,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Canny High Threshold:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Create a slider to select the scan number
scan_selector = widgets.IntSlider(
    value=0,
    min=0,
    max=len(data_proc) - 1,
    step=1,
    description='Scan Number:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Peak detection parameters
peak_threshold_widget = widgets.FloatSlider(
    value=0.5,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Peak Threshold:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

neighborhood_size_widget = widgets.IntSlider(
    value=5,
    min=1,
    max=21,
    step=2,
    description='Neighborhood Size:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

smoothing_sigma_widget = widgets.FloatSlider(
    value=1.0,
    min=0.0,
    max=5.0,
    step=0.1,
    description='Smoothing Sigma:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Euler characteristic calculation threshold
euler_binary_threshold_widget = widgets.FloatSlider(
    value=0.5,
    min=0.0,
    max=1.0,
    step=0.01,
    description='Euler Threshold:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Checkbox to display Euler characteristic points
display_euler_points_toggle = widgets.Checkbox(
    value=False,
    description='Display Euler Points',
    style={'description_width': 'initial'}
)

# Toggle to display components and edges
display_edges_toggle = widgets.Checkbox(
    value=False,
    description='Display Edges',
    style={'description_width': 'initial'}
)

display_components_toggle = widgets.Checkbox(
    value=False,
    description='Display Components',
    style={'description_width': 'initial'}
)
# Add a widget for adjusting the gradient threshold
gradient_threshold_widget = widgets.FloatSlider(
    value=0.01,
    min=0.0,
    max=0.1,
    step=0.001,
    description='Gradient Threshold:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Checkbox to enable critical point detection
enable_critical_points_toggle = widgets.Checkbox(
    value=False,
    description='Enable Critical Point Detection',
    style={'description_width': 'initial'}
)
# Slider for data density threshold
data_density_threshold_widget = widgets.IntSlider(
    value=5,
    min=1,
    max=20,
    step=1,
    description='Data Density Threshold:',
    continuous_update=False,
    style={'description_width': 'initial'}
)

# Update the interactive_output to include the new parameter
out = widgets.interactive_output(
    plot_constant_energy_map,
    {
        'mode': mode_selector,
        'E_binding': E_binding_widget,
        'vmin': vmin_widget,
        'vmax': vmax_widget,
        'use_contours': contour_toggle,
        'contour_levels': contour_density_widget,
        'kernel_size': kernel_size_widget,
        'x_offset': x_offset_widget,
        'y_offset': y_offset_widget,
        'sigma': sigma_widget,
        'low_threshold': low_threshold_widget,
        'high_threshold': high_threshold_widget,
        'display_edges': display_edges_toggle,
        'display_components': display_components_toggle,
        'scan_number': scan_selector,
        'peak_threshold': peak_threshold_widget,
        'neighborhood_size': neighborhood_size_widget,
        'smoothing_sigma': smoothing_sigma_widget,
        'euler_binary_threshold': euler_binary_threshold_widget,
        'display_euler_points': display_euler_points_toggle,
        'intensity_threshold': intensity_threshold_widget,
        'surface_resolution': surface_resolution_widget,
        'gradient_threshold': gradient_threshold_widget,
        'enable_critical_points': enable_critical_points_toggle,
        'data_density_threshold': data_density_threshold_widget  # Include the new parameter
    }
)

# Update the UI to include the new slider
ui = widgets.VBox([
    widgets.HBox([mode_selector, E_binding_widget, scan_selector]),
    widgets.HBox([vmin_widget, vmax_widget, intensity_threshold_widget, kernel_size_widget]),
    widgets.HBox([x_offset_widget, y_offset_widget]),
    widgets.HBox([sigma_widget, low_threshold_widget, high_threshold_widget]),
    widgets.HBox([peak_threshold_widget, neighborhood_size_widget, smoothing_sigma_widget]),
    widgets.HBox([euler_binary_threshold_widget, display_euler_points_toggle]),
    widgets.HBox([contour_toggle, contour_density_widget, surface_resolution_widget]),
    widgets.HBox([gradient_threshold_widget, data_density_threshold_widget, enable_critical_points_toggle]),
    widgets.HBox([display_edges_toggle, display_components_toggle]),
])

display(ui, out)