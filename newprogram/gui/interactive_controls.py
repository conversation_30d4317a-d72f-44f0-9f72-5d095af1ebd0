from ipywidgets import interactive, IntSlider, FloatSlider, Checkbox, VBox, HBox
from ipywidgets import <PERSON><PERSON>

def create_interactive_plot(all_plots, config, arpes_plot_func, edc_plot_func, 
                            filter_func, averaging_func, save_func):
    def update_plot(scan_index, vmin, vmax, use_canny, sigma, low_threshold, high_threshold,
                    enable_averaging, averaging_kernel_size):
        plot_data = all_plots[scan_index - 1]
        data = plot_data['data_values'].copy()
        
        if filter_func:
            data = filter_func(data, use_canny, sigma, low_threshold, high_threshold)
        
        if averaging_func and enable_averaging:
            data = averaging_func(data, averaging_kernel_size)
        
        fig, ax = plt.subplots(figsize=(10, 8))
        
        if arpes_plot_func:
            im = arpes_plot_func(ax, plot_data, vmin, vmax, plt.cm.viridis)
            fig.colorbar(im, ax=ax)
        
        if edc_plot_func:
            edc_plot_func(ax, plot_data, 5, 0.5)  # Example values for n and vertical_offset
        
        plt.tight_layout()
        return fig

    interactive_plot = interactive(
        update_plot,
        scan_index=IntSlider(value=1, min=1, max=len(all_plots), description='Scan Index'),
        vmin=FloatSlider(value=0, description='Min Value'),
        vmax=FloatSlider(value=1, description='Max Value'),
        use_canny=Checkbox(value=False, description='Use Canny Filter'),
        sigma=FloatSlider(value=1.0, min=0.1, max=20.0, description='Canny Sigma'),
        low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, description='Low Threshold'),
        high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, description='High Threshold'),
        enable_averaging=Checkbox(value=False, description='Enable Averaging'),
        averaging_kernel_size=IntSlider(value=3, min=1, max=20, description='Averaging Kernel Size')
    )
    
    save_button = Button(description="Save Plot")
    save_button.on_click(lambda b: save_func(interactive_plot.result, "plot.png") if save_func else None)
    
    return VBox([interactive_plot, HBox([save_button])])
