import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
import matplotlib.collections as mcollections
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize
from IPython.display import display
import tkinter as tk
from tkinter import filedialog
from arpes.load_pxt import read_single_pxt
import warnings
from matplotlib import MatplotlibDeprecationWarning
from skimage.feature import canny
from matplotlib.colors import ListedColormap
from ipywidgets import Dropdown, VBox, Output
import os
import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from ipywidgets import interactive, IntSlider, FloatSlider, Layout, Button, VBox, Checkbox, HBox
from matplotlib.colors import Normalize, ListedColormap, TwoSlopeNorm
from IPython.display import display, clear_output
import tkinter as tk
from tkinter import filedialog
import warnings
from arpes.load_pxt import *
from arpes.io import *
from arpes import *
from arpes.utilities import *
from matplotlib import MatplotlibDeprecationWarning
from lmfit.models import LorentzianModel, LinearModel
from scipy.signal import find_peaks
from skimage.feature import canny
from scipy.ndimage import convolve

%matplotlib widget
# Suppress warnings
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=MatplotlibDeprecationWarning)
if not hasattr(np, 'complex'):
    np.complex = np.complex128


class ARPESAnalysisSuite:
    def __init__(self):
        self.data_files = []
        self.work_function = 4.5
        self.plot_modules = {}
        self.filter_modules = {}
        self.dft_data = []
        self.current_plot = None
        self.current_output = Output()

    def load_data_files(self):
        root = tk.Tk()
        root.withdraw()
        folder_path = filedialog.askdirectory(title="Select Folder Containing Data Files")
        root.destroy()
        if folder_path:
            self.data_files = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.endswith('.pxt')]
        else:
            print("No folder selected.")

    def add_plot_module(self, name, module_class):
        self.plot_modules[name] = module_class

    def add_filter_module(self, name, module):
        self.filter_modules[name] = module

    def run(self):
        if not self.data_files:
            print("No data files loaded. Please load data files first.")
            return
        
        plot_selector = self.create_plot_selector()
        main_interface = VBox([plot_selector, self.current_output])
        display(main_interface)

    def create_plot_selector(self):
        plot_options = list(self.plot_modules.keys())
        plot_dropdown = Dropdown(options=plot_options, description='Plot Type:')
        plot_dropdown.observe(self.on_plot_change, names='value')
        return plot_dropdown

    def on_plot_change(self, change):
        plot_type = change.new
        with self.current_output:
            clear_output(wait=True)
            
            if plot_type in self.plot_modules:
                module_class = self.plot_modules[plot_type]
                module_instance = module_class(self.data_files, self.work_function)
                module_instance.run()


class ARPESPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.interactive_widgets = {}
        self.filter_widgets = {}
        self.dft_data = []
        self.scan_types = self.determine_scan_type()
        self.rainbow_light = self.rainbowlightct()
        self.add_filter_widget('canny_enabled', Checkbox(value=False, description='Canny Edge Detection'))
        self.add_filter_widget('moving_average_enabled', Checkbox(value=False, description='Moving Average'))
        self.preprocess_data()  # Call this before initialize_widgets
        self.initialize_widgets()

    def initialize_widgets(self):
        self.add_interactive_widget('scan_index', IntSlider(
            value=1, min=1, max=len(self.data_files), step=1,
            description='Scan Index', continuous_update=False,
            layout=Layout(width='50%')
        ))
        self.add_interactive_widget('vmin', FloatSlider(
            value=self.min_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Min Value',
            continuous_update=False
        ))
        self.add_interactive_widget('vmax', FloatSlider(
            value=self.max_data_value,
            min=self.min_data_value,
            max=self.max_data_value,
            step=(self.max_data_value - self.min_data_value) / 100,
            description='Max Value',
            continuous_update=False
        ))
        self.add_interactive_widget('scale', FloatSlider(
            value=1.0, min=0.1, max=2.0, step=0.1,
            description='Scale', continuous_update=False
        ))
        self.add_interactive_widget('sigma', FloatSlider(
            value=1.0, min=0.1, max=20.0, step=0.1,
            description='Canny Sigma', continuous_update=False
        ))
        self.add_interactive_widget('low_threshold', FloatSlider(
            value=0.1, min=0.0, max=1.0, step=0.01,
            description='Canny Low Threshold', continuous_update=False
        ))
        self.add_interactive_widget('high_threshold', FloatSlider(
            value=0.2, min=0.0, max=1.0, step=0.01,
            description='Canny High Threshold', continuous_update=False
        ))
        self.add_interactive_widget('moving_average_size', IntSlider(
            value=3, min=3, max=50, step=1,
            description='Moving Average Size', continuous_update=False
        ))

    def rainbowlightct(self):
        # Normalize the RGB values to the range 0-1
        normalized_data = igor_data / 65535.0
        
        # Create the ListedColormap
        return ListedColormap(normalized_data)


    def determine_scan_type(self):
        scan_types = []
        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            if 'polar' in data.attrs:
                scan_types.append(('polar', data.attrs['polar']))
            elif 'hv' in data.attrs:
                scan_types.append(('hv', data.attrs['hv']))
            else:
                scan_types.append(('unknown', None))
        return scan_types

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10
            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })
            self.max_data_value = max(self.max_data_value, np.max(data.values))

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap = self.rainbowlightct(), norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, canny_enabled, sigma, low_threshold, high_threshold, moving_average_enabled, moving_average_size, dft_x_offset, dft_y_offset):
        with self.output:
            clear_output(wait=True)
        
            plot_data = self.all_plots[scan_index - 1]
            data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbowlightct()

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
        display(self.fig)

    def moving_average(self, data, window_size):
        kernel = np.ones((window_size, window_size)) / (window_size ** 2)
        return np.convolve(data.flatten(), kernel.flatten(), mode='same').reshape(data.shape)

    def add_interactive_widget(self, name, widget):
        self.interactive_widgets[name] = widget

    def add_filter_widget(self, name, widget):
        self.filter_widgets[name] = widget

    def create_interface(self):
        interactive_plot = interactive(self.update_plot, **self.interactive_widgets)
        filter_checkboxes = [widget for widget in self.filter_widgets.values()]
        
        choose_dft_button = Button(description="Choose DFT")
        choose_dft_button.on_click(self.choose_dft_files)
        
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)

        output = VBox([
            interactive_plot,
            HBox(filter_checkboxes),
            HBox([save_button, choose_dft_button])
        ])
        return output


    def choose_dft_files(self, b):
        import tkinter as tk
        from tkinter import filedialog

        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data

    def save_plot(self, b):
        current_values = {name: widget.value for name, widget in self.interactive_widgets.items()}
        plot_data = self.all_plots[current_values['scan_index'] - 1]
        current_file = plot_data['file_name']
        save_folder = os.path.dirname(current_file)
        filename = os.path.join(save_folder, f"ARPES_plot_{os.path.basename(current_file)}.png")
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")

    def run(self):
        self.preprocess_data()
        self.create_plot()
        self.output = Output()
        with self.output:
            display(self.fig)
        interface = self.create_interface()
        display(VBox([self.output, interface]))


class KSpacePlotModule(ARPESPlotModule):
    def __init__(self, data_files, work_function):
        self.hbar = 1.054571817e-34  # J*s
        self.m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        super().__init__(data_files, work_function)
        self.preprocess_data()  # Call this again to calculate k-space specific values
        

    def initialize_widgets(self):
        super().initialize_widgets()
        self.add_interactive_widget('dft_x_offset', FloatSlider(
            value=0, min=self.min_k, max=self.max_k,
            step=(self.max_k - self.min_k) / 200,
            description='DFT X Offset', continuous_update=True
        ))
        self.add_interactive_widget('dft_y_offset', FloatSlider(
            value=0, min=self.min_energy, max=self.max_energy,
            step=(self.max_energy - self.min_energy) / 200,
            description='DFT Y Offset', continuous_update=True
        ))


    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg
        self.min_k = float('inf')
        self.max_k = float('-inf')
        self.min_energy = float('inf')
        self.max_energy = float('-inf')
        self.min_data_value = float('inf')
        self.max_data_value = float('-inf')

        for file_path, scan_info in zip(self.data_files, self.scan_types):
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']
            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * self.m_e * E_b) * np.sin(np.radians(data.phi)) / self.hbar) / 10**10
            energy_values = -data.eV.values
            
            self.min_k = min(self.min_k, np.min(k_parallel))
            self.max_k = max(self.max_k, np.max(k_parallel))
            self.min_energy = min(self.min_energy, np.min(energy_values))
            self.max_energy = max(self.max_energy, np.max(energy_values))
            self.min_data_value = min(self.min_data_value, np.min(data.values))
            self.max_data_value = max(self.max_data_value, np.max(data.values))

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': file_path,
                'scan_type': scan_info[0],
                'scan_value': scan_info[1]
            })

    def create_plot(self):
        plot_data = self.all_plots[0]
        norm = Normalize(vmin=0, vmax=self.max_data_value)
        self.im = self.ax.pcolormesh(plot_data['k_parallel'], plot_data['energy_values'], 
                                     plot_data['data_values'], shading='auto', 
                                     cmap=self.rainbow_light, norm=norm)
        self.cbar = self.fig.colorbar(self.im, ax=self.ax)
        self.cbar.set_label('Intensity', fontsize=12, fontweight='bold')
        self.cbar.ax.tick_params(labelsize=10)
        self.ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}', 
                          fontsize=14, fontweight='bold')
        self.ax.set_ylim(plot_data['energy_values'].min(), plot_data['energy_values'].max())
        self.ax.tick_params(axis='both', which='major', labelsize=10)
        plt.tight_layout()

    def update_plot(self, scan_index, vmin, vmax, scale, sigma, low_threshold, high_threshold,
                moving_average_size, dft_x_offset, dft_y_offset):
        canny_enabled = self.filter_widgets['canny_enabled'].value
        moving_average_enabled = self.filter_widgets['moving_average_enabled'].value
        plot_data = self.all_plots[scan_index - 1]
        data_to_plot = plot_data['data_values'].copy()

        if canny_enabled:
            data_to_plot = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)

        if moving_average_enabled:
            data_to_plot = self.moving_average(data_to_plot, moving_average_size)

        if canny_enabled:
            norm = Normalize(vmin=0, vmax=1)
            cmap = plt.cm.gray
        else:
            norm = Normalize(vmin=vmin, vmax=vmax / scale)
            cmap = self.rainbow_light

        self.im.set_array(data_to_plot.ravel())
        self.im.set_norm(norm)
        self.im.set_cmap(cmap)

        if plot_data['scan_type'] == 'polar':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (Polar: {plot_data["scan_value"]:.2f}°)'
        elif plot_data['scan_type'] == 'hv':
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])} (hv: {plot_data["scan_value"]:.2f} eV)'
        else:
            title = f'ARPES Data in Momentum Space - File: {os.path.basename(plot_data["file_name"])}'
        self.ax.set_title(title, fontsize=14, fontweight='bold')

        for artist in self.ax.lines:
            if artist.get_label() == 'DFT':
                artist.remove()

        for data in self.dft_data:
            self.ax.plot(data[:, 0] + dft_x_offset, data[:, 1] + dft_y_offset, 'r:',
                         linewidth=1, alpha=0.7, label='DFT')

        self.fig.canvas.draw_idle()
    def run(self):
        self.preprocess_data()
        self.create_plot()
        interface = self.create_interface()
        display(interface)
        return self




    def choose_dft_files(self, b):
        root = tk.Tk()
        root.withdraw()
        file_paths = filedialog.askopenfilenames(title="Select DFT Files", filetypes=[("All Files", "*.*")])
        root.destroy()

        if file_paths:
            self.dft_data = self.load_dft_data(file_paths)
            self.update_plot(**{name: widget.value for name, widget in self.interactive_widgets.items()})

    def load_dft_data(self, file_paths):
        dft_data = []
        for file_path in file_paths:
            data = np.loadtxt(file_path, delimiter=',', skiprows=1)
            dft_data.append(data)
        return dft_data


class EDCPlotModule:
    def __init__(self, data_files, work_function):
        self.data_files = data_files
        self.work_function = work_function
        self.all_plots = []
        self.global_k_min = float('inf')
        self.global_k_max = float('-inf')
        self.global_e_min = float('inf')
        self.global_e_max = float('-inf')
        self.global_intensity_max = float('-inf')
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        self.preprocess_data()

    def preprocess_data(self):
        hbar = 1.054571817e-34  # J*s
        m_e = 9.1093837015e-31  # kg

        for file_path in self.data_files:
            data = read_single_pxt(file_path)
            E_photon = data.attrs['hv']

            E_b = (self.work_function + np.abs(data.eV) - E_photon) * 1.602176634e-19
            k_parallel = (np.sqrt(-2 * m_e * E_b) * np.sin(np.radians(data.phi)) / hbar) / 10**10

            energy_values = -data.eV.values

            self.all_plots.append({
                'k_parallel': k_parallel,
                'energy_values': energy_values,
                'data_values': data.values,
                'file_name': os.path.basename(file_path)
            })

            self.global_k_min = min(self.global_k_min, np.min(k_parallel))
            self.global_k_max = max(self.global_k_max, np.max(k_parallel))
            self.global_e_min = min(self.global_e_min, np.min(energy_values))
            self.global_e_max = max(self.global_e_max, np.max(energy_values))
            self.global_intensity_max = max(self.global_intensity_max, np.max(data.values))

    def run(self):
        interactive_plot = self.create_interactive_plot()
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)
        output = VBox([interactive_plot, save_button])
        display(output)

    def create_interactive_plot(self):
        return interactive(
            self.plot_edc,
            scan_index=IntSlider(value=1, min=1, max=len(self.data_files), step=1, description='Scan Index', continuous_update=False),
            n=IntSlider(value=5, min=1, max=20, step=1, description='Number of EDCs', continuous_update=True),
            vertical_offset=FloatSlider(value=0.5, min=0.0, max=2, step=0.01, description='Vertical Offset', continuous_update=True),
            show_edc=Checkbox(value=True, description='Show EDCs'),
            show_fit=Checkbox(value=False, description='Show Fits'),
            num_peaks=IntSlider(value=1, min=1, max=5, step=1, description='Number of Peaks', continuous_update=True),
            k_min=FloatSlider(value=self.global_k_min, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_min (Å⁻¹)', continuous_update=True),
            k_max=FloatSlider(value=self.global_k_max, min=self.global_k_min, max=self.global_k_max, step=(self.global_k_max - self.global_k_min)/100, description='k_max (Å⁻¹)', continuous_update=True),
            e_min=FloatSlider(value=self.global_e_min, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_min (eV)', continuous_update=True),
            e_max=FloatSlider(value=self.global_e_max, min=self.global_e_min, max=self.global_e_max, step=(self.global_e_max - self.global_e_min)/100, description='E_max (eV)', continuous_update=True),
            use_canny=Checkbox(value=False, description='Use Canny Filter'),
            sigma=FloatSlider(value=1.0, min=0.1, max=20.0, step=0.1, description='Canny Sigma', continuous_update=False),
            low_threshold=FloatSlider(value=0.1, min=0.0, max=1.0, step=0.05, description='Low Threshold', continuous_update=False),
            high_threshold=FloatSlider(value=0.2, min=0.0, max=1.0, step=0.05, description='High Threshold', continuous_update=False),
            enable_averaging=Checkbox(value=False, description='Enable Moving Average'),
            averaging_kernel_size=IntSlider(value=3, min=3, max=20, step=2, description='Averaging Kernel Size', continuous_update=False)
        )

    def plot_edc(self, scan_index, n, vertical_offset, show_edc, show_fit, num_peaks, k_min, k_max, e_min, e_max, use_canny, sigma, low_threshold, high_threshold, enable_averaging, averaging_kernel_size):
        plot_data = self.all_plots[scan_index - 1]
        self.ax.clear()

        data_to_plot = plot_data['data_values'].copy()
        if use_canny:
            edges = canny(data_to_plot, sigma=sigma, low_threshold=low_threshold, high_threshold=high_threshold)
            data_to_plot = edges.astype(float)

        if enable_averaging:
            averaging_kernel = np.ones((averaging_kernel_size, averaging_kernel_size)) / (averaging_kernel_size ** 2)
            data_to_plot = convolve(data_to_plot, averaging_kernel)

        k_parallel = plot_data['k_parallel'][0]
        valid_indices = np.where((k_parallel >= k_min) & (k_parallel <= k_max))[0]
        k_indices = np.linspace(0, len(valid_indices) - 1, n, dtype=int)
        k_indices = valid_indices[k_indices]

        max_intensity = float('-inf')
        min_intensity = float('inf')

        for i, k_index in enumerate(k_indices):
            actual_k = k_parallel[k_index]
            kdc = data_to_plot[:, k_index]
            energy_values = plot_data['energy_values']

            valid_e_indices = np.where((energy_values >= e_min) & (energy_values <= e_max))[0]
            kdc = kdc[valid_e_indices]
            energy_values = energy_values[valid_e_indices]

            if isinstance(kdc, xr.DataArray):
                kdc = kdc.values
            if isinstance(energy_values, xr.DataArray):
                energy_values = energy_values.values

            if not use_canny:
                kdc = kdc / np.max(kdc)

            offset_kdc = kdc + i * vertical_offset
            max_intensity = max(max_intensity, np.max(offset_kdc))
            min_intensity = min(min_intensity, np.min(offset_kdc))

            if show_edc:
                self.ax.plot(energy_values, offset_kdc, label=fr'$k_\parallel$ = {actual_k:.2f}')

            self.ax.axhline(y=i * vertical_offset, color='gray', linestyle='--', marker='x', markersize=5)

            if show_fit:
                peaks, _ = find_peaks(kdc)
                peak_heights = kdc[peaks]
                sorted_indices = np.argsort(peak_heights)[-num_peaks:]
                largest_peaks = peaks[sorted_indices]

                model = LinearModel(prefix='bkg_')
                params = model.make_params(bkg_slope=0, bkg_intercept=np.min(kdc))

                for j, peak in enumerate(largest_peaks):
                    lorentzian = LorentzianModel(prefix=f'l{j+1}_')
                    model += lorentzian
                    params.update(lorentzian.make_params())
                    params[f'l{j+1}_center'].set(value=energy_values[peak], min=energy_values.min(), max=energy_values.max())
                    params[f'l{j+1}_amplitude'].set(value=peak_heights[sorted_indices[j]], min=0)
                    params[f'l{j+1}_sigma'].set(value=0.1, min=0)

                result = model.fit(kdc, params, x=energy_values)
                fit = result.best_fit
                offset_fit = fit + i * vertical_offset
                self.ax.plot(energy_values, offset_fit, '--', label=fr'Fit $k_\parallel$ = {actual_k:.2f}', color=f'C{i}')

                fit_peaks, _ = find_peaks(fit)
                fit_valleys, _ = find_peaks(-fit)
                self.ax.plot(energy_values[fit_peaks], offset_fit[fit_peaks], 'ro', markersize=4)
                self.ax.plot(energy_values[fit_valleys], offset_fit[fit_valleys], 'go', markersize=4)

        self.ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
        self.ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
        self.ax.set_title(f'EDCs - File: {plot_data["file_name"]}', fontsize=14, fontweight='bold')
        self.ax.legend()
        self.ax.tick_params(axis='both', which='major', labelsize=10)

        self.ax.set_xlim(e_min, e_max)
        y_range = max_intensity - min_intensity
        self.ax.set_ylim(min_intensity - 0.1 * y_range, max_intensity + 0.1 * y_range)

        plt.tight_layout()
        self.fig.canvas.draw_idle()

    def run(self):
        interactive_plot = self.create_interactive_plot()
        save_button = Button(description="Save Plot")
        save_button.on_click(self.save_plot)
        output = VBox([interactive_plot, save_button])
        display(output)
        return self

    def save_plot(self, b):
        current_values = {
            'scan_index': self.interactive_plot.children[0].value,
            'n': self.interactive_plot.children[1].value,
            'vertical_offset': self.interactive_plot.children[2].value,
            'show_edc': self.interactive_plot.children[3].value,
            'show_fit': self.interactive_plot.children[4].value,
            'num_peaks': self.interactive_plot.children[5].value,
            'k_min': self.interactive_plot.children[6].value,
            'k_max': self.interactive_plot.children[7].value,
            'e_min': self.interactive_plot.children[8].value,
            'e_max': self.interactive_plot.children[9].value,
            'use_canny': self.interactive_plot.children[10].value,
            'sigma': self.interactive_plot.children[11].value,
            'low_threshold': self.interactive_plot.children[12].value,
            'high_threshold': self.interactive_plot.children[13].value,
            'enable_averaging': self.interactive_plot.children[14].value,
            'averaging_kernel_size': self.interactive_plot.children[15].value
        }
        self.plot_edc(**current_values)
        filename = f"EDC_plot_scan_{current_values['scan_index']}_n_{current_values['n']}_offset_{current_values['vertical_offset']:.2f}.png"
        self.fig.savefig(filename, dpi=2000, bbox_inches='tight')
        print(f"Plot saved as {filename}")
if __name__ == "__main__":
    suite = ARPESAnalysisSuite()
    suite.load_data_files()
    
    # Add plot modules
    suite.add_plot_module("K-Space Plot", KSpacePlotModule)
    suite.add_plot_module("EDC Plot", EDCPlotModule)
    
    suite.run()