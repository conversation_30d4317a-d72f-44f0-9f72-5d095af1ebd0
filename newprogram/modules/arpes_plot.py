import matplotlib.pyplot as plt
import numpy as np
from matplotlib.colors import Normalize

def plot_arpes(ax, plot_data, vmin, vmax, cmap):
    k_parallel = plot_data['k_parallel']
    energy_values = plot_data['energy_values']
    
    # Ensure k_parallel is 1D
    if k_parallel.ndim > 1:
        k_parallel = k_parallel[0]  # Take the first row if it's 2D
    
    # Calculate cell edges
    k_step = np.mean(np.diff(k_parallel))
    k_edges = np.concatenate([[k_parallel[0] - k_step/2], k_parallel + k_step/2])
    
    e_step = np.mean(np.diff(energy_values))
    e_edges = np.concatenate([[energy_values[0] - e_step/2], energy_values + e_step/2])
    
    im = ax.pcolormesh(k_edges, e_edges, plot_data['data_values'], 
                       shading='flat', cmap=cmap, norm=Normalize(vmin=vmin, vmax=vmax))
    
    ax.set_xlabel(r'$k_\parallel $($\AA^{-1}$)', fontsize=12, fontweight='bold')
    ax.set_ylabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_title(f'ARPES Data - {plot_data["file_name"]}', fontsize=14, fontweight='bold')
    
    return im

