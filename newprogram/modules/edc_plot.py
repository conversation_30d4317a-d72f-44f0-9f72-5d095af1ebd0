import numpy as np
import xarray as xr
import matplotlib.pyplot as plt


def plot_edc(ax, plot_data, n, vertical_offset):
    k_indices = np.linspace(0, len(plot_data['k_parallel']) - 1, n, dtype=int)
    for i, k_index in enumerate(k_indices):
        kdc = plot_data['data_values'][:, k_index] / np.max(plot_data['data_values'][:, k_index])
        offset_kdc = kdc + i * vertical_offset
        k_value = plot_data['k_parallel'][k_index]
        
        # Check if k_value is an array or DataArray and extract a single value if needed
        if isinstance(k_value, (np.ndarray, xr.DataArray)):
            if k_value.size > 1:
                k_value = k_value.mean()  # Use the mean value if there are multiple elements
            else:
                k_value = k_value.item()  # Convert to scalar if it's a single-element array
        
        ax.plot(plot_data['energy_values'], offset_kdc, 
                label=fr'$k_\parallel$ = {k_value:.2f}')
    
    ax.set_xlabel(r'$E-E_f$ (eV)', fontsize=12, fontweight='bold')
    ax.set_ylabel('Intensity (arb. units)', fontsize=12, fontweight='bold')
    ax.set_title(f'EDCs - {plot_data["file_name"]}', fontsize=14, fontweight='bold')
    ax.legend()

